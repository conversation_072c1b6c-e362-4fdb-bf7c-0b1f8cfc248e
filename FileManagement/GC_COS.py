from google.cloud import storage
from AI.GC_Credentials import get_gcs_credentials
import time
import random
import os

def upload_to_gcs(file_path, gcs_bucket_data, destination_blob_name, max_retries=5, initial_delay=1):
    """Uploads a file to Google Cloud Storage with exponential backoff.

    Args:
        file_path: The local path to the file.
        gcs_bucket_data: The GCS bucket object OR the name of the bucket.
        destination_blob_name: The name of the blob in the bucket.
        max_retries: The maximum number of retries.
        initial_delay: The initial delay in seconds.
    """
    if isinstance(gcs_bucket_data, str):
        credentials = get_gcs_credentials()
        client = storage.Client(credentials=credentials)
        gcs_bucket = client.bucket(gcs_bucket_data)
        bucket_name = gcs_bucket_data
    else:
        gcs_bucket = gcs_bucket_data
        bucket_name = gcs_bucket_data.name

    retries = 0
    while retries < max_retries:
        try:
            blob = gcs_bucket.blob(destination_blob_name)
            blob.upload_from_filename(file_path)
            return f"gs://{bucket_name}/{destination_blob_name}"
        except Exception as e:
            if hasattr(e, "code") and (e.code == 502 or e.code == 503 or e.code == 429): # 502 Bad Gateway, 503 Service Unavailable, 429 Too Many Requests
                retries += 1
                delay_time = initial_delay * (2 ** retries) + random.uniform(0, initial_delay)
                print(f"Error uploading {destination_blob_name}: {e.code}")
                print(f"Retrying in {delay_time:.2f} seconds (attempt {retries}/{max_retries})...")
                time.sleep(delay_time)
            else:
                print(f"Error uploading {destination_blob_name}: {e}")
                raise  # Re-raise the exception if it's not a retryable error

    print(f"Failed to upload {destination_blob_name} after {max_retries} retries.")
    return None

def download_from_gcs(gcs_bucket_data, source_blob_name, destination_file_path, max_retries=5, initial_delay=1):
    """Downloads a file from Google Cloud Storage with exponential backoff.

    Args:
        gcs_bucket_data: The GCS bucket object OR the name of the bucket.
        source_blob_name: The name of the blob in the bucket to download.
        destination_file_path: The local path to save the downloaded file.
        max_retries: The maximum number of retries.
        initial_delay: The initial delay in seconds.
    """
    if isinstance(gcs_bucket_data, str):
        credentials = get_gcs_credentials()
        client = storage.Client(credentials=credentials)
        gcs_bucket = client.bucket(gcs_bucket_data)
        bucket_name = gcs_bucket_data # Keep for potential future use if needed
    else:
        gcs_bucket = gcs_bucket_data
        bucket_name = gcs_bucket_data.name # Keep for potential future use

    retries = 0
    while retries < max_retries:
        try:
            blob = gcs_bucket.blob(source_blob_name)
            os.makedirs(os.path.dirname(destination_file_path), exist_ok=True)
            blob.download_to_filename(destination_file_path)
            print(f"Downloaded {source_blob_name} to {destination_file_path}")
            return True
        except Exception as e:
            if hasattr(e, "code") and (e.code == 502 or e.code == 503 or e.code == 429): # Retryable errors
                retries += 1
                delay_time = initial_delay * (2 ** retries) + random.uniform(0, initial_delay)
                print(f"Error downloading {source_blob_name}: {e.code}")
                print(f"Retrying in {delay_time:.2f} seconds (attempt {retries}/{max_retries})...")
                time.sleep(delay_time)
            else:
                print(f"Error downloading {source_blob_name}: {e}")
                raise  # Re-raise the exception if it's not a retryable error

    print(f"Failed to download {source_blob_name} after {max_retries} retries.")
    return False

def delete_from_gcs(gcs_bucket_data, destination_blob_name):
    """Deletes a blob from Google Cloud Storage.

    Args:
        gcs_bucket_data: The GCS bucket object OR the name of the bucket.
        destination_blob_name: The name of the blob in the bucket to delete.
    """
    if isinstance(gcs_bucket_data, str):
        credentials = get_gcs_credentials()
        client = storage.Client(credentials=credentials)
        bucket = client.bucket(gcs_bucket_data)
    else:
        bucket = gcs_bucket_data
    blob = bucket.blob(destination_blob_name)
    blob.delete()
    print(f"Deleted {destination_blob_name} from GCS")