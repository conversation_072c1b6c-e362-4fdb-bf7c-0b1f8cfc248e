import os
from Common.Constants import local_case_folder, nas_case_folder
from FileManagement.SendToNAS import connect_to_nas
import stat

# This function is redundant after the change in PDF file naming
def handle_duplicate_files(directory):
    seen_files = {}
    for filename in os.listdir(directory):
        filepath = os.path.join(directory, filename)
        if os.path.isfile(filepath):
            # Split the filename by underscores and reconstruct without the unique identifier
            parts = filename.split('_')
            if len(parts) >= 7 and parts[5][0] == "d":  # Ensure we have enough parts
                # Reconstruct filename without the unique identifier (part at index 6)
                normalized_name = '_'.join(parts[:5] + parts[6:])
                
                if normalized_name in seen_files:
                    os.remove(filepath)
                else:
                    seen_files[normalized_name] = filepath
        else:
            handle_duplicate_files(filepath)


# This function is redundant after the change in PDF file naming
def handle_duplicate_files_on_nas(remote_directory, sftp):
    seen_files = {}
    
    # List all files in the remote directory
    for entry in sftp.listdir_attr(remote_directory):
        if stat.S_ISREG(entry.st_mode):  # Check if it's a regular file
            filepath = remote_directory + '/' + entry.filename
            filename = entry.filename
            
            # Split the filename by underscores and reconstruct without the unique identifier
            parts = filename.split('_')
            if len(parts) >= 7 and parts[5][0] == "d":  # Ensure we have enough parts
                # Reconstruct filename without the unique identifier (part at index 6)
                normalized_name = '_'.join(parts[:5] + parts[6:])
                
                if normalized_name in seen_files:
                    print(f"Removing duplicate file: {filepath}")
                    try:
                        sftp.remove(filepath)
                    except Exception as e:
                        print(f"Error removing {filepath}: {str(e)}")
                else:
                    seen_files[normalized_name] = filepath
        
        elif stat.S_ISDIR(entry.st_mode):  # If it's a directory, recurse into it
            new_path = remote_directory + '/' + entry.filename
            handle_duplicate_files_on_nas(new_path, sftp)

if __name__ == "__main__":
    # handle_duplicate_files(local_case_folder)
    sftp, transport = connect_to_nas()
    handle_duplicate_files_on_nas(nas_case_folder, sftp)
    transport.close()
    sftp.close()
