import paramiko
import os
import sys
import time
import shutil
import threading
from logdata import log_message
import pandas as pd
import shlex


# Add project root to path to allow importing Common
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from Common.Constants import local_case_folder, nas_case_folder, local_plaintiff_folder, nas_plaintiff_folder, sanitize_name

class NASConnectionBase:
    """
    Base class for NAS connection handling and core SSH operations.
    """
    def __init__(self):
        self.ssh = None
        self._connection_lock = threading.RLock() # Add this line
        self._connect() # Initial connection attempt

    def _connect(self):
        """Establishes a new connection with retry logic"""
        with self._connection_lock: # Acquire lock
            # Check if connection was already established by another thread while this one was waiting for the lock
            if self.ssh and self.ssh.get_transport() and self.ssh.get_transport().is_active():
                log_message("NAS Core: Connection already active (checked inside _connect lock), skipping redundant connect.", level="debug")
                return

            absolute_ssh_key_path = os.path.abspath(os.path.join(".ssh", "id_rsa"))
            # Ensure the .ssh directory exists relative to the workspace root
            ssh_dir = os.path.abspath(".ssh")
            if not os.path.isdir(ssh_dir):
                # Attempt to find .ssh relative to script's grandparent dir if not in workspace root
                script_grandparent_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
                ssh_dir_alt = os.path.join(script_grandparent_dir, ".ssh")
                if os.path.isdir(ssh_dir_alt):
                    absolute_ssh_key_path = os.path.join(ssh_dir_alt, "id_rsa")
                else:
                    # Fallback or raise error if key is truly not found
                    print(f"Warning: .ssh directory not found in workspace root ({ssh_dir}) or project root ({ssh_dir_alt}). Ensure id_rsa exists.")
                    # Use the original path calculation as a last resort
                    absolute_ssh_key_path = os.path.abspath(os.path.join(".ssh", "id_rsa"))

            if not os.path.exists(absolute_ssh_key_path):
                raise FileNotFoundError(f"SSH private key not found at expected location: {absolute_ssh_key_path}")

            # Potentially close existing broken client before creating a new one
            if self.ssh:
                try:
                    self.ssh.close()
                except Exception:
                    pass # Ignore errors closing a potentially broken client
            self.ssh = paramiko.SSHClient() # Create new client instance
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            max_attempts = 5
            for attempt in range(max_attempts):
                try:
                    self.ssh.connect(os.getenv("NAS_HOST"), 22,
                                os.getenv("NAS_USERNAME"),
                                key_filename=absolute_ssh_key_path)
                    print("NAS Core: SSH connection established.")
                    return
                except Exception as e:
                    wait_time = 2 ** attempt
                    log_message(f"NAS Core: SSH connection failed (attempt {attempt+1}/{max_attempts}): {e}. Retrying in {wait_time:.1f}s...", level="warning")
                    if attempt == max_attempts - 1:
                        log_message(f"NAS Core: SSH connection failed after {max_attempts} attempts.", level="error")
                        # Ensure self.ssh is None if all attempts fail
                        if self.ssh:
                            try: self.ssh.close()
                            except: pass
                        self.ssh = None
                        raise ConnectionError(f"Failed to connect to NAS via SSH after {max_attempts} attempts.") from e
                    time.sleep(wait_time)

    def ensure_connection(self):
        """Verifies and re-establishes connection if needed"""

        with self._connection_lock: # Acquire lock
            if not self.ssh or not self.ssh.get_transport() or not self.ssh.get_transport().is_active():
                log_message("NAS Core: Connection lost or inactive, reconnecting...", level="warning")
                self._connect() # _connect will also acquire the RLock

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def close(self):
        """Closes the SSH connection."""
        if self.ssh:
            try:
                self.ssh.close()
                print("NAS Core: SSH connection closed.")
            except Exception as e:
                log_message(f"NAS Core: Error closing SSH connection: {e}", level="warning")
            finally:
                self.ssh = None

    def ssh_execute_command(self, command, expected_exit_codes=[0], suppress_stderr_codes=None):
        """
        Executes a command on the NAS with retries.

        Args:
            command (str): The command to execute.
            expected_exit_codes (list, optional): List of exit codes considered successful. Defaults to [0].
            suppress_stderr_codes (list, optional): List of exit codes for which stderr should NOT be logged,
                                                    even if present. Defaults to None (meaning log all stderr).
        """
        max_retries = 3
        retry_delay = 5  # seconds
        if suppress_stderr_codes is None:
            suppress_stderr_codes = [] # Ensure it's a list

        for attempt in range(max_retries):
            try:
                self.ensure_connection()
                # Execute command with timeout
                stdin, stdout, stderr = self.ssh.exec_command(command, timeout=120)

                # Read stdout *before* checking exit status
                stdout_str = stdout.read().decode(errors='ignore')

                # Get exit status
                exit_status = stdout.channel.recv_exit_status() # Block until command finishes

                # Read stderr
                error_message = stderr.read().decode(errors='ignore') # Original variable name

                # Consolidated log for command execution and result
                # log_message(f"NAS Core: Executing command (attempt {attempt+1}): {command} ====> Exit code {exit_status}", level="debug")

                # Log stdout only on failure
                if exit_status not in expected_exit_codes and stdout_str:
                    log_message(f"NAS Core: Command stdout on failure: {stdout_str[:500]}{'...' if len(stdout_str)>500 else ''}", level="debug")

                # Log stderr if present, adjust level on failure, suppress if requested
                if error_message:
                    # Suppress if exit_status is in suppress_stderr_codes
                    should_log_stderr = exit_status not in suppress_stderr_codes
                    if should_log_stderr:
                        log_level = "warning" if exit_status not in expected_exit_codes else "debug"
                        log_message(f"NAS Core: Command stderr: {error_message[:500]}{'...' if len(error_message)>500 else ''}", level=log_level)

                if exit_status in expected_exit_codes:
                    return stdout_str, exit_status  # Success!

                # Handle unexpected exit codes
                full_error = f"Command failed with exit code {exit_status}. Stderr: {error_message}" # Use error_message
                if attempt < max_retries - 1:
                    log_message(f"🔴 Command failed (attempt {attempt+1}/{max_retries}): {full_error}. Retrying in {retry_delay:.1f} seconds...", level="warning") # Original message format
                    time.sleep(retry_delay)
                else:
                    log_message(f"🔴 Command failed after {max_retries} attempts: {full_error}", level="error") # Original message format
                    raise Exception(f"Command failed after {max_retries} attempts: {full_error}") # Original exception

            except paramiko.ssh_exception.SSHException as e:
                # Handle SSH-specific errors
                log_message(f"🔴 SSH error during command execution (attempt {attempt+1}): {e}", level="warning") # Original message format
                self.close() # Close potentially broken connection
                if attempt < max_retries - 1:
                    log_message(f"NAS Core: Retrying connection and command in {retry_delay:.1f} seconds...", level="warning")
                    time.sleep(retry_delay)
                    # ensure_connection() will handle reconnect on the next loop
                else:
                    log_message(f"🔴 SSH connection failed irrecoverably after {max_retries} attempts during command execution.", level="error") # Original message format
                    raise ConnectionError(f"SSH connection failed after {max_retries} attempts during command execution.") from e

            except TimeoutError: # Specific TimeoutError catch
                 if attempt < max_retries - 1:
                     log_message(f"🔴 Command timed out (attempt {attempt+1}/{max_retries}). Retrying in {retry_delay:.1f} seconds...", level="warning") # Original message format
                     time.sleep(retry_delay)
                 else:
                     log_message(f"🔴 Command timed out after {max_retries} attempts.", level="error") # Original message format
                     raise TimeoutError(f"Command timed out after {max_retries} attempts.")

            except Exception as e:
                # Catch other potential errors
                log_message(f"🔴 Unexpected error during command execution (attempt {attempt + 1}): {type(e).__name__} - {e}", level="error") # Original message format
                if attempt < max_retries - 1:
                    log_message(f"NAS Core: Retrying command in {retry_delay:.1f} seconds...", level="warning")
                    time.sleep(retry_delay)
                else:
                    log_message(f"🔴 Command failed due to unexpected error after {max_retries} attempts.", level="error") # Original message format
                    raise Exception(f"Command failed due to unexpected error after {max_retries} attempts.") from e

    def ssh_exists(self, path):
        """Checks if a file or directory exists on the NAS."""
        try:
            # Use stat command for efficiency and reliability
            command = f'stat "/volume1{path}"'
            # Execute stat, expecting 0 (exists) or 1 (doesn't exist).
            # Suppress stderr logging specifically for exit code 1 (doesn't exist),
            # as this is expected and generates "stat: cannot stat... No such file or directory".
            _, exit_code = self.ssh_execute_command(command, expected_exit_codes=[0, 1], suppress_stderr_codes=[1])
            exists = exit_code == 0
            return exists
        except Exception as e:
            log_message(f"NAS Core: Error checking existence for '{path}': {e}", level="warning")
            return False # Assume not exists on error

    def ssh_remove_file(self, remote_path):
        """Removes a file on the NAS."""
        try:
            log_message(f"NAS Core: Removing remote file: {remote_path}", level="info")
            command = f'rm -f "/volume1{remote_path}"' # Use -f to ignore non-existent files and suppress errors
            self.ssh_execute_command(command, expected_exit_codes=[0]) # Expect 0 even if file didn't exist due to -f
        except Exception as e:
            log_message(f"NAS Core: Error removing remote file '{remote_path}': {e}", level="error")
            raise # Re-raise the exception

    def create_remote_directory(self, path):
        """Creates a directory on the NAS, including parent directories."""
        try:
            if not self.ssh_exists(path):
                # log_message(f"NAS Core: Creating remote directory: {path}", level="info")
                command = f'mkdir -p "/volume1{path}"'
                self.ssh_execute_command(command)
                # Verify creation
                if not self.ssh_exists(path):
                     # Add a small delay and re-check
                     time.sleep(0.5)
                     if not self.ssh_exists(path):
                         raise OSError(f"Failed to create remote directory after command execution: {path}")
            else:
                # log_message(f"NAS Core: Remote directory already exists: {path}", level="debug")
                pass
        except Exception as e:
            log_message(f"NAS Core: Error creating remote directory '{path}': {e}", level="error")
            raise

    def list_remote_directory(self, path):
        """Lists contents (files and directories) of a remote directory."""
        try:
            log_message(f"NAS Core: Listing remote directory: {path}", level="debug")
            # Ensure path exists and is a directory before listing
            if not self.ssh_is_dir(path):
                 log_message(f"NAS Core: Path is not a directory or does not exist: {path}", level="warning")
                 return "" # Return empty string or perhaps raise an error? Empty string for now.

            command = f'ls -1 "/volume1{path}"'
            stdout, exit_code = self.ssh_execute_command(command)
            if exit_code == 0:
                return stdout.strip() # Return stripped stdout
            else:
                # This case should ideally be caught by ssh_is_dir check, but handle defensively
                log_message(f"NAS Core: Failed to list remote directory '{path}' (exit code {exit_code}).", level="warning")
                return ""
        except Exception as e:
            log_message(f"NAS Core: Error listing remote directory '{path}': {e}", level="error")
            raise

    def ssh_is_dir(self, path):
        """Checks if a remote path is a directory."""
        try:
            command = f'test -d "/volume1{path}"'
            _, exit_code = self.ssh_execute_command(command, expected_exit_codes=[0, 1]) # 0=is dir, 1=not dir or not exist
            is_dir = exit_code == 0
            # log_message(f"NAS Core: Check if directory for '{path}': {is_dir}", level="debug")
            return is_dir
        except Exception as e:
            log_message(f"NAS Core: Error checking if directory for '{path}': {e}", level="warning")
            return False # Assume not a directory on error

    def copy_within_nas(self, source_path, destination_path):
        """Copies files/directories from one location to another within the NAS."""
        self.ensure_connection()
        try:
            log_message(f"NAS Core: Copying within NAS: '{source_path}' -> '{destination_path}'", level="info")
            # Ensure source exists
            if not self.ssh_exists(source_path):
                raise FileNotFoundError(f"Source path does not exist on NAS: {source_path}")

            # Create the destination parent directory if it doesn't exist
            dest_parent = os.path.dirname(destination_path)
            if dest_parent and dest_parent != '/': # Avoid trying to create root
                 self.create_remote_directory(dest_parent)

            # Use 'cp -a' for archive mode (preserves permissions, timestamps, recursive)
            # Handle copying a single file vs directory contents
            if self.ssh_is_dir(source_path):
                 # Copy directory contents into destination
                 # Ensure destination exists as a directory
                 self.create_remote_directory(destination_path)
                 # Revert to original pattern: cp -r source/* dest/
                 command = f'cp -r "/volume1{source_path}"/* "/volume1{destination_path}/"'
            else:
                 # Copy a single file
                 command = f'cp -a "/volume1{source_path}" "/volume1{destination_path}"'

            self.ssh_execute_command(command)

            # Basic verification (e.g., check if destination exists) - could be more robust
            if not self.ssh_exists(destination_path):
                 # Add a small delay and re-check
                 time.sleep(0.5)
                 if not self.ssh_exists(destination_path):
                     log_message(f"NAS Core: Verification failed after copy: Destination '{destination_path}' not found.", level="warning")
                     # Decide if this should be an error or just a warning
                     # raise OSError(f"Verification failed after copy: Destination '{destination_path}' not found.")

        except Exception as e:
            log_message(f"NAS Core: Error copying within NAS ('{source_path}' -> '{destination_path}'): {e}", level="error")
            raise

    def download_file_from_nas(self, remote_path, local_path):
        """Downloads a file from NAS to local storage using the configured transfer method."""
        self.ensure_connection()
        try:
            log_message(f"NAS Core: Downloading '{remote_path}' -> '{local_path}'", level="info")
            # Ensure local directory exists
            local_dir = os.path.dirname(local_path)
            if local_dir: # Check if there is a directory part
                os.makedirs(local_dir, exist_ok=True)

            # This method relies on transfer_file_with_scp or transfer_file_with_rsync
            # being available in the final class via mixins.
            if hasattr(self, 'transfer_file_with_scp'):
                 log_message(f"NAS Core: Using scp for download.", level="debug")
                 self.transfer_file_with_scp(
                     local_path=local_path,
                     remote_path=remote_path,
                     to_nas=False
                 )
            elif hasattr(self, 'transfer_file_with_rsync'):
                 log_message(f"NAS Core: Using rsync for download.", level="debug")
                 self.transfer_file_with_rsync(
                     local_path=local_path,
                     remote_path=remote_path,
                     to_nas=False
                 )
            else:
                 raise NotImplementedError("No file transfer method (scp or rsync) available in the class.")

            # Basic verification
            if not os.path.exists(local_path):
                 raise FileNotFoundError(f"Local file not found after download attempt: {local_path}")

            log_message(f"NAS Core: Download completed successfully.", level="info")

        except Exception as e:
            log_message(f"NAS Core: Download failed ('{remote_path}' -> '{local_path}'): {e}", level="error")
            # Attempt to clean up potentially incomplete local file
            if os.path.exists(local_path):
                try:
                    os.remove(local_path)
                    log_message(f"NAS Core: Cleaned up incomplete local file: {local_path}", level="debug")
                except OSError as cleanup_e:
                    log_message(f"NAS Core: Error cleaning up incomplete local file '{local_path}': {cleanup_e}", level="warning")
            raise

    def _zip_remote_folder(self, remote_folder, file_type=None):
        """
        Zips a remote folder on the NAS using 7z.
        Places the zip file in the NAS case folder.
        """
        self.ensure_connection()
        try:
            if not self.ssh_is_dir(remote_folder):
                 raise FileNotFoundError(f"Remote folder to zip does not exist or is not a directory: {remote_folder}")

            base_name = os.path.basename(os.path.normpath(remote_folder))
            zip_file_name = f"{base_name}.zip"
            # Place the zip in the parent directory of the folder being zipped, or nas_case_folder as fallback?
            # Let's place it alongside the folder for simplicity unless it's already in nas_case_folder.
            remote_folder_parent = os.path.dirname(remote_folder)
            if remote_folder_parent and remote_folder_parent != '/':
                remote_zip_path = f"{remote_folder_parent}/{zip_file_name}"
            else:
                 # Fallback to nas_case_folder if parent is root or empty
                 remote_zip_path = f"{nas_case_folder}/{zip_file_name}"


            log_message(f"NAS Core: Zipping remote folder '{remote_folder}' to '{remote_zip_path}'", level="info")

            # Remove existing zip file if it exists
            if self.ssh_exists(remote_zip_path):
                log_message(f"NAS Core: Removing existing remote zip file: {remote_zip_path}", level="debug")
                self.ssh_remove_file(remote_zip_path)

            # Construct the 7z command based on the original logic:
            # cd into the target folder, then add everything (*)
            # Revert to original command logic:
            target_path_7z = f'/volume1{remote_folder}'
            zip_path_7z = f'/volume1{remote_zip_path}'

            # Original pattern: cd into the folder itself, use manual double quotes
            command = f'cd "{target_path_7z}" && 7z a "{zip_path_7z}" "*"' # Use "*" as source

            # Add file type filter if specified (Keep commented out as before unless needed)
            # if file_type:
            #     command += f' "{file_type}"' # Manual double quotes if used

            command += ' -r -x!"@eaDir" -y' # Recursive, exclude Synology folders, assume yes

            self.ssh_execute_command(command)

            # Verify zip file creation
            if not self.ssh_exists(remote_zip_path):
                 time.sleep(1) # Wait a bit for filesystem sync
                 if not self.ssh_exists(remote_zip_path):
                     raise FileNotFoundError(f"Remote zip file not found after zipping command: {remote_zip_path}")

            log_message(f"NAS Core: Remote zipping completed: {remote_zip_path}", level="info")
            return remote_zip_path

        except Exception as e:
            log_message(f"NAS Core: Error zipping remote folder '{remote_folder}': {e}", level="error")
            # Attempt cleanup of potentially incomplete remote zip
            if 'remote_zip_path' in locals() and self.ssh_exists(remote_zip_path):
                try:
                    self.ssh_remove_file(remote_zip_path)
                    log_message(f"NAS Core: Cleaned up incomplete remote zip: {remote_zip_path}", level="debug")
                except Exception as cleanup_e:
                    log_message(f"NAS Core: Error cleaning up incomplete remote zip '{remote_zip_path}': {cleanup_e}", level="warning")
            raise

    def _unzip_local_file(self, local_zip_path, local_extract_folder):
        """Helper method to unzip a local archive file using shutil."""
        try:
            # log_message(f"NAS Core: Unzipping local file '{local_zip_path}' to '{local_extract_folder}'", level="info")
            if not os.path.exists(local_zip_path):
                raise FileNotFoundError(f"Local zip file not found: {local_zip_path}")

            # Ensure the extraction directory exists
            os.makedirs(local_extract_folder, exist_ok=True)

            shutil.unpack_archive(local_zip_path, local_extract_folder)
            # log_message(f"NAS Core: Local unzip completed.", level="info")

        except Exception as e:
            log_message(f"NAS Core: Error unzipping local file '{local_zip_path}': {e}", level="error")
            raise

    def ssh_remove_directory(self, remote_path):
        """Recursively removes a directory on the NAS."""
        try:
            log_message(f"NAS Core: Removing remote directory: {remote_path}", level="info")
            # Check if it exists first to avoid unnecessary commands/errors
            if self.ssh_exists(remote_path):
                 # Use rm -rf for recursive force deletion
                 command = f'rm -rf "/volume1{remote_path}"'
                 self.ssh_execute_command(command)
                 # Verify deletion
                 if self.ssh_exists(remote_path):
                      time.sleep(0.5)
                      if self.ssh_exists(remote_path):
                           log_message(f"NAS Core: Verification failed after remove directory: '{remote_path}' still exists.", level="warning")
                           # raise OSError(f"Failed to remove remote directory: {remote_path}")
            else:
                 log_message(f"NAS Core: Remote directory already absent: {remote_path}", level="debug")

        except Exception as e:
            log_message(f"NAS Core: Error removing remote directory '{remote_path}': {e}", level="error")
            raise

    def ssh_get_folder_files_names(self, remote_path, file_extension=None):
        """
        Lists all files recursively within a remote directory, optionally
        filtered by extension (case-insensitive). Returns full paths.
        """
        try:
            log_message(f"NAS Core: Getting file names in remote folder: {remote_path} (Ext: {file_extension})", level="debug")
            if not self.ssh_is_dir(remote_path):
                 log_message(f"NAS Core: Path is not a directory or does not exist: {remote_path}", level="warning")
                 return "" # Return empty string

            # Use find command for recursive listing
            command = f'find "/volume1{remote_path}" -type f'
            if file_extension:
                # Ensure file_extension doesn't have leading dot if user provides it
                ext_pattern = file_extension.lstrip('*.')
                ext_pattern = ext_pattern.lstrip('.')
                command += f' -iname "*.{ext_pattern}"' # Case-insensitive match

            stdout, exit_code = self.ssh_execute_command(command)

            if exit_code == 0:
                # Remove the /volume1 prefix from the output paths
                volume_prefix = "/volume1"
                full_paths = stdout.strip().split('\n')
                relative_paths = []
                for p in full_paths:
                    if p.startswith(volume_prefix):
                        relative_paths.append(p[len(volume_prefix):])
                    elif p: # Add non-empty paths even if prefix missing (unexpected)
                        relative_paths.append(p)

                log_message(f"NAS Core: Found {len(relative_paths)} files.", level="debug")
                return "\n".join(relative_paths) # Return paths relative to /volume1
            else:
                log_message(f"NAS Core: Failed to get file names in '{remote_path}' (exit code {exit_code}).", level="warning")
                return "" # Return empty on error or no files found

        except Exception as e:
            log_message(f"NAS Core: Error getting file names in '{remote_path}': {e}", level="error")
            raise

    def send_files_to_nas(self, df_with_id, plaintiff_df):
        """
        Main method for sending case files and plaintiff images to NAS.
        Relies on ssh_local_to_nas (Sync Mixin) and copy_within_nas (Core).
        """
        self.ensure_connection()
        transfer_count = 0
        error_count = 0
        total_cases = len(df_with_id)
        # log_message(f"NAS Core: Starting send_files_to_nas for {total_cases} cases.", level="info")

        # Check required methods exist from mixins
        if not hasattr(self, 'ssh_local_to_nas'):
             raise NotImplementedError("send_files_to_nas requires 'ssh_local_to_nas' method (expected from NASSyncMixin).")

        try:
            for counter, (index, row) in enumerate(df_with_id.iterrows()):
                docket = row.get("docket", "UnknownDocket")
                date_filed = row.get("date_filed")
                plaintiff_id = row.get("plaintiff_id")

                # log_message(f'NAS Core ({counter+1}/{total_cases}): Processing case {docket}', level="info")

                if not date_filed or pd.isna(date_filed):
                     log_message(f'NAS Core ({counter+1}/{total_cases}): Skipping case {docket} due to missing/invalid date_filed.', level="warning")
                     error_count += 1
                     continue
                if not plaintiff_id or pd.isna(plaintiff_id):
                     log_message(f'NAS Core ({counter+1}/{total_cases}): Skipping case {docket} due to missing plaintiff_id.', level="warning")
                     error_count += 1
                     continue

                try:
                    # Send the case folder to NAS
                    sanitized_docket = sanitize_name(f'{date_filed.strftime("%Y-%m-%d")} - {docket}')
                    case_directory_nas = f"{nas_case_folder}/{sanitized_docket}"
                    case_directory_local = os.path.join(local_case_folder, sanitized_docket)

                    if not os.path.isdir(case_directory_local):
                         log_message(f'NAS Core ({counter+1}/{total_cases}): Local case directory not found, skipping: {case_directory_local}', level="warning")
                         error_count += 1
                         continue

                    # log_message(f'NAS Core ({counter+1}/{total_cases}): Transferring case folder {sanitized_docket} LOCAL -> NAS', level="debug")
                    # This relies on the NASSyncMixin method
                    self.ssh_local_to_nas(case_directory_local, case_directory_nas)
                    transfer_count += 1

                    # Send the plaintiff picture to NAS
                    local_image_dir = os.path.join(case_directory_local, "images")
                    if os.path.isdir(local_image_dir) and len(os.listdir(local_image_dir)) > 0:
                        plaintiff_rows = plaintiff_df[plaintiff_df['id'] == plaintiff_id]
                        if not plaintiff_rows.empty:
                            plaintiff_name = plaintiff_rows['plaintiff_name'].values[0]
                            if plaintiff_name and pd.notna(plaintiff_name):
                                sanitized_plaintiff_name = sanitize_name(plaintiff_name)
                                case_plaintiff_directory_nas = f"{nas_plaintiff_folder}/{sanitized_plaintiff_name}"
                                log_message(f'NAS Core ({counter+1}/{total_cases}): Copying images to plaintiff folder {sanitized_plaintiff_name} on NAS', level="debug")
                                # Use core method
                                self.copy_within_nas(f"{case_directory_nas}/images", case_plaintiff_directory_nas)
                            else:
                                log_message(f'NAS Core ({counter+1}/{total_cases}): Plaintiff name missing or invalid for ID {plaintiff_id}, cannot copy images.', level="warning")
                        else:
                             log_message(f'NAS Core ({counter+1}/{total_cases}): Plaintiff ID {plaintiff_id} not found in plaintiff_df, cannot copy images.', level="warning")
                    else:
                        log_message(f'NAS Core ({counter+1}/{total_cases}): No images found locally in {local_image_dir} for case {docket}, skipping image copy.', level="debug")

                except Exception as case_e:
                    log_message(f"NAS Core ({counter+1}/{total_cases}): Error processing case {docket}: {case_e}", level="error")
                    error_count += 1
                    # Continue to the next case

            # log_message(f"NAS Core: send_files_to_nas finished. Cases processed: {total_cases}, Transfers attempted: {transfer_count}, Errors: {error_count}", level="info")

        except Exception as e:
            log_message(f"NAS Core: Unhandled error in send_files_to_nas: {e}", level="critical")
            raise # Re-raise the exception after logging
        
        