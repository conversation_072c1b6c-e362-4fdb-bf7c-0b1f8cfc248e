import os
import sys
from dotenv import load_dotenv

sys.path.append(os.getcwd())

# Import Base and Mixins from the sub-package using relative imports
from FileManagement.NAS_module.core import NASConnectionBase
from FileManagement.NAS_module.sync import NASSyncMixin
from FileManagement.NAS_module.transfer import NASTransferMixin

# Imports needed specifically for the __main__ block (if any beyond pandas/load_dotenv)
from DatabaseManagement.ImportExport import get_table_from_GZ
from Common.Constants import local_case_folder, nas_case_folder # Add others if needed by __main__
from logdata import log_message # Assuming logdata is importable from project root


class NASConnection(NASConnectionBase, NASSyncMixin, NASTransferMixin):
    """
    Main NAS Connection class combining core, sync, and rsync functionalities.
    Inherits implementation from base and mixin classes located in the NAS sub-package.

    Core methods (from NAS.core):
    - __init__, _connect, ensure_connection, __enter__, __exit__, close
    - ssh_execute_command, ssh_exists, ssh_remove_file, create_remote_directory
    - list_remote_directory, ssh_is_dir, copy_within_nas, download_file_from_nas
    - _zip_remote_folder, _unzip_local_file, ssh_remove_directory
    - ssh_get_folder_files_names, send_files_to_nas

    Sync methods (from NAS.sync):
    - sync_local_to_nas, sync_nas_to_local
    - sync_local_to_nas_test, sync_nas_to_local_test
    - _count_pdfs_local, _count_pdfs_remote, list_remote_directories
    - _get_nas_pdf_counts_by_folder, _get_local_pdf_counts_by_folder
    - ssh_local_to_nas, ssh_nas_to_local, ssh_nas_to_local_aggregated

    Rsync/SCP methods (from NAS.rsync):
    - transfer_file_with_rsync, transfer_file_with_scp, test_transfer_speed
    """
    def __init__(self):
        """Initializes the NAS connection by calling the base class constructor."""
        super().__init__() # Call __init__ of the first base class (NASConnectionBase)

    # No other methods needed here unless overriding or adding new combined functionality.
    pass


# Keep the original __main__ block for testing, but update imports and add checks
if __name__ == "__main__":
    print("Running NASConnection main block...")
    load_dotenv()
    
    # --- Execute Tests ---
    nas_instance = None
    try:
        df = get_table_from_GZ("tb_case", force_refresh=False)
        print("\nAttempting to establish NAS connection...")
        # Use context manager to ensure connection is closed
        with NASConnection() as nas_instance:
            print("✅ NAS connection established successfully.")

            # --- Speed Test ---
            print("\n--- Running test_transfer_speed ---")
            try:
                # test_transfer_speed is now part of the NASTransferMixin
                nas_instance.test_transfer_speed(df, ["25-cv-60315"])
                print("--- test_transfer_speed finished ---")
            except Exception as speed_test_e:
                print(f"🔴 Error during test_transfer_speed: {speed_test_e}")


            # --- Sync Tests ---
            print("\n--- Running sync_local_to_nas_test ---")
            try:
                # sync_local_to_nas_test is now part of the NASSyncMixin
                nas_instance.sync_local_to_nas_test()
                print("--- sync_local_to_nas_test finished ---")
            except Exception as sync_e:
                 print(f"🔴 Error during sync_local_to_nas_test: {sync_e}")


            print("\n--- Running sync_nas_to_local_test ---")
            try:
                # sync_nas_to_local_test is now part of the NASSyncMixin
                nas_instance.sync_nas_to_local_test()
                print("--- sync_nas_to_local_test finished ---")
            except Exception as sync_e:
                 print(f"🔴 Error during sync_nas_to_local_test: {sync_e}")

        print("\nNAS connection closed.")

    except FileNotFoundError as e:
         print(f"🔴 Connection Error: {e}")
         print("   Ensure SSH key exists and paths are correct.")
    except ConnectionError as e:
         print(f"🔴 Connection Error: Failed to connect to NAS via SSH.")
         print(f"   Details: {e}")
         print("   Verify NAS_HOST, NAS_USERNAME, network connectivity, and SSH key permissions.")
    except Exception as e:
         print(f"🔴 An unexpected error occurred: {type(e).__name__} - {e}")
         import traceback
         traceback.print_exc() # Print full traceback for unexpected errors
    finally:
        # Explicitly close if context manager wasn't reached or failed
        if nas_instance and nas_instance.ssh:
            print("Attempting final cleanup of NAS connection...")
            nas_instance.close()

    print("\nNASConnection main block finished.")
