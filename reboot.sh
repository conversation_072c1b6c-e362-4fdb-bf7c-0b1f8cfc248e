#!/usr/bin/env bash
set -euo pipefail

echo "Restarting cloudflared..."
# Using systemctl is the modern way, but vast.ai does not have systemctl => we use service....
service cloudflared restart

echo "Switching to /workspace..."
cd /workspace

# convert CRLF→LF if needed (no-op when already Unix)
if grep -q $'\r' .env; then
  echo "[onstart] Converting .env from CRLF to LF"
  sed -i 's/\r$//' .env
fi

echo "Loading .env into current shell..."
set -a
. .env
set +a

echo "Syncing variables to /etc/environment (idempotent) so that it is available to every shell..."
while IFS='=' read -r k v; do
  if grep -q "^${k}=" /etc/environment; then
      sed -i "s|^${k}=.*|${k}=${v}|" /etc/environment
  else
      echo "${k}=${v}" >> /etc/environment
  fi
done < <(grep -Ev '^\s*($|#)' .env)

echo "Setting up cf-ddns cron job..."
chmod +x /workspace/cf-ddns.sh
echo "*/5 * * * * root /workspace/cf-ddns.sh >> /var/log/cf-ddns.log 2>&1" > /etc/cron.d/cf-ddns
service cron restart

echo "Starting app in tmux detached session 'apistudio', use 'tmux attach -t apistudio'  to reattach..."
tmux new-session -d -s apistudio 'cd /workspace && exec /venv/main/bin/python app_apistudio.py'

echo "Ensuring Node Exporter is installed and running..."
# --- Node Exporter Setup ---
NODE_EXPORTER_BINARY="/usr/local/bin/node_exporter"
NODE_EXPORTER_VERSION="1.9.1" # Specify a version for consistency
NODE_EXPORTER_URL="https://github.com/prometheus/node_exporter/releases/download/v${NODE_EXPORTER_VERSION}/node_exporter-${NODE_EXPORTER_VERSION}.linux-amd64.tar.gz"
TMUX_SESSION_NAME="node_exporter"

# 1. Install Node Exporter if not present (idempotent)
if [ ! -f "${NODE_EXPORTER_BINARY}" ]; then
    echo "Node Exporter not found. Installing v${NODE_EXPORTER_VERSION}..."
    # Use a temporary directory for clean download and extraction
    TEMP_DIR=$(mktemp -d)
    curl -sL "${NODE_EXPORTER_URL}" | tar -xz -C "${TEMP_DIR}" --strip-components=1
    mv "${TEMP_DIR}/node_exporter" "${NODE_EXPORTER_BINARY}"
    rm -rf "${TEMP_DIR}"
    echo "Node Exporter installed successfully to ${NODE_EXPORTER_BINARY}"
else
    echo "Node Exporter is already installed."
fi

# 2. Start Node Exporter in a tmux session if not already running (idempotent)
if ! tmux has-session -t "${TMUX_SESSION_NAME}" 2>/dev/null; then
    echo "Starting Node Exporter in tmux session '${TMUX_SESSION_NAME}'..."
    tmux new-session -d -s "${TMUX_SESSION_NAME}" "${NODE_EXPORTER_BINARY}"
else
    echo "Node Exporter is already running in tmux session '${TMUX_SESSION_NAME}'."
fi