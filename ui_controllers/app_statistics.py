import pandas as pd
import json
import zlib
import base64
from datetime import datetime, timedelta
from flask import request, jsonify, render_template
from auth_decorators import login_required
from logdata import log_message
from DatabaseManagement.StatisticsDB import (
    init_statistics_table, 
    get_latest_statistics, 
    get_statistics_history, 
    get_metric_trend,
    store_statistics_batch
)
from Statistics.StatisticsCollector import collect_all_statistics
from DatabaseManagement.ImportExport import get_table_from_GZ
from DatabaseManagement.Connections import get_gz_connection
from FileManagement.Tencent_COS import list_all_files_in_COS

def init_statistics_routes(app):
    """Initialize routes related to statistics."""

    @app.route('/statistics')
    @login_required
    def statistics_page():
        """Render the statistics page."""
        try:
            # Ensure statistics table exists
            init_statistics_table()
            return render_template('statistics.html')
        except Exception as e:
            log_message(f"Error loading statistics page: {str(e)}", level='ERROR')
            return render_template('statistics.html', error=str(e))

    @app.route('/api/statistics/latest', methods=['GET'])
    @login_required
    def get_latest_statistics_api():
        """Get the latest statistics for all metrics."""
        try:
            df = get_latest_statistics()
            
            # Convert to dictionary format for JSON response
            stats = {}
            for _, row in df.iterrows():
                stats[row['metric_name']] = {
                    'value': int(row['metric_value']),
                    'timestamp': row['report_timestamp'].isoformat() if pd.notna(row['report_timestamp']) else None,
                    'details_preview': row['details_preview'] # Already a dict or None
                }
            
            return jsonify({'success': True, 'data': stats})
            
        except Exception as e:
            log_message(f"Error getting latest statistics: {str(e)}", level='ERROR')
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/statistics/history', methods=['GET'])
    @login_required
    def get_statistics_history_api():
        """Get statistics history with optional filtering."""
        try:
            metric_name = request.args.get('metric_name')
            days_back = int(request.args.get('days_back', 30))
            
            df = get_statistics_history(metric_name=metric_name, days_back=days_back)
            
            # Convert to list of dictionaries for JSON response
            history = []
            for _, row in df.iterrows():
                history.append({
                    'timestamp': row['report_timestamp'].isoformat(),
                    'metric_name': row['metric_name'],
                    'metric_value': int(row['metric_value']),
                    'details_preview': row['details_preview'] # Already a dict or None
                })
            
            return jsonify({'success': True, 'data': history})
            
        except Exception as e:
            log_message(f"Error getting statistics history: {str(e)}", level='ERROR')
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/statistics/trend/<metric_name>', methods=['GET'])
    @login_required
    def get_metric_trend_api(metric_name):
        """Get trend data for a specific metric."""
        try:
            days_back = int(request.args.get('days_back', 30))
            
            df = get_metric_trend(metric_name, days_back=days_back)
            
            # Convert to list for D3.js consumption
            trend_data = []
            for _, row in df.iterrows():
                trend_data.append({
                    'timestamp': row['report_timestamp'].isoformat(),
                    'value': int(row['metric_value'])
                })
            
            return jsonify({'success': True, 'data': trend_data})
            
        except Exception as e:
            log_message(f"Error getting metric trend for {metric_name}: {str(e)}", level='ERROR')
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/statistics/details/<metric_name>', methods=['GET'])
    @login_required
    def get_metric_details_api(metric_name):
        """Get detailed breakdown for a specific metric with fresh data."""
        try:
            # Get fresh data from the source based on metric type
            details = get_fresh_metric_details(metric_name)
            
            return jsonify({'success': True, 'data': details})
            
        except Exception as e:
            log_message(f"Error getting metric details for {metric_name}: {str(e)}", level='ERROR')
            return jsonify({'success': False, 'error': str(e)}), 500

    @app.route('/api/statistics/collect', methods=['POST'])
    @login_required
    def collect_statistics_api():
        """Manually trigger statistics collection."""
        try:
            # Collect all statistics
            statistics_data = collect_all_statistics()
            
            # Store in database
            store_statistics_batch(statistics_data)
            
            return jsonify({
                'success': True, 
                'message': f'Successfully collected and stored {len(statistics_data)} metrics'
            })
            
        except Exception as e:
            log_message(f"Error collecting statistics: {str(e)}", level='ERROR')
            return jsonify({'success': False, 'error': str(e)}), 500

    return app

def get_fresh_metric_details(metric_name):
    """
    Get fresh detailed data for a specific metric by querying the source.
    
    Args:
        metric_name: Name of the metric to get details for
        
    Returns:
        Dictionary with detailed breakdown
    """
    try:
        if metric_name == 'cases_without_cause':
            df_cases = get_table_from_GZ("tb_case", force_refresh=True)
            df_without_cause = df_cases[df_cases["cause"].isna()]
            return {
                'items': [
                    {
                        'case_id': int(row['id']),
                        'docket_number': row['docket'],
                        'date_filed': row['date_filed'].strftime('%Y-%m-%d') if pd.notna(row['date_filed']) else None,
                        'court': row['court'],
                        'link': f'/visualizer?case_id={row["id"]}'
                    }
                    for _, row in df_without_cause.iterrows()
                ],
                'total': len(df_without_cause)
            }
            
        elif metric_name == 'duplicate_plaintiffs':
            df_plaintiffs = get_table_from_GZ("tb_plaintiff", force_refresh=True)
            df_duplicates = df_plaintiffs[df_plaintiffs["plaintiff_name"].str.lower().duplicated(keep='first')]
            return {
                'items': [
                    {
                        'plaintiff_id': int(row['id']),
                        'plaintiff_name': row['plaintiff_name'],
                        'link': f'/plaintiffs?plaintiff_id={row["id"]}'
                    }
                    for _, row in df_duplicates.iterrows()
                ],
                'total': len(df_duplicates)
            }
            
        elif metric_name == 'pictures_in_cos_not_in_dataframe':
            # Get pictures from database
            conn = get_gz_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT id, plaintiff_id, images FROM tb_case")
            data = cursor.fetchall()
            df_cases = pd.DataFrame(data, columns=[desc[0] for desc in cursor.description])
            
            full_list_of_pictures = set()
            full_list_of_certificates = set()
            
            for index, row in df_cases.iterrows():
                try:
                    plaintiff_id = row["plaintiff_id"]
                    images = row['images']
                    decoded_images = json.loads(zlib.decompress(base64.b64decode(images)).decode('utf-8'))
                    
                    for ip in ["patents", "trademarks", "copyrights"]:
                        if ip in decoded_images.keys():
                            for key, value in decoded_images[ip].items():
                                full_list_of_pictures.add((plaintiff_id, key))
                                if value.get("full_filename"):
                                    for picture in value["full_filename"]:
                                        full_list_of_certificates.add((plaintiff_id, picture))
                except Exception:
                    continue
            
            # Get COS files
            all_cos_files = list_all_files_in_COS("plaintiff_images/")
            
            orphaned_files = []
            for file in all_cos_files:
                filename = file.split("/")[-1]
                plaintiff_id_str = file.split("/")[1]
                try:
                    plaintiff_id = int(plaintiff_id_str)
                    if (plaintiff_id, filename) not in full_list_of_pictures and (plaintiff_id, filename) not in full_list_of_certificates:
                        orphaned_files.append({
                            'plaintiff_id': plaintiff_id,
                            'filename': filename,
                            'cos_path': file,
                            'link': f'/plaintiffs?plaintiff_id={plaintiff_id}'
                        })
                except (ValueError, IndexError):
                    continue
            
            cursor.close()
            conn.close()
            
            return {
                'items': orphaned_files,
                'total': len(orphaned_files)
            }
            
        elif metric_name == 'cases_with_corrupted_image_data':
            conn = get_gz_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT id, docket, date_filed, images FROM tb_case")
            data = cursor.fetchall()
            df_cases = pd.DataFrame(data, columns=[desc[0] for desc in cursor.description])
            
            corrupted_cases = []
            for index, row in df_cases.iterrows():
                try:
                    images = row['images']
                    decoded_images = json.loads(zlib.decompress(base64.b64decode(images)).decode('utf-8'))
                except (json.JSONDecodeError, Exception):
                    corrupted_cases.append({
                        'case_id': int(row['id']),
                        'docket_number': row['docket'],
                        'date_filed': row['date_filed'].strftime('%Y-%m-%d') if pd.notna(row['date_filed']) else None,
                        'link': f'/visualizer?case_id={row["id"]}'
                    })
            
            cursor.close()
            conn.close()
            
            return {
                'items': corrupted_cases,
                'total': len(corrupted_cases)
            }
            
        # Add more metric detail handlers as needed
        else:
            return {
                'items': [],
                'total': 0,
                'message': f'Detail view not implemented for {metric_name}'
            }
            
    except Exception as e:
        log_message(f"Error getting fresh details for {metric_name}: {str(e)}", level='ERROR')
        raise
