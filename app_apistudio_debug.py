from datetime import datetime, timedelta
import time, json, zlib, asyncio, os, copy, sys, io, base64, langfuse, contextlib, re
import pandas as pd
from flask import Flask, render_template, request, jsonify
from concurrent.futures import ThreadPoolExecutor
from threading import Lock
from collections import OrderedDict
from langfuse import observe
from langfuse.api.client import FernLangfuse

from Check.Do_Check import download_and_check
from DatabaseManagement.ImportExport import insert_and_update_df_to_GZ_batch, get_gz_connection
from Qdrant.api.utils.db import get_cached_api_keys, refresh_api_keys_cache

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗

# Load API keys from database with caching
allowed_api_keys = get_cached_api_keys()
print(f"✅ Loaded {len(allowed_api_keys)} API keys from database")

# Create Flask app for standalone mode
standalone_app = Flask(__name__)

# Shared data structures for tracking tasks
executor = ThreadPoolExecutor(max_workers=4)
futures = OrderedDict()
queue_timestamps = {}
currently_processing = set()

api_key_usage = {}
api_key_lock = Lock()

# Lock for siglip embeddings to prevent multiple concurrent GPU calls
siglip_lock = Lock()

# Check API project
os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-bac4b191-590d-4059-89d6-e5ff73e91b48"


def ansi_to_html(text):
    """Convert ANSI color codes to more readable format with color circle emojis for Langfuse UI"""
    # Define color mappings from ANSI to color circle emojis
    # Format: ANSI code -> (start_emoji, end_emoji)
    ansi_to_emoji = {
        '\033[0m': '',  # Reset - no end emoji needed
        '\033[30m': '⚫ ',  # Black
        '\033[31m': '🔴 ',  # Red
        '\033[32m': '🟢 ',  # Green
        '\033[33m': '🟠 ',  # Orange
        '\033[34m': '🔵 ',  # Blue
        '\033[35m': '🟣 ',  # Purple
        '\033[36m': '🟢🔵 ',  # Cyan (using green+blue)
        '\033[37m': '⚪ ',  # White
        '\033[90m': '⚫ ',  # Gray (using black)
        '\033[91m': '🔴 ',  # Light Red
        '\033[92m': '🟢 ',  # Light Green
        '\033[93m': '🟡 ',  # Light Yellow
        '\033[94m': '🔵 ',  # Light Blue
        '\033[95m': '🟣 ',  # Light Purple
        '\033[96m': '🟢🔵 ',  # Light Cyan (using green+blue)
        '\033[97m': '⚪ ',  # Light White
    }
    
    result = []
    
    # Split the text by ANSI codes
    segments = re.split(r'(\033\[[0-9;]*m)', text)
    
    for segment in segments:
        if segment.startswith('\033['):
            result.append(ansi_to_emoji.get(segment, ''))
        else:
            # This is text content
            result.append(segment)
    
    # Handle any remaining ANSI codes with a regex
    result_text = ''.join(result)
    
    return result_text



# Custom context manager to capture stdout while still printing to console
@contextlib.contextmanager
def capture_stdout():
    """Context manager to capture stdout and return it as a string while still printing to console."""
    captured_output = io.StringIO()
    original_stdout = sys.stdout
    
    # Create a custom stdout that writes to both the original stdout and our capture buffer
    class TeeOutput:
        def write(self, data):
            captured_output.write(data)
            original_stdout.write(data)
            
        def flush(self):
            captured_output.flush()
            original_stdout.flush()
    
    sys.stdout = TeeOutput()
    try:
        yield captured_output
    finally:
        sys.stdout = original_stdout

def refresh_api_keys():
    try:
        with api_key_lock:
            # Refresh from database cache
            new_keys = refresh_api_keys_cache()

            # Preserve existing usage data for keys that still exist
            global allowed_api_keys, api_key_usage
            allowed_api_keys = new_keys
            api_key_usage = {
                key: api_key_usage.get(key, {'last_reset': 0, 'count': 0, 'daily_count': 0, 'daily_window_start': 0})
                for key in new_keys
            }

        return jsonify({'status': 'success', 'keys_loaded': len(new_keys)})
    except Exception as e:
        return jsonify({'error': str(e)}), 500
    

# Function to check rate limit
def check_rate_limit(api_key):
    """Checks and updates rate limits with daily rolling window"""
    current_time = time.time()
    
    with api_key_lock:  # Acquire lock for thread safety
        usage = api_key_usage.get(api_key)
        config = allowed_api_keys.get(api_key, {})

        if not config:
            return False
        if not usage:
            usage = {'daily_count': 0, 'daily_window_start': current_time, 'last_reset': current_time, 'count': 0 }
            api_key_usage[api_key] = usage
        
        # Daily rolling window check
        if (current_time - usage['daily_window_start']) > 86400:
            usage['daily_count'] = 0
            usage['daily_window_start'] = current_time
            
        if usage['daily_count'] >= config.get('daily_limit', 100):
            return False

        # Minute-based rate limit
        if (current_time - usage['last_reset']) > 60:
            usage['last_reset'] = current_time
            usage['count'] = 0
            
        if usage['count'] >= config.get('rate_limit', 1):
            return False
        
        # Update counts
        usage['count'] += 1
        usage['daily_count'] += 1
        
    return True  # Lock released here when exiting 'with' block

# @observe
def process_check_with_db(check_id, client_id, client_name, db_future=None, **kwargs):
    captured_output = None
    
    try:
        # Capture all stdout during the entire execution
        with capture_stdout() as captured_output:
            # Run the analysis
            print(f"📥 Starting download_and_check for check_id: {check_id}")
            
            # kwargs['langfuse_trace_id'] = langfuse.get_client().get_current_trace_id()  # Only needed if we have @observe decorator
            # print(f"📊 Langfuse trace_id for check_id {check_id}: {langfuse.get_client().get_current_trace_id()}")
            json_results = asyncio.run(download_and_check(check_id=check_id, client_id=client_id, **kwargs))
            
            # Store result in database
            print(f"📤 process_check_with_db: Storing result in database for check_id: {check_id}")
            
            db_result_data = {
                'check_id': int(check_id),
                'check_date': datetime.now().date(),
                'result': json.dumps(json_results)  # Serialize JSON results to string
            }
            db_result_df = pd.DataFrame([db_result_data])

            if client_name == "MiniApp" or client_name == "H5":
                insert_and_update_df_to_GZ_batch(db_result_df, 'tb_case_check_result', 'check_id')
            elif client_name == "MiniAppDev" or client_name == "H5Dev":
                connection = get_gz_connection(host="maidalv.com", port=3307)
                insert_and_update_df_to_GZ_batch(db_result_df, 'tb_case_check_result', 'check_id', conn=connection)
            else:
                insert_and_update_df_to_GZ_batch(db_result_df, 'tb_case_check_result_api', 'check_id')
                
                
            # If there's a db_future, wait for it to complete
            if db_future:
                try:
                    db_future.result()  # This will raise any exceptions that occurred
                    print(f"📤 Check_API : Inserted check_id: {check_id} in Database")
                except Exception as e:
                    print(f"❌ Database insertion failed for check_id: {check_id}: {str(e)}")
                    with langfuse.get_client().start_as_current_span(name="DB Querry Insersion Error",  trace_context={"trace_id": kwargs['langfuse_trace_id']}) as log_span:
                        log_span.update(input={"Database Error": str(e)})
            
            return json_results
    except Exception as e:
        error_msg = f"Error processing check {check_id}: {str(e)}"
        print(f"🔥 {error_msg}")
        
        if captured_output:
            log_output = captured_output.getvalue()
            with langfuse.get_client().start_as_current_span(name="Error Logs",  trace_context={"trace_id": kwargs['langfuse_trace_id']}) as log_span:
                log_span.update(input=ansi_to_html(log_output), output={ "error": f"🔥🔥🔥 {error_msg}" })
        else:
            with langfuse.get_client().start_as_current_span(name="Error Logs",  trace_context={"trace_id": kwargs['langfuse_trace_id']}) as log_span:
                log_span.update(output={ "error": f"🔥🔥🔥 {error_msg}"})
        
        raise
    finally:
        # Get captured logs if available
        if captured_output:
            log_output = captured_output.getvalue()
            
            # Send logs to Langfuse
            with langfuse.get_client().start_as_current_span(name="Success Logs",  trace_context={"trace_id": kwargs['langfuse_trace_id']}) as log_span:
                log_span.update(input=ansi_to_html(log_output), output="success")

        if 'connection' in locals():
            connection.close()

def check_status(check_id):
    # Check if task is being tracked in futures
    is_tracked = check_id in futures
    trace_id = langfuse.get_client().create_trace_id(seed=check_id)  # Create a trace ID for this check_id
    
    # If task is tracked and still processing
    if is_tracked and not futures[check_id].done():
        # Calculate queue position based on submission time
        queue_position = 1  # Start at 1 (this task itself)
        
        # If this task is being processed, it's at position 0
        if check_id in currently_processing:
            queue_position = 0
        else:
            # Count how many tasks were submitted before this one and are still pending
            current_time = queue_timestamps.get(check_id, time.time())
            for task_id, timestamp in queue_timestamps.items():
                if task_id != check_id and timestamp < current_time and not futures[task_id].done():
                    queue_position += 1
        
        # Estimate time based on queue position
        estimated_time = max(1, queue_position)  # At least 1 minute
        
        return jsonify({'status': 'processing', 'queue_position': queue_position, 'estimated_completion_time': f'{estimated_time} minutes'})
    
    # For completed tasks or unknown tasks, check the database
    print(f"Checking database for results for check_id: {check_id}")
    connection = get_gz_connection()
    try:
        # Use a cursor and fetchall, similar to ImportExport.py
        cursor = connection.cursor()
        query = f"SELECT result FROM tb_case_check_result_api WHERE check_id = %s"  # Use parameterized query
        cursor.execute(query, (check_id,))  # Pass check_id as a parameter
        data = cursor.fetchall()

        if data:  # Check if data was returned (fetchall returns an empty list if no rows match)
            print(f"✅✅✅ Results are sent to client for check_id: {check_id}")
            # Extract the 'result' from the first row (data[0]) and first column (data[0][0])
            result_json = data[0][0]
            
            # If this task is still in futures, clean it up
            if is_tracked and futures[check_id].done():
                del futures[check_id]
                if check_id in queue_timestamps:
                    del queue_timestamps[check_id]
            
            # Update the Langfuse trace with result info when client receives the result
            try: 
                with langfuse.get_client().start_as_current_span(name="Results Fetched and Found", trace_context={"trace_id": trace_id}):
                    langfuse.get_client().update_current_span(output={ "✅✅✅ Results are sent to client at": datetime.now().isoformat() })
                print(f"📊 Updated Langfuse trace with result info for check_id: {check_id}")
            except Exception as trace_error:
                print(f"⚠️ Failed to update Langfuse trace for check_id: {check_id}, with trade_id: {trace_id}, the error is : {str(trace_error)}")
                
            return jsonify({
                'status': 'completed',
                'result': json.loads(result_json)
            })
        else:
            print(f"📥 No results found in database for check_id: {check_id}")
            error_message = f"Results not found for check_id: {check_id}"
            
            # Update Langfuse trace with error info
            try: 
                with langfuse.get_client().start_as_current_span(name="!!! Results Fetched but Not Found", trace_context={"trace_id": trace_id}):
                    langfuse.get_client().update_current_span(output={ "error in check_status": error_message, "error_code": "RESULTS_NOT_FOUND => 404" })
                print(f"📊 Updated Langfuse trace with error info for check_id: {check_id}")
            except Exception as trace_error:
                print(f"⚠️ Failed to update Langfuse trace for check_id: {check_id}, with trade_id: {trace_id},: {str(trace_error)}")
            
            return jsonify({ 'status': 'error', 'message': error_message,'error_code': 'RESULTS_NOT_FOUND'}), 404

    except Exception as e:
        error_message = f"Database error while retrieving results: {str(e)}"
        print(f"🔥 Error in check_status: {error_message}")
         
        try: 
            with langfuse.get_client().start_as_current_span(name="!!! Results Fetched but Database Error", trace_context={"trace_id": trace_id}):
                langfuse.get_client().update_current_span(output={"error": error_message, "error_code": "DATABASE_ERROR => 500", "details": str(e)})
            print(f"📊 Updated Langfuse trace with database error info for check_id: {check_id}")
        except Exception as trace_error:
            print(f"⚠️ Failed to update Langfuse trace for check_id: {check_id}: {str(trace_error)}")
        
        return jsonify({'status': 'error',  'message': error_message, 'error_code': 'DATABASE_ERROR', 'details': str(e) }), 500
    finally:
        if 'cursor' in locals():
            cursor.close()
        connection.close()  # Ensure connection is closed

@observe(name="/check_api")
def check_api(check_id):
    data = request.get_json()
    trace_id = langfuse.get_client().get_current_trace_id()
    
    # Create a copy of data for logging to avoid modifying the original
    log_data = copy.deepcopy(data) if data else {}
    
    # Identify and truncate likely base64 image strings for logging
    for key, value in log_data.items() if isinstance(log_data, dict) else []:
        if key in ['main_product_image', 'other_product_images', 'ip_images', 'reference_images']:
            # Handle array of images or direct image strings
            if isinstance(value, list):
                for i in range(len(value)):
                    if isinstance(value[i], str) and len(value[i]) > 500:
                        log_data[key][i] = f"[base64 image: {len(value[i])} chars]"
            elif isinstance(value, str) and len(value) > 500:
                log_data[key] = f"[base64 image: {len(value)} chars]"

    print(f"\n\n📥📥📥 API request received at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}: Parsed JSON data: {log_data}")
    print(f"Raw data: {request.data}")
    
    try:
        start_time = time.time()
        # Retrieve and validate API key first
        api_key = data.get('api_key')
        if not api_key or api_key not in allowed_api_keys:
            error_message = "Invalid API Key. Authentication failed."
            print(f"⛔ API authentication failed for {api_key}: {error_message}")
            return jsonify({'error': error_message, 'status': 'failed', 'error_code': 'AUTH_FAILED'}), 401
        else:
            client_info = allowed_api_keys.get(api_key)
            
        # Check rate limit before processing
        if not check_rate_limit(api_key):
            rate_limits = f"max {client_info['rate_limit']}/min and {client_info['daily_limit']}/day"
            error_message = f"Rate limit exceeded - {rate_limits}"
            print(f"⛔ API rate limit exceeded for {api_key}: {rate_limits}")
            return jsonify({'error': error_message, 'status': 'failed', 'error_code': 'RATE_LIMIT_EXCEEDED', 'rate_limits': {'per_minute': client_info['rate_limit'], 'per_day': client_info['daily_limit']}}), 429
        
        langfuse.get_client().update_current_trace(name=f"API Check", user_id=client_info['client_name'], session_id=check_id, metadata={"check_id": check_id, "client": client_info['client_name'], "api_key": api_key})
        langfuse.get_client().update_current_span(input=log_data)
        print(f"📊 Langfuse trace_id for check_id {check_id}: {langfuse.get_client().get_current_trace_id()}")
        print(f"📊 Updated Langfuse trace with check_id: {check_id} for client: {client_info['client_name']}")
    
        
        # Retrieve and validate other required fields.
        product_category = data.get('product_category', '') or ""
        main_product_image = data.get('main_product_image')
        if not main_product_image:
            error_message = "Missing required field: main_product_image"
            print(f"⛔ API validation error: {error_message}")
            return jsonify({'error': error_message, 'status': 'failed', 'error_code': 'MISSING_REQUIRED_FIELD'}), 400
            
        other_product_images = data.get('other_product_images', []) or []
        client_ip_images = data.get('ip_images', []) or []
        ip_keywords = data.get('ip_keywords', []) or []
        description = data.get('description', '')
            
        reference_text = data.get('reference_text', '') or ""
        reference_images = data.get('reference_images', []) or []
        
        # Create case check record for the database
        if client_info['client_name'] != "MiniApp" and client_info['client_name'] != "H5" and client_info['client_name'] != "MiniAppDev" and client_info['client_name'] != "H5Dev":
            db_case_check_data = {
                'id': int(check_id),
                'user_id': f"{client_info['client_name']}_{api_key}",
                'product_category': product_category,
                'images': zlib.compress(json.dumps(client_ip_images).encode()).hex(),  # Serialize list to JSON string
                'keyword': json.dumps(ip_keywords),  # Serialize list to JSON string
                'product_images': zlib.compress(main_product_image.encode()).hex(),
                'other_product_images': zlib.compress(json.dumps(other_product_images).encode()).hex(),  # Serialize list
                'product_describe': description,
                'reference_source': reference_text,
                'reference_images': zlib.compress(json.dumps(reference_images).encode()).hex()  # Serialize list
            }
        
            db_case_check_df = pd.DataFrame([db_case_check_data])            

            # Create a future for the database insertion, if awaited at the end of "process_check_with_db" in order not to be blocking
            db_future = executor.submit(insert_and_update_df_to_GZ_batch, db_case_check_df,  'tb_case_check_api',  'id')

    except KeyError as e:
        field_name = str(e).strip("'")
        error_message = f"Missing required field: {field_name}"
        print(f"⛔ API validation error: {error_message}")
        return jsonify({'error': error_message, 'status': 'failed', 'error_code': 'MISSING_REQUIRED_FIELD'}), 400
    except Exception as e:
        error_message = f"Internal server error: {str(e)}"
        print(f"🔥 API server error: {error_message}")  
        return jsonify({'error': error_message, 'status': 'failed', 'error_code': 'SERVER_ERROR', 'details': str(e)}), 500


    try:        # Submit to executor with proper error handling
        future = executor.submit(
            process_check_with_db,
            check_id=check_id,
            client_id=client_info['id'],
            client_name=client_info['client_name'],
            db_future=db_future if 'db_future' in locals() else None,
            main_product_image=main_product_image,
            other_product_images=other_product_images,
            client_ip_images=client_ip_images,
            ip_keywords=ip_keywords,
            description=description,
            reference_text=reference_text,
            reference_images=reference_images,
            langfuse_trace_id=trace_id 
        )
        
        # Add a callback to track when tasks start and finish processing
        def task_state_callback(future):
            if not future.done():
                currently_processing.add(check_id)
            else:
                currently_processing.discard(check_id)
                # Optionally clean up the timestamp when done
                if check_id in queue_timestamps:
                    del queue_timestamps[check_id]
        
        future.add_done_callback(task_state_callback)

        # Add the future to the futures dictionary, keyed by check_id
        futures[check_id] = future
        queue_timestamps[check_id] = time.time()  # Record submission time

        # Calculate queue position (add 1 because it includes the currently running task)
        queue_position = sum(1 for f in futures.values() if not f.done())

        # Add 1 to include any currently running tasks.
        queue_position += len(executor._threads)
        
        # Calculate estimated time (assuming 1/3 minute per task) and round up to nearest integer
        estimated_time = int(queue_position * 0.33 + 0.9999)

        return jsonify({'check_id': check_id, 'status': 'processing', 'message': 'Analysis started. Use check_status endpoint to poll results.', 'estimated_completion_time': f"{estimated_time} minutes" })
    except Exception as e:
        error_message = f"Error starting analysis: {str(e)}"
        print(f"🔥 API processing error: {error_message}")
        return jsonify({'error': error_message,  'status': 'failed','error_code': 'PROCESSING_ERROR','details': str(e)}), 500

# Define routes for standalone mode
@standalone_app.route('/api_studio')
def api_studio_route():
    return api_studio()

@standalone_app.route('/api_studio_reverse_check')
def api_studio_reverse_check_route():
    return api_studio_reverse_check()

def api_studio():
    return render_template('api_studio.html')

def api_studio_reverse_check():
    return render_template('api_studio_reverse_check.html')


@standalone_app.route('/check_status/<check_id>')
def check_status_route(check_id):
    return check_status(check_id)

@standalone_app.route('/check_api', methods=['POST'])
def check_api_route():
    now = datetime.now()
    data = request.get_json()
    api_key = data.get('api_key')
    client_info = allowed_api_keys.get(api_key)
    if client_info["client_name"] == "MiniApp" or client_info["client_name"] == "H5":
        check_id = data.get('check_id')
    else:
        check_id = f"{client_info['id']}{now.strftime('%Y%m%d%H%M%S')}"
    print(f"Generated check_id: {check_id}")
    trade_id = langfuse.get_client().create_trace_id(seed=check_id)
    print(f"Generated Langfuse trace_id: {trade_id}")
    return check_api(check_id=check_id, langfuse_trace_id=trade_id)

@standalone_app.route('/reverse_check_status', methods=['POST'])
def reverse_check_status_route():
    """Route to relay reverse check status requests to the Qdrant API"""
    import requests

    # Get the Qdrant API URL from environment variable
    qdrant_api_url = os.getenv("QDRANT_API_URL")
    if not qdrant_api_url:
        return jsonify({"error": "QDRANT_API_URL environment variable not set"}), 500

    # Relay the request to the Qdrant API
    try:
        response = requests.post(
            f"{qdrant_api_url}/reverse_check_status",
            json=request.get_json(),
            headers={'Content-Type': 'application/json'},
            timeout=30
        )

        # Return the response from the Qdrant API
        return jsonify(response.json()), response.status_code

    except requests.exceptions.RequestException as e:
        return jsonify({"error": f"Failed to connect to Qdrant API: {str(e)}"}), 500
    except Exception as e:
        return jsonify({"error": f"Server error: {str(e)}"}), 500

@standalone_app.route('/get_image_embeddings', methods=['POST'])
def get_image_embeddings_route():
    data = request.get_json()

    # API Key validation and rate limiting
    api_key = data.get('api_key')
    if not api_key or api_key != os.environ.get("API_BEARER_TOKEN"):
        error_message = "Invalid API Key. Authentication failed."
        print(f"⛔ API authentication failed for {api_key}: {error_message}")
        return jsonify({'error': error_message, 'status': 'failed', 'error_code': 'AUTH_FAILED'}), 401

    try:
        images_base64 = data.get('images', [])
        if not images_base64:
            error_message = "Missing required field: 'images' (list of base64 encoded images)"
            print(f"⛔ API validation error: {error_message}")
            return jsonify({'error': error_message,'status': 'failed','error_code': 'MISSING_REQUIRED_FIELD'}), 400

        # Decode base64 images to bytes
        decoded_images = []
        for img_b64 in images_base64:
            try:
                decoded_images.append(base64.b64decode(img_b64))
            except Exception as e:
                error_message = f"Invalid base64 image data: {str(e)}"
                print(f"⛔ API decoding error: {error_message}")
                return jsonify({
                    'error': error_message,
                    'status': 'failed',
                    'error_code': 'INVALID_IMAGE_DATA'
                }), 400

        # Get embeddings
        from Check.RAG.RAG_Inference import get_siglip_embeddings
        
        # Acquire lock before calling get_siglip_embeddings
        with siglip_lock:
            embeddings = get_siglip_embeddings(decoded_images, data_type="image")

        # Compress embeddings
        compressed_embeddings = zlib.compress(embeddings.tobytes())
        
        # Encode compressed embeddings to base64 for JSON transfer
        encoded_compressed_embeddings = base64.b64encode(compressed_embeddings).decode('utf-8')

        print(f"✅ Successfully generated and compressed embeddings for {len(decoded_images)} images, compression_ratio: {len(embeddings.tobytes()) / len(compressed_embeddings):.2f}x")

        return jsonify({
            'status': 'success',
            'embeddings': encoded_compressed_embeddings
        })

    except Exception as e:
        error_message = f"Internal server error during embedding generation: {str(e)}"
        print(f"🔥 API processing error: {error_message}")
        langfuse.get_client().update_current_span(output={"error": error_message}, status_message="error")
        return jsonify({
            'error': error_message,
            'status': 'failed',
            'error_code': 'PROCESSING_ERROR',
            'details': str(e)
        }), 500

# Standalone server
if __name__ == '__main__':
    import os
    from waitress import serve

    from Check.RAG.RAG_Inference import load_all_models
    load_all_models() 

    from Check.Data_Cache import update_dataframe_cache
    update_dataframe_cache()
    
    print("Starting API Studio Server...")
    serve(
        standalone_app,
        host="0.0.0.0",
        port=5000,  # if you change the port, you need to change in Javascript also
        threads=10,  # The number of requests Waitress can actively process at the same time. This is your primary concurrency control.
        connection_limit=40,  # The maximum number of connections Waitress will accept, including those being actively processed and those waiting in its internal queue.
        channel_timeout=900
    )
