# PatentApi Documentation

## Overview

The `PatentApi` class provides a Python interface to interact with the USPTO Patent Public Search API. This API allows searching across three main database types:

1. US-PGPUB (U.S. Patent Application Publication):

   - Represents published patent applications that have not yet been granted
   - Applications are published 18 months after the earliest filing date (with exceptions)
   - Document numbers follow the format YYYYNNNNNNN (e.g., 20230123456)
   - Applications are still under examination and may be granted, rejected, or abandoned
   - Publication makes the application's contents public

2. USPAT (U.S. Patent):

   - Represents granted U.S. patents
   - Patents have been examined and meet requirements for patentability
   - Owners have enforceable rights for typically 20 years from filing date
   - Document numbers are typically numbers without year prefix (e.g., 7,123,456)
   - Older patents may have fewer digits

3. USOCR (U.S. Optical Character Recognition):
   - Contains text data derived from OCR processing of patent documents
   - Primarily used for older patents originally in paper form
   - Enables searchable text access to scanned patent documents
   - Not a type of patent document, but rather a text representation

## Installation

```python
from AI.PatentApi import PatentApi
```

## Basic Usage

```python
async def main():
    api = PatentApi()
    await api.start_session()

    # Perform a search
    results = await api.search_patents("semiconductor AND memory")

    # Download a patent
    await api.save_patent("12243085", output_dir="patents")

    await api.close_session()
```

## Search Capabilities

### Basic Search Operators

The API supports various search operators:

1. Boolean Operators:

   - `AND`: Both terms must occur in the document
   - `OR`: At least one term must occur
   - `NOT`: First term must occur, second must not
   - `XOR`: One term must occur, but not both

2. Proximity Operators:

   - `ADJ`: Terms must occur next to each other in order
   - `ADJ[n]`: Terms within n words of each other (max 450)
   - `NEAR`: Terms must be adjacent in any order
   - `NEAR[n]`: Terms within n words in any order (max 450)
   - `WITH`: Terms must be in same sentence
   - `WITH[n]`: Terms within n sentences (max 25)
   - `SAME`: Terms must be in same paragraph
   - `SAME[n]`: Terms within n paragraphs (max 25)

3. Wildcards:
   - `?`: Matches any single character
   - `$[n]`: Matches up to n characters
   - `*` or `$`: Matches any number of characters

### Search Query Examples

Here are some practical examples of search queries:

1. Basic Boolean Search:

```python
# Find patents containing both "semiconductor" and "memory"
results = await api.search_patents("semiconductor AND memory")

# Find patents containing either "battery" or "capacitor"
results = await api.search_patents("battery OR capacitor")

# Find patents about "processor" but not "intel"
results = await api.search_patents("processor NOT intel")
```

2. Field-Specific Search:

```python
# Search in abstract
results = await api.search_patents("quantum computing.ab.")

# Search in title
results = await api.search_patents("artificial intelligence.ti.")

# Search in claims
results = await api.search_patents("neural network.clm.")

# Search in specification
results = await api.search_patents("machine learning.spec.")

# Search by inventor
results = await api.search_patents("smith.in.")

# Search by assignee
results = await api.search_patents("microsoft.as.")

# Search by application number
results = await api.search_patents("10/501576.app.")

# Search by patent number
results = await api.search_patents("1234567.pn.")

# Search by document ID
results = await api.search_patents("US-2418590-A.did.")

# Search by classification
results = await api.search_patents("G06F17/00.ipc.")  # IPC classification
results = await api.search_patents("138/$.ccls.")     # US Patent classification
results = await api.search_patents("F16L11/00.cpc.")  # Cooperative Patent Classification

# Search by examiner
results = await api.search_patents("smith.xa.")  # Assistant examiner
results = await api.search_patents("hook.xp.")   # Primary examiner
```

3. Date Range Search:

```python
# Patents published in 2023
results = await api.search_patents("@pd=2023")

# Patents published after 2020
results = await api.search_patents("@pd>2020")

# Patents from 2015-2020
results = await api.search_patents("@py>=2015<=2020")

# Patents filed in specific year
results = await api.search_patents("@ay=2020")

# Patents filed after specific date
results = await api.search_patents("@ad>20200101")

# Patents filed in specific year range
results = await api.search_patents("@ay>=2015<=2020")
```

4. Complex Queries:

```python
# Find recent AI patents by specific inventor and assignee
results = await api.search_patents("(artificial intelligence OR machine learning).ti. AND smith.in. AND google.as. AND @py>=2020")

# Find patents about battery technology in claims with specific date range and classification
results = await api.search_patents("battery.clm. AND (lithium OR solid-state) AND @py>=2015<=2023 AND H01M.cpc.")

# Find patents with specific proximity requirements and examiner
results = await api.search_patents("neural ADJ network AND deep ADJ learning AND smith.xp.")

# Find patents by multiple inventors in specific field
results = await api.search_patents("(smith.in. OR jones.in.) AND quantum.ab. AND @py>=2020")

# Find patents with specific application number and claims
results = await api.search_patents("10/501576.app. AND processor.clm.")

# Find patents by classification and date range
results = await api.search_patents("G06F17/00.ipc. AND @py>=2015<=2023")

# Find patents with specific document ID and abstract
results = await api.search_patents("US-2418590-A.did. AND machine learning.ab.")
```

5. Wildcard Search:

```python
# Find variations of "micro" words
results = await api.search_patents("micro*")

# Find specific length variations
results = await api.search_patents("process$3")  # matches process, processor, etc.

# Find variations in specific fields
results = await api.search_patents("micro*.ti.")  # in title
results = await api.search_patents("process$3.clm.")  # in claims
results = await api.search_patents("smith*.in.")  # in inventor names
```

6. Combined Field and Date Searches:

```python
# Find recent patents by specific assignee
results = await api.search_patents("microsoft.as. AND @py>=2020")

# Find patents by inventor in specific year range
results = await api.search_patents("smith.in. AND @ay>=2015<=2020")

# Find patents by classification and publication date
results = await api.search_patents("G06F17/00.ipc. AND @pd>=20200101")

# Find patents by examiner and application date
results = await api.search_patents("smith.xp. AND @ad>=20200101")
```

7. Advanced Classification Searches:

```python
# Search by US Patent Classification
results = await api.search_patents("435.clas.")  # Chemistry

# Search by Cooperative Patent Classification
results = await api.search_patents("B32B2307/50.cpca.")  # Specific CPC code

# Search by IPC Classification
results = await api.search_patents("A61M5/385.cpci.")  # Medical devices

# Combine classifications
results = await api.search_patents("(G06F17/00.ipc. OR G06F17/30.ipc.) AND @py>=2020")
```

8. Citation and Reference Searches:

```python
# Find patents citing a specific patent
results = await api.search_patents("8025207.urpn.")  # References cited patent number

# Find patents with specific references
results = await api.search_patents("8025207.urpn. AND @py>=2020")
```

### Field-Specific Searches

The API supports searching in specific patent fields using suffixes:

| Suffix | Description           | Example             |
| ------ | --------------------- | ------------------- |
| `AB`   | Abstract text         | `semiconductor.ab.` |
| `TI`   | Title text            | `memory.ti.`        |
| `CLM`  | Claims section        | `processor.clm.`    |
| `SPEC` | Specification section | `circuit.spec.`     |
| `IN`   | Inventor name         | `smith.in.`         |
| `AS`   | Assignee name         | `microsoft.as.`     |
| `PD`   | Publication date      | `20230101.pd.`      |
| `PN`   | Patent number         | `1234567.pn.`       |

### Date Range Searches

The API supports various date range operators:

- Equal: `@pd=20011118`
- Greater than: `@ad>19961231`
- Less than: `@py<1997`
- Range: `@ay>=1980<=1986`

## Methods

### `search_patents(query, database_filters=None, fields=None, page_size=50, cursor_marker="*", sort="date_publ desc")`

Searches for patents using the provided query.

**Parameters:**

- `query` (str): Search query string
- `database_filters` (list): List of database filters (default: ["USPAT", "US-PGPUB", "USOCR"])
- `fields` (list): Fields to include in response
- `page_size` (int): Results per page (default: 50)
- `cursor_marker` (str): Pagination marker (default: "\*")
- `sort` (str): Sort order (default: "date_publ desc")

**Returns:**

```python
{
    "cursorMarker": str,
    "numFound": int,
    "results": list,
    "nextCursorMarker": str
}
```

### `search_patents_paginated(query, database_filters=None, fields=None, page_size=50, max_pages=10, sort="date_publ desc")`

Performs a paginated search across multiple pages.

**Parameters:**

- Same as `search_patents()`
- `max_pages` (int): Maximum number of pages to retrieve

**Returns:**

- List of all results combined from all pages

### `download_patent_pdf(document_id)`

Downloads a patent document in PDF format.

**Parameters:**

- `document_id` (str): Patent document ID

**Returns:**

- PDF content as bytes

### `download_patent_html(document_id, source_db="USPAT")`

Downloads a patent document in HTML format.

**Parameters:**

- `document_id` (str): Patent document ID
- `source_db` (str): Source database (default: "USPAT")

**Returns:**

- HTML content as bytes

### `save_patent(document_id, output_dir=None, formats=None)`

Downloads and saves a patent document in various formats.

**Parameters:**

- `document_id` (str): Patent document ID
- `output_dir` (str): Output directory (default: current directory)
- `formats` (list): List of formats to download (default: ["pdf", "html"])

**Returns:**

- Dictionary with saved file paths for each format

## Error Handling

The API includes robust error handling and retry logic:

- Automatic token refresh
- Rate limiting (5 seconds between requests)
- Exponential backoff for retries
- Maximum retry attempts (5 by default)

## Best Practices

1. Always use `async/await` when calling API methods
2. Properly manage sessions using `start_session()` and `close_session()`
3. Use pagination for large result sets
4. Implement appropriate error handling
5. Consider rate limiting when making multiple requests

## Example Usage

```python
async def search_and_download():
    api = PatentApi()
    await api.start_session()

    try:
        # Search for patents
        results = await api.search_patents(
            query="semiconductor AND memory",
            fields=["documentId", "title", "datePublished"],
            page_size=10
        )

        # Download first result
        if results and results["results"]:
            first_patent = results["results"][0]
            await api.save_patent(
                first_patent["documentId"],
                output_dir="patents",
                formats=["pdf", "html"]
            )

    finally:
        await api.close_session()
```

## Notes

- The API uses token-based authentication
- Rate limiting is implemented to avoid 429 errors
- Results are sorted by relevance using TF-IDF scoring
- The API supports 70 fewer stopwords than legacy systems
- Document IDs can be in various formats (e.g., "US-12243085-B1")