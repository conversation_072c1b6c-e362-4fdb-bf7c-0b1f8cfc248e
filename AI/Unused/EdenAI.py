
import json
import requests
import pandas as pd
import os
import time


def eden_ai_call(file_path):
    headers = {"Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiZTJkY2IxM2UtZjYzNy00MmM3LWI0N2YtMWMwZmZmMWJmZTJjIiwidHlwZSI6ImFwaV90b2tlbiJ9.D9ElUlJlbblU8GHIbBtNoXFPMNvLEvDJzL93ii8YQ0A"}

    url = "https://api.edenai.run/v2/workflow/e6a708e6-36ba-4750-8a4f-b3974b1eee99/execution/"
    files = {"file":open(file_path, 'rb')} 
    payload = {"Provider":"ABC"}

    try:
        response = requests.post(url, files=files, data=payload, headers=headers, timeout=30)
        result = json.loads(response.text)
    except requests.exceptions.Timeout:
        print("Request timed out. Retrying...")
        response = requests.post(url, files=files, data=payload, headers=headers, timeout=30)
        result = json.loads(response.text)
    return result

def eden_ai_get_result(id):
    headers = {"Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiZTJkY2IxM2UtZjYzNy00MmM3LWI0N2YtMWMwZmZmMWJmZTJjIiwidHlwZSI6ImFwaV90b2tlbiJ9.D9ElUlJlbblU8GHIbBtNoXFPMNvLEvDJzL93ii8YQ0A"}

    url = f"https://api.edenai.run/v2/workflow/e6a708e6-36ba-4750-8a4f-b3974b1eee99/execution/{id}/"

    try:
        response = requests.get(url, headers=headers, timeout=30)
        result = json.loads(response.text)
    except requests.exceptions.Timeout:
        print("Request timed out. Retrying...")
        response = requests.get(url, headers=headers, timeout=30)
        result = json.loads(response.text)
    
    while result["content"]["status"] != "success":
        time.sleep(2)
        try:
            response = requests.get(url, headers=headers, timeout=30)
            result = json.loads(response.text)
        except requests.exceptions.Timeout:
            print("Request timed out. Retrying...")
            response = requests.get(url, headers=headers, timeout=30)
            result = json.loads(response.text)
        

    return result



def get_brand_eden():
    product_path = "D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/Products-HardExStan"
    results_df = pd.DataFrame(columns=["picture", "Plaintiff", "AI_brand", "AI_confidence"])
    files = os.listdir(product_path)
    # files = files[-85] #Gemini flash status
    files = files[89:]
    for i, file in enumerate(files):
        plaintiff = file.replace("_cropped", "").split("_")[-1].split(".")[0]
        answer_ai = eden_ai_call(os.path.join(product_path, file)) # , model_name='gemini-flash-experimental'
        id = answer_ai['id']
        result = eden_ai_get_result(id)
        
        if len(result["content"]["results"]["image__logo_detection"]["results"]) > 1:
            print("alert1")
        if len(result["content"]["results"]["image__logo_detection"]["results"]) > 0:
            if len(result["content"]["results"]["image__logo_detection"]["results"][0]["items"]) > 1:
                print("alert2")
            if len(result["content"]["results"]["image__logo_detection"]["results"][0]["items"]) > 0: 
                description = result["content"]["results"]["image__logo_detection"]["results"][0]["items"][0]["description"]
                score = result["content"]["results"]["image__logo_detection"]["results"][0]["items"][0]["score"]
                print(f"{i}/{len(files)}: {description} - {score}")
                results_df.loc[len(results_df)] = [file, plaintiff, description, score]  
            else:
                print(f"{i}/{len(files)}: No brand detected")      
                results_df.loc[len(results_df)] = [file, plaintiff, "No brand detected", -1]  
        
    results_df.to_excel("D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/resultsEdenAI.xlsx", index=False)


def get_brand_test():
    product_path = "D:/Win10User/Downloads"
    file = "Screenshot 2024-11-05 002618.jpg"
    answer_ai = eden_ai_call(os.path.join(product_path, file))
    id = answer_ai['id']
    result = eden_ai_get_result(id)
    print(f"{result}")


if __name__ == "__main__":
    get_brand_eden()
