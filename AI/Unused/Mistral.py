import os
from mistralai import Mistral
from dotenv import load_dotenv
import base64
import time

load_dotenv()

def encode_image_base64(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")

# def pixtral(prompt, image_path, model = "pixtral-12b-2409"): #"pixtral-large-latest"
#     api_key = os.getenv("MISTRAL_API_KEY")
#     client = Mistral(api_key=api_key)
#     base_64_image = encode_image_base64(image_path)

#     chat_response = client.chat.complete(
#         model=model,
#         messages = [
#             {
#                     "role": "user",
#                     "content": [
#                         {
#                             "type": "text",
#                             "text": prompt
#                         },
#                         {
#                         "type": "image_url",
#                         "image_url": f"data:image/jpeg;base64,{base_64_image}"
#                         }
#                     ]
#             },
#         ]
#     )
#     return chat_response.choices[0].message.content


def pixtral(prompt, image_path, model="pixtral-12b-2409", max_retries=5, delay=10):
    api_key = os.getenv("MISTRAL_API_KEY")
    client = Mistral(api_key=api_key)
    base_64_image = encode_image_base64(image_path)

    for attempt in range(max_retries):
        try:
            chat_response = client.chat.complete(
                model=model,
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": f"data:image/jpeg;base64,{base_64_image}"
                            }
                        ]
                    },
                ]
            )
            return chat_response.choices[0].message.content
            
        except Exception as e:
            if attempt < max_retries - 1:  # Don't sleep on the last attempt
                print(f"Mistral API call failed (attempt {attempt + 1}/{max_retries}): {e}")
                print(f"Waiting {delay:.1f} seconds before retrying...")
                time.sleep(delay)
            else:
                print(f"Mistral API call failed after {max_retries} attempts: {e}")
                raise  # Re-raise the last exception if all retries failed


