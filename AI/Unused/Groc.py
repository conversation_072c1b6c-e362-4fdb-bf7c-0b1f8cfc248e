import os
from groq import Groq
import base64
import time

# Llama 3.2 90B Vision	3,500 requests/day ;  7,000 tokens/minute
# Llama 3.3 70B	14,400 requests/day ; 6,000 tokens/minute
# Llama 3.3 70B (Speculative Decoding)	1,000 requests/day ; 6,000 tokens/minute
# Models: llama-3.3-70b-versatile, llama-3.3-70b-specdec, llama-3.2-90b-vision-preview

def groq_text(prompt, model_name="llama-3.2-90b-vision-preview", max_retries=3, delay=60):
    try:
        client = Groq(api_key=os.environ.get("GROQ_API_KEY"))
        chat_completion = client.chat.completions.create(
            messages=[
                {
                    "role": "user",
                    "content": prompt,
                }
            ],
            model=model_name,
        )
        return chat_completion.choices[0].message.content
        
    except Exception as e:
        print(f"LLM call failed with non-recoverable error: {e}")
        raise


# Function to encode the image
def encode_image(image_path):
  with open(image_path, "rb") as image_file:
    return base64.b64encode(image_file.read()).decode('utf-8')

def groq_image(prompt, image_path, model_name="llama-3.2-90b-vision-preview", max_retries=3, delay=60):
    try:
        # Getting the base64 string
        base64_image = encode_image(image_path)
        client = Groq(api_key=os.environ.get("GROQ_API_KEY"))
        
        # The client will handle 429s (too many requests) internally with its own retry mechanism
        chat_completion = client.chat.completions.create(
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}",
                            },
                        },
                    ],
                }
            ],
            model=model_name,
        )
        return chat_completion.choices[0].message.content
        
    except Exception as e:
        print(f"LLM call failed with non-recoverable error: {e}")
        raise

if __name__ == "__main__":
    print("llama-3.2-90b-vision-preview: ", groq_text("Tell me a joke", "llama-3.2-90b-vision-preview"))
    print("llama-3.3-70b-versatile: ", groq_text("Tell me a joke", "llama-3.3-70b-versatile"))
    print("llama-3.3-70b-specdec: ", groq_text("Tell me a joke", "llama-3.3-70b-specdec"))
    print("llama-3.2-90b-vision-preview: ", groq_image("What brand is this?", "D:/Documents/Programing/TRO/USside/Documents/AssessFiles/For AI/Products/1_A_GOPRO_cropped.jpg", "llama-3.2-90b-vision-preview"))



