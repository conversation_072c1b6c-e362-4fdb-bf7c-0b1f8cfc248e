# Google AI Studio: Can only fine tune Flash, not Pro. 
# Free tier: It is free to fine tune Flash, then you can use the fine tune model free. Our data will be used to train the model.
# Pay-as-you-go: It is free to fine tune Flash, then you pay to use the model. Our data will not be used to train the model.


# Google Cloud: Can fine tune Flash and Pro. Flash is $8 per 1M tokens, Pro is $80 per 1M tokens. 
# => 60k pictures with 256 token each = 15m tokens => 120 USD for flash and 1200 USD for pro!
# https://cloud.google.com/vertex-ai/generative-ai/pricing

# Openai: 4o-0806: $25.000 / 1M training tokens. 4o-mini: $3.000 / 1M training tokens


# Strategy: finetune flash (pay as you go) on google ai studio and see the improvement. Then see if it is worth to finetune Pro.


import time
from google import genai
from google.genai import types
from AI.GC_Credentials import get_gcs_credentials
import vertexai
import os

# Configuration
GCS_BUCKET_NAME = "trodata_bucket" 
PROJECT_ID = os.environ.get("GOOGLE_CLOUD_PROJECT")
REGION = os.environ.get("GOOGLE_CLOUD_LOCATION")
TRAINING_DATA_PATH = "copyright_training_data.jsonl"
CREDENTIALS = get_gcs_credentials()
vertexai.init(project=PROJECT_ID, location=REGION, credentials=CREDENTIALS)
client = genai.Client(vertexai=True, project=PROJECT_ID, location=REGION, credentials=CREDENTIALS)

def fine_tune_gemini(base_model, tuned_model_display_name):
    """Fine-tunes the Gemini model using the prepared data."""
    training_dataset = {
        "gcs_uri": f"gs://{GCS_BUCKET_NAME}/{TRAINING_DATA_PATH}",
    }

    # training_dataset = types.TuningDataset(gcs_uri=TRAINING_DATA_PATH)
    
    tuning_job = client.tunings.tune(
        base_model=base_model,
        training_dataset=training_dataset,
        config=types.CreateTuningJobConfig(
            epoch_count=3,
            tuned_model_display_name=tuned_model_display_name,
        ),
    )
    print(f"Tuning job started: {tuning_job.name}")
    
    running_states = {
        "JOB_STATE_PENDING",
        "JOB_STATE_RUNNING",
    }
    
    while tuning_job.state in running_states:
        print(f"Tuning job state: {tuning_job.state}")
        tuning_job = client.tunings.get(name=tuning_job.name)
        time.sleep(60)
    
    print(f"Tuning job finished with state: {tuning_job.state}")
    print(f"Tuned model endpoint: {tuning_job.tuned_model.endpoint}")

if __name__ == "__main__":
    # 1 epoch
    # fine_tune_gemini("gemini-1.5-flash-002", "gemini-1.5-flash-002-1E")  # "gemini-2.0-flash-exp" # or "gemini-1.5-flash-002"
    # Tuned model endpoint: projects/1083701181239/locations/us-central1/endpoints/2764674709042757632

    # 3 epochs
    fine_tune_gemini("gemini-1.5-flash-002", "gemini-1.5-flash-002-3E")

    


    # Add to finetuned model
# Create New JSONL for New Images:
# When you have new images, run a modified version of the script that only processes the new images and creates a new JSONL file containing only the new data.
# Resume Fine-Tuning:
# Use the tuning_job.resume() method (if available in the SDK) or create a new tuning job using the previously tuned model as the base_model and the new JSONL file as the training_dataset. This way, you are building upon the previous fine-tuning instead of starting from scratch.