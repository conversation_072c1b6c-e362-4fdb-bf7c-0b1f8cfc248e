import os
import time
import json
import requests
from googleapiclient.discovery import build
import uuid
from selenium.webdriver.common.by import By
from Alerts.Chrome_Driver import get_driver
import imghdr  # Import imghdr for image header validation
from PIL import Image  # Import Pillow for more robust image validation
from io import BytesIO # Import BytesIO to handle image data in memory
from Common.Constants import sanitize_name # Import sanitize_name


def search_google(query, num_results=5):
    """Search Google for information about a plaintiff"""
    try:
        service = build("customsearch", "v1", developerKey=os.environ["GOOGLE_API_KEY"])
        result = service.cse().list(q=query, cx=os.environ["GOOGLE_CSE_ID"], num=num_results).execute()
        
        if "items" in result:
            # Combine title and snippet from top 3 results
            context = ""
            for item in result["items"]:
                context += f"{item['title']}: {item.get('snippet', '')}\n"
            return context
        return ""
    except Exception as e:
        print(f"API Google search error: {e}")
        print(f"Trying UC...")
        return search_google_uc(query, num_results)
    
    
def search_google_uc(query, num_results=5):
    driver = get_driver()
    try:
        driver.get(f"https://www.google.com/search?q={query}")

        # Wait for the page to load
        time.sleep(2)

        # Extract search results
        search = driver.find_elements(By.ID, 'search')
        return search[0].text

    except Exception as e:
        print(f"UC Google search error: {e}")
        try: 
            results = driver.find_elements(By.CSS_SELECTOR, 'div.g')
            context = ""
            for result in results[:num_results]:
                    # title = result.find_element(By.TAG_NAME, 'h3').text
                    # snippet = result.find_element(By.CSS_SELECTOR, 'div.VwiC3b').text
                    context += f"{result.text}\n"
            return context
        except Exception as e:
            print(f"UC Google search error: {e}")
            return context

    finally:
        driver.quit()

def validate_image(image_path):
    """
    Validates an image file by checking its size, header, and attempting to open it.
    Returns True if the image is valid, False otherwise.
    """
    try:
        if os.path.getsize(image_path) == 0:
            print(f"[ERROR] Image file is empty: {image_path}")
            return False

        image_type = imghdr.what(image_path)
        if not image_type:
            print(f"[ERROR] Invalid image file header: {image_path}")
            return False

        try:
            img = Image.open(image_path)
            img.verify()  # Verify the image file is not truncated
            img.close()
        except (IOError, SyntaxError) as e:
            print(f"[ERROR] Cannot open image: {image_path} - {e}")
            return False

        return True

    except Exception as e:
        print(f"[ERROR] Error validating image: {image_path} - {e}")
        return False

def download_and_save_image(image_url, image_path):
    """Downloads an image from a URL and saves it to a specified path."""
    try:
        response = requests.get(image_url, stream=True, timeout=10)
        response.raise_for_status()  # Raise HTTPError for bad responses (4xx or 5xx)
        image_data = BytesIO(response.content)  # Store image data in BytesIO

        with open(image_path, 'wb') as image_file:
            image_file.write(image_data.getvalue())  # Write from BytesIO
        return True  # Indicate success

    except requests.exceptions.RequestException as e:
        print(f"Error downloading image: {e}")
        return False  # Indicate failure
    except Exception as e:
        print(f"Error saving image: {e}")
        return False

def search_and_save_images(query, folder_path, num_results=10):
    """Search Google for images and save them to a specified folder"""
    # IMPORTANT: Configure your Custom Search Engine (CSE) in the Google Cloud Console
    # to achieve behavior similar to incognito mode and non-personalized search.
    # This includes setting site restrictions, SafeSearch settings, and other options.
    # The API itself does not have an "incognito" mode.
    try:
        service = build("customsearch", "v1", developerKey=os.environ["GOOGLE_SEARCH_TRODATA"])
        # Use safeSearch='off' to disable SafeSearch.  Consider the implications!
        result = service.cse().list(q=query, cx=os.environ["GOOGLE_SEARCH_TRODATA_PSE"], num=num_results, searchType="image").execute()
        metadata_list = [] 
        if "items" in result:
            if not os.path.exists(folder_path):
                os.makedirs(folder_path)
            
            image_filenames = []  # List to store image filenames
            
            for i, item in enumerate(result["items"]):
                sanitized_query_part = sanitize_name(query)
                image_filename = f"{sanitized_query_part}_result_{i + 1}.jpg"
                image_path = os.path.join(folder_path, image_filename)
                title = item.get('title', '')
                html_title = item.get('htmlTitle', '')
                link = item.get('link', '')
                display_link = item.get('displayLink', '')
                thumbnail_link = item.get('image', {}).get('thumbnailLink', '')  # Safely get thumbnail link

                try:
                    # Prioritize direct image links
                    if link and link.startswith(('http://', 'https://')):
                        if download_and_save_image(link, image_path):
                            if validate_image(image_path):
                                image_filenames.append(image_filename)
                                # Extract and store metadata
                                print(f"Result {i+1}:")
                                print(f"  Title: {title}")
                                print(f"  HTML Title: {html_title}")
                                print(f"  Link: {link}")
                                print(f"  Display Link: {display_link}")
                                print(f"  Filename: {image_filename}")
                                print(f"  Image Path: {image_path}")
                                # Store metadata in a dictionary
                                metadata_list.append({
                                    'title': title,
                                    'htmlTitle': html_title,
                                    'link': link,
                                    'displayLink': display_link,
                                    'filename': image_filename,
                                    'image_path': image_path
                                })
                                continue  # Skip to the next image
                            else:
                                print(f"[ERROR] Invalid image, removing: {image_path}")
                                os.remove(image_path)
                        else:
                            print(f"[WARNING] Failed to download image from main link: {link}")

                    # Fallback to thumbnailLink if the main link is invalid
                    if thumbnail_link and thumbnail_link.startswith(('http://', 'https://')):
                        print(f"Attempting thumbnail fallback for result {i+1} from {thumbnail_link}")
                        if download_and_save_image(thumbnail_link, image_path):
                            if validate_image(image_path):
                                image_filenames.append(image_filename)
                                # Extract and store metadata
                                print(f"Result {i+1} (Thumbnail):")
                                print(f"  Title: {title}")
                                print(f"  HTML Title: {html_title}")
                                print(f"  Link: {link}")
                                print(f"  Display Link: {display_link}")
                                print(f"  Filename: {image_filename}")
                                print(f"  Image Path: {image_path}")
                                # Store metadata in a dictionary
                                metadata_list.append({
                                    'title': title,
                                    'htmlTitle': html_title,
                                    'link': link,
                                    'displayLink': display_link,
                                    'filename': image_filename,
                                    'image_path': image_path
                                })
                                continue  # Skip to the next image
                            else:
                                print(f"[ERROR] Invalid thumbnail image, removing: {image_path}")
                                os.remove(image_path)
                        else:
                            print(f"[WARNING] Failed to download thumbnail image from {thumbnail_link}")
                    else:
                        print(f"Skipping result {i+1}: No valid image or thumbnail URL found.")
                        continue # Skip if no valid link.

                except Exception as e:
                    print(f"An unexpected error occurred: {e}")

            print(f"Saved {len(image_filenames)} images/thumbnails to {folder_path}")
            return json.dumps(image_filenames), json.dumps(metadata_list)
        else:
            print("No images found.")
            return json.dumps([]), json.dumps([])
    except Exception as e:
        print(f"Google image search error: {e}")
        return json.dumps([]), json.dumps([])

# def search_and_save_images_rest(query, folder_path, num_results=10):
#     """Search Google for images using the REST API and save them."""
#     api_key = os.environ["GOOGLE_SEARCH_TRODATA"]
#     cse_id = os.environ["GOOGLE_SEARCH_TRODATA_PSE"]
#     url = f"https://www.googleapis.com/customsearch/v1?key={api_key}&cx={cse_id}&q={query}&searchType=image&num={num_results}"

#     try:
#         response = requests.get(url)
#         response.raise_for_status()  # Raise an exception for bad status codes
#         result = response.json()

#         if "items" in result:
#             if not os.path.exists(folder_path):
#                 os.makedirs(folder_path)

#             image_filenames = []
#             for i, item in enumerate(result["items"]):
#                 image_url = item['link']
#                 image_data = requests.get(image_url).content
#                 random_id = uuid.uuid4().hex[:8]
#                 image_filename = f"{i}_{random_id}.jpg"
#                 image_path = os.path.join(folder_path, image_filename)

#                 with open(image_path, 'wb') as image_file:
#                     image_file.write(image_data)

#                 image_filenames.append(image_filename)

#             print(f"Saved {len(result['items'])} images to {folder_path}")
#             return json.dumps(image_filenames)
#         else:
#             print("No images found.")
#             return json.dumps([])
#     except requests.exceptions.RequestException as e:
#         print(f"REST API Google image search error: {e}")
#         return json.dumps([])




if __name__ == "__main__":
    from dotenv import load_dotenv
    load_dotenv()
    search_and_save_images("April Major Rimpo 1955 Essence", "C:\\Users\\<USER>\\Documents\\maidalv\\TRO-USside\\@copyrights image screenshots")