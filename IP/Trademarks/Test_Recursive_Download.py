"""
Test script for the recursive certificate download logic.
This tests the binary search approach to isolate problematic certificates.
"""

import asyncio
import time
import os
from IP.Trademarks.USPTO_TSDR_API import TSDRApi
from IP.Trademarks.Trademark_API import download_certificates_recursive
from Common.Constants import local_ip_folder


async def save_content(content, filename, folder, is_xml=False):
    """Simple save content function for testing."""
    if content:
        os.makedirs(folder, exist_ok=True)
        filepath = os.path.join(folder, filename)
        try:
            with open(filepath, 'wb') as f:
                f.write(content)
            print(f"💾 Saved {filename} ({len(content)} bytes)")
            return True
        except Exception as e:
            print(f"❌ Error saving {filename}: {e}")
            return False
    return False


async def test_recursive_download():
    """Test the recursive download logic."""
    
    # Test data: mix of valid and potentially invalid registration numbers
    test_reg_nos = [
        "6181442",  # Known valid
        "1871376",  # Known valid  
        "5004543",  # Known valid
        "9999999",  # Likely invalid - should cause batch to fail
        "4083473",  # Known valid
        "3306673",  # Known valid
        "1346436",  # Known valid
        "8888888",  # Likely invalid - should cause batch to fail
        "5546145",  # Known valid
        "6731985"   # Known valid
    ]
    
    print("🚀 Testing Recursive Certificate Download Logic")
    print("=" * 60)
    print(f"Test registration numbers: {test_reg_nos}")
    print(f"Expected behavior: Binary search should isolate invalid certificates")
    print("-" * 60)
    
    # Setup
    api_client = TSDRApi()
    await api_client.start_session()
    
    certificate_folder = os.path.join(local_ip_folder, "Trademarks", "Test_Recursive_Certificates")
    os.makedirs(certificate_folder, exist_ok=True)
    
    try:
        start_time = time.time()
        
        # Run recursive download
        successful_reg_nos, failed_reg_nos = await download_certificates_recursive(
            api_client, test_reg_nos, certificate_folder
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        # Results
        print("\n" + "=" * 60)
        print("🏆 RECURSIVE DOWNLOAD TEST RESULTS")
        print("=" * 60)
        print(f"⏱️  Total time: {duration:.2f} seconds")
        print(f"📊 Total requested: {len(test_reg_nos)}")
        print(f"✅ Successful downloads: {len(successful_reg_nos)}")
        print(f"❌ Failed downloads: {len(failed_reg_nos)}")
        print(f"📈 Success rate: {len(successful_reg_nos)/len(test_reg_nos):.1%}")
        
        if successful_reg_nos:
            print(f"\n✅ Successfully downloaded certificates:")
            for reg_no in successful_reg_nos:
                print(f"   • {reg_no}")
        
        if failed_reg_nos:
            print(f"\n\033[91m❌ Failed to download certificates:\033[0m")
            for reg_no in failed_reg_nos:
                print(f"\033[91m   • {reg_no}\033[0m")
        
        # Check saved files
        saved_files = [f for f in os.listdir(certificate_folder) if f.endswith('.zip')]
        print(f"\n📁 Files saved in {certificate_folder}:")
        for file in saved_files:
            file_path = os.path.join(certificate_folder, file)
            file_size = os.path.getsize(file_path)
            print(f"   • {file} ({file_size:,} bytes)")
        
        print("=" * 60)
        
        # Verify the logic worked correctly
        expected_successful = [reg for reg in test_reg_nos if reg not in ["9999999", "8888888"]]
        expected_failed = ["9999999", "8888888"]
        
        print("\n🔍 VERIFICATION:")
        if set(successful_reg_nos) == set(expected_successful):
            print("✅ Successful downloads match expected results")
        else:
            print("⚠️  Successful downloads don't match expected results")
            print(f"   Expected: {expected_successful}")
            print(f"   Actual: {successful_reg_nos}")
        
        if set(failed_reg_nos) == set(expected_failed):
            print("✅ Failed downloads match expected results")
        else:
            print("⚠️  Failed downloads don't match expected results")
            print(f"   Expected: {expected_failed}")
            print(f"   Actual: {failed_reg_nos}")
        
    except Exception as e:
        print(f"💥 Test failed with error: {e}")
        raise
    finally:
        await api_client.close_session()


async def test_simple_batch():
    """Test a simple batch to compare with recursive approach."""
    
    print("\n🔄 COMPARISON: Testing simple batch approach")
    print("-" * 40)
    
    # Same test data
    test_reg_nos = [
        "6181442", "1871376", "5004543", "9999999", "4083473"
    ]
    
    api_client = TSDRApi()
    await api_client.start_session()
    
    try:
        start_time = time.time()
        
        # Try bulk download (should fail due to invalid certificate)
        print(f"📦 Trying bulk download of {len(test_reg_nos)} certificates...")
        reg_cert_content = await api_client.get_casedocs_bundle(
            test_reg_nos, id_key='rn', format='zip', option='category=RC'
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        if reg_cert_content:
            print(f"✅ Bulk download succeeded in {duration:.2f}s ({len(reg_cert_content)} bytes)")
        else:
            print(f"❌ Bulk download failed in {duration:.2f}s (as expected due to invalid certificate)")
            
    except Exception as e:
        print(f"💥 Bulk download error: {e}")
    finally:
        await api_client.close_session()


if __name__ == "__main__":
    print("🧪 Starting Recursive Certificate Download Test")
    print("This will test the binary search logic for isolating problematic certificates.")
    print("Expected duration: ~2-3 minutes due to rate limiting")
    print()
    
    asyncio.run(test_recursive_download())
    asyncio.run(test_simple_batch())
