import time, os, shutil
import undetected_chromedriver as uc
from IP.Trademarks.Trademark_API import get_trademarks_uspto, get_certificate_local_path
from Common.Constants import local_ip_folder

from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import StaleElementReferenceException, TimeoutException

from Alerts.Chrome_Driver import get_driver, move_mouse_to, random_delay


def process_trademarks_from_uspto(df, index, df_trademarks, case_images_directory):
    # Process trademarks received from the USPTO API and update the dataframe
    success = False
    
    for _, trademark in df_trademarks.iterrows():
        if "metadata" in trademark:
            metadata = trademark["metadata"]
            
            # Create filenames for the trademark
            if "trademark_filename" in metadata:
                trademark_filename = metadata["trademark_filename"]
                full_filename = metadata.get("full_filename")
                formatted_reg_no = trademark.get("reg_no", "") # Assuming reg_no is already formatted if coming from API

                # Copy images to the case directory if they exist in the central IP folder
                images_copied = False
                if formatted_reg_no: # Need reg_no to find files
                    tradeMark_img_path = os.path.join(local_ip_folder, "Trademarks", "Images", f"{formatted_reg_no}.webp")
                    # Use new certificate path structure
                    ser_no = trademark.get("ser_no")
                    certificate_base_folder = os.path.join(local_ip_folder, "Trademarks", "Certificates")
                    certificate_img_path = get_certificate_local_path(certificate_base_folder, ser_no=ser_no, reg_no=formatted_reg_no)

                    if os.path.exists(tradeMark_img_path):
                        shutil.copy(tradeMark_img_path, os.path.join(case_images_directory, trademark_filename))
                        images_copied = True # Mark as copied if at least the main image is copied
                    else:
                        print(f"\033[91m 🔥 Could not find trademark image in central folder: {tradeMark_img_path}\033[0m")

                    if full_filename and os.path.exists(certificate_img_path):
                        shutil.copy(certificate_img_path, os.path.join(case_images_directory, full_filename))
                    elif full_filename:
                        # Certificate might not be available but we can still proceed if the main image was copied
                        pass
                
                # Only create record and mark success if images were copied (or at least the main one)
                if images_copied:
                    # Create a record in the dataframe for this trademark
                    df.at[index, 'images']['trademarks'][trademark_filename] = {
                        'trademark_text': [trademark.get("text", "")],
                        'reg_no': [formatted_reg_no],
                        'int_cls': [trademark.get("int_cls", [])], # Use empty list as default
                        'full_filename': [full_filename] if full_filename else []
                    }
                    success = True
                else:
                     print(f"\033[91m 🔥 Skipping dataframe update for {formatted_reg_no} as essential image was not found/copied.\033[0m")
    
    return success

def uspto_get_reg_by_owner(owner_name, max_results=100):
    serial_list = []
    owner_list = set()
    driver = None

    retries = 0
    while retries < 3:
        try:
            # Check if the driver is still "alive" and recreate it if necessary
            if driver is None:
                print("Driver is None.")
                driver_alive = False
            else:
                try:
                    driver.title  # Simple command to check if the driver is responsive
                    driver_alive = True
                except Exception as e:
                    print("Error while trying to access driver.title:", e)
                    driver_alive = False

            if not driver_alive:
                if driver is not None:
                    try:
                        driver.quit()  # Try to quit the old driver
                    except Exception:
                        pass  # Ignore any errors during quitting
                print("Creating a new driver instance...")
                driver = get_driver()

            # Navigate to search page
            search_url = "https://tmsearch.uspto.gov/search/search-information"
            driver.get(search_url)
            random_delay(1,2)

            # Select Owner search refinement
            menu = WebDriverWait(driver, 20).until(EC.element_to_be_clickable((By.CSS_SELECTOR, "mat-select[formcontrolname='searchRefinement']")))
            menu.click()
            random_delay(1,2)

            owner_option = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.XPATH, "//mat-option[contains(., 'Owner')]")))
            # move_mouse_to(driver, owner_option)
            owner_option.click()
            random_delay(1,2)

            # Input owner name and search
            search_input = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "searchbar")))
            # move_mouse_to(driver, search_input)
            search_input.send_keys(owner_name)
            random_delay(0.5,1)
            
            # Click search button
            search_btn = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.XPATH, "//button[contains(@class, 'md-icon')]//i[contains(text(), 'search')]/..")))
            # move_mouse_to(driver, search_btn)
            search_btn.click()
            random_delay(1,1.1)

            # Filter out dead trademarks
            WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.ID, "statusDead"))).click()
            random_delay(1,1.1)

            # Filter out pending trademarks
            WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.ID, "statusPending"))).click()
            random_delay(1,1.1)

            # Check for "No results found" message
            try:
                no_results_xpath = "//div[@role='status']/span[contains(text(), 'No results found')]"
                WebDriverWait(driver, 3).until(EC.presence_of_element_located((By.XPATH, no_results_xpath)))
                print(f"\033[34m🚫🚫🚫 No trademark results found for owner: {owner_name}\033[0m")
                driver.quit()
                return [], set() # Return empty lists if no results
            except TimeoutException:
                # No "No results found" message detected, proceed to process results
                pass 


            while True: # For each page
                WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.CSS_SELECTOR, "app-search-results-grid")))
                cards = WebDriverWait(driver, 20).until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "#resultsRow > div > div.card-body.ps-0.pe-0.ps-sm-2.pe-sm-2")))
                
                for card in cards: # For each trademark
                    try:
                        serial_div = card.find_element(By.XPATH, ".//div[contains(., 'Serial')]/div[@class='col ps-0 ps-sm-1']")
                        owner_div = card.find_element(By.XPATH, ".//div[contains(., 'Owners')]/div[@class='col listMultipleLine ps-0 ps-sm-1']")
                        serial_list.append(serial_div.text.strip())
                        owner_list.add(owner_div.text.strip())
                        
                    except StaleElementReferenceException:
                        continue

                if len(serial_list) >= max_results:
                    break
                
                # Pagination handling
                try:
                    # Find next button using XPath with icon text
                    next_btn = WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.XPATH, "//i[contains(@class, 'material-icons') and text()='navigate_next']/.."))
                    )
                    
                    # Check if disabled
                    if "disabled" in next_btn.find_element(By.XPATH,"..").get_attribute("class"):
                        break
                    # move_mouse_to(driver, next_btn)
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                    time.sleep(1)
                    next_btn.click()
                    random_delay(0,1)
                except Exception as e:
                    print(f"Pagination error: {str(e)}")
                    if dismiss_popup(driver):
                        continue  # Retry pagination after dismissal
                    else:
                        break

            break  # Break out of retry loop if successful

        except Exception as e:
            print(f"Error: {str(e)}")
            dismissed = dismiss_popup(driver)
            if not dismissed:
                retries += 1
            if retries >= 3:
                break
            # Reset driver only if popup wasn't the cause
            if not dismissed:
                try: driver.quit()
                except: pass
                driver = get_driver()


    driver.quit()
    return serial_list, owner_list

def dismiss_popup(driver):
    try:
        no_thanks_btn = WebDriverWait(driver, 2).until(
            EC.element_to_be_clickable((By.XPATH, "//button[contains(., 'No Thanks') and contains(@class, 'QSIWebResponsiveDialog')]"))
        )
        # move_mouse_to(no_thanks_btn)
        no_thanks_btn.click()
        print("Dismissed survey popup")
        return True
    except:
        return False

if __name__ == "__main__":
    serial_list = uspto_get_reg_by_owner("Chanel")
