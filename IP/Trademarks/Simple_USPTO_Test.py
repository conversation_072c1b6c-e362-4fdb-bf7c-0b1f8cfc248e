"""
Simple USPTO API Test

Minimal script to quickly answer: How long does USPTO take to respond?
- Single certificate request
- 20-certificate ZIP request
"""

import asyncio
import time
import datetime

from IP.Trademarks.USPTO_TSDR_API import TSDRApi, format_reg_number


async def main():
    """Simple test of USPTO API response times."""
    
    print("🕐 USPTO API Response Time Test")
    print(f"Started at: {datetime.datetime.now().strftime('%H:%M:%S')}")
    print("-" * 40)
    
    # Test registration numbers
    single_reg = "6181442"
    bulk_regs = ["6181442", "1871376", "5004543", "4083473", "3306673",
                 "1346436", "5546145", "6731985", "6731984", "2854465",
                 "7426341", "5276717", "6731983", "6069963", "1100741",
                 "1075059", "5697522", "2772224", "1314299", "1312269"]
    
    api_client = TSDRApi()
    await api_client.start_session()
    
    try:
        # Test 1: Single certificate
        print("📋 Testing single certificate request...")
        start = time.time()
        single_content = await api_client.get_casedocs_bundle(
            [format_reg_number(single_reg)], 
            id_key='rn', 
            format='zip', 
            option='category=RC'
        )
        single_time = time.time() - start
        single_size = len(single_content) if single_content else 0
        
        print(f"   ✅ Single request: {single_time:.2f} seconds, {single_size:,} bytes")
        
        # Wait for rate limit
        print("   ⏳ Waiting 15 seconds (rate limit)...")
        await asyncio.sleep(15)
        
        # Test 2: Bulk certificates
        print("📦 Testing bulk certificate request (20 certificates)...")
        formatted_bulk = [format_reg_number(reg) for reg in bulk_regs]
        formatted_bulk = [reg for reg in formatted_bulk if reg]
        
        start = time.time()
        bulk_content = await api_client.get_casedocs_bundle(
            formatted_bulk, 
            id_key='rn', 
            format='zip', 
            option='category=RC'
        )
        bulk_time = time.time() - start
        bulk_size = len(bulk_content) if bulk_content else 0
        
        print(f"   ✅ Bulk request: {bulk_time:.2f} seconds, {bulk_size:,} bytes")
        
        # Results
        print("\n📊 RESULTS:")
        print(f"   Single certificate:  {single_time:.2f}s")
        print(f"   20 certificates:     {bulk_time:.2f}s")
        print(f"   Time per cert (bulk): {bulk_time/20:.2f}s")
        
        if bulk_time/20 < single_time:
            speedup = single_time / (bulk_time/20)
            print(f"   🚀 Bulk is {speedup:.1f}x FASTER per certificate!")
        else:
            print(f"   ⚠️  Single requests are faster per certificate")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        await api_client.close_session()
        print(f"\n🏁 Completed at: {datetime.datetime.now().strftime('%H:%M:%S')}")


if __name__ == "__main__":
    asyncio.run(main())
