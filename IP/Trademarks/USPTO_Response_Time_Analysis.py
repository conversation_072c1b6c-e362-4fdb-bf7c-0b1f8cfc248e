"""
USPTO TSDR API Response Time Analysis

This script measures and compares response times for:
1. Single certificate requests
2. Bulk certificate requests (20 certificates in ZIP format)

The goal is to understand USPTO API performance characteristics and optimize
certificate downloading strategies.
"""

import asyncio
import time
import datetime
import os
import statistics
import json
from typing import List, Dict, Tuple
import pandas as pd

from IP.Trademarks.USPTO_TSDR_API import TSDRApi, format_reg_number
from logdata import log_message
from Common import Constants

class USPTOResponseTimeAnalyzer:
    """Analyzes USPTO API response times for different request patterns."""
    
    def __init__(self, output_dir: str = None):
        """
        Initialize the analyzer.
        
        Args:
            output_dir: Directory to save results. Defaults to timestamped folder.
        """
        if output_dir is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            self.output_dir = f"uspto_timing_analysis_{timestamp}"
        else:
            self.output_dir = output_dir
            
        os.makedirs(self.output_dir, exist_ok=True)
        
        self.api_client = None
        self.results = {
            'single_requests': [],
            'bulk_requests': [],
            'metadata': {
                'start_time': None,
                'end_time': None,
                'total_duration': None,
                'api_keys_used': None
            }
        }
    
    async def setup(self):
        """Initialize API client and session."""
        self.api_client = TSDRApi()
        await self.api_client.start_session()
        self.results['metadata']['start_time'] = datetime.datetime.now().isoformat()
        log_message("🚀 USPTO Response Time Analyzer initialized", level='INFO')
    
    async def cleanup(self):
        """Clean up API client session."""
        if self.api_client:
            await self.api_client.close_session()
        self.results['metadata']['end_time'] = datetime.datetime.now().isoformat()
        
        if self.results['metadata']['start_time'] and self.results['metadata']['end_time']:
            start = datetime.datetime.fromisoformat(self.results['metadata']['start_time'])
            end = datetime.datetime.fromisoformat(self.results['metadata']['end_time'])
            self.results['metadata']['total_duration'] = (end - start).total_seconds()
        
        log_message("🏁 USPTO Response Time Analyzer cleanup completed", level='INFO')
    
    async def time_single_certificate_request(self, reg_no: str) -> Dict:
        """
        Time a single certificate request.
        
        Args:
            reg_no: Registration number to request
            
        Returns:
            Dictionary with timing and result information
        """
        formatted_reg_no = format_reg_number(reg_no)
        if not formatted_reg_no:
            return {
                'reg_no': reg_no,
                'formatted_reg_no': None,
                'success': False,
                'error': 'Invalid registration number format',
                'response_time': None,
                'content_size': None
            }
        
        log_message(f"📋 Testing single certificate request for reg_no: {formatted_reg_no}", level='INFO')
        
        start_time = time.time()
        try:
            content = await self.api_client.get_casedocs_bundle(
                [formatted_reg_no], 
                id_key='rn', 
                format='zip', 
                option='category=RC'
            )
            end_time = time.time()
            
            response_time = end_time - start_time
            content_size = len(content) if content else 0
            success = content is not None
            
            result = {
                'reg_no': reg_no,
                'formatted_reg_no': formatted_reg_no,
                'success': success,
                'response_time': response_time,
                'content_size': content_size,
                'timestamp': datetime.datetime.now().isoformat(),
                'error': None if success else 'No content received'
            }
            
            log_message(f"✅ Single request completed: {response_time:.2f}s, {content_size} bytes", level='INFO')
            return result
            
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            
            result = {
                'reg_no': reg_no,
                'formatted_reg_no': formatted_reg_no,
                'success': False,
                'response_time': response_time,
                'content_size': None,
                'timestamp': datetime.datetime.now().isoformat(),
                'error': str(e)
            }
            
            log_message(f"❌ Single request failed: {response_time:.2f}s, error: {e}", level='ERROR')
            return result
    
    async def time_bulk_certificate_request(self, reg_nos: List[str]) -> Dict:
        """
        Time a bulk certificate request (up to 20 certificates).
        
        Args:
            reg_nos: List of registration numbers (max 20)
            
        Returns:
            Dictionary with timing and result information
        """
        if len(reg_nos) > 20:
            reg_nos = reg_nos[:20]
            log_message(f"⚠️ Truncating bulk request to 20 certificates", level='WARNING')
        
        formatted_reg_nos = [format_reg_number(reg_no) for reg_no in reg_nos]
        formatted_reg_nos = [rn for rn in formatted_reg_nos if rn]  # Filter out None values
        
        if not formatted_reg_nos:
            return {
                'reg_nos': reg_nos,
                'formatted_reg_nos': [],
                'count': 0,
                'success': False,
                'error': 'No valid registration numbers',
                'response_time': None,
                'content_size': None
            }
        
        log_message(f"📦 Testing bulk certificate request for {len(formatted_reg_nos)} reg_nos", level='INFO')
        
        start_time = time.time()
        try:
            content = await self.api_client.get_casedocs_bundle(
                formatted_reg_nos, 
                id_key='rn', 
                format='zip', 
                option='category=RC'
            )
            end_time = time.time()
            
            response_time = end_time - start_time
            content_size = len(content) if content else 0
            success = content is not None
            
            result = {
                'reg_nos': reg_nos,
                'formatted_reg_nos': formatted_reg_nos,
                'count': len(formatted_reg_nos),
                'success': success,
                'response_time': response_time,
                'content_size': content_size,
                'timestamp': datetime.datetime.now().isoformat(),
                'error': None if success else 'No content received'
            }
            
            log_message(f"✅ Bulk request completed: {response_time:.2f}s, {content_size} bytes, {len(formatted_reg_nos)} certs", level='INFO')
            return result
            
        except Exception as e:
            end_time = time.time()
            response_time = end_time - start_time
            
            result = {
                'reg_nos': reg_nos,
                'formatted_reg_nos': formatted_reg_nos,
                'count': len(formatted_reg_nos),
                'success': False,
                'response_time': response_time,
                'content_size': None,
                'timestamp': datetime.datetime.now().isoformat(),
                'error': str(e)
            }
            
            log_message(f"❌ Bulk request failed: {response_time:.2f}s, error: {e}", level='ERROR')
            return result

    async def run_single_certificate_analysis(self, reg_nos: List[str], iterations: int = 3) -> None:
        """
        Run multiple single certificate requests for analysis.

        Args:
            reg_nos: List of registration numbers to test
            iterations: Number of iterations per registration number
        """
        log_message(f"🔍 Starting single certificate analysis: {len(reg_nos)} reg_nos, {iterations} iterations each", level='INFO')

        for iteration in range(iterations):
            log_message(f"📊 Single certificate analysis - Iteration {iteration + 1}/{iterations}", level='INFO')

            for reg_no in reg_nos:
                result = await self.time_single_certificate_request(reg_no)
                self.results['single_requests'].append(result)

                # Add delay between requests to respect rate limits
                await asyncio.sleep(15)  # 4 requests per minute = 15 seconds between requests

    async def run_bulk_certificate_analysis(self, reg_nos: List[str], iterations: int = 3) -> None:
        """
        Run multiple bulk certificate requests for analysis.

        Args:
            reg_nos: List of registration numbers to test (will be grouped into batches of 20)
            iterations: Number of iterations per batch
        """
        # Group reg_nos into batches of 20
        batches = [reg_nos[i:i+20] for i in range(0, len(reg_nos), 20)]

        log_message(f"🔍 Starting bulk certificate analysis: {len(batches)} batches of up to 20, {iterations} iterations each", level='INFO')

        for iteration in range(iterations):
            log_message(f"📊 Bulk certificate analysis - Iteration {iteration + 1}/{iterations}", level='INFO')

            for batch_idx, batch in enumerate(batches):
                log_message(f"📦 Processing batch {batch_idx + 1}/{len(batches)} with {len(batch)} certificates", level='INFO')

                result = await self.time_bulk_certificate_request(batch)
                self.results['bulk_requests'].append(result)

                # Add delay between bulk requests to respect rate limits
                await asyncio.sleep(15)  # 4 requests per minute = 15 seconds between requests

    def analyze_results(self) -> Dict:
        """
        Analyze the collected timing results.

        Returns:
            Dictionary with statistical analysis
        """
        analysis = {
            'single_requests': {},
            'bulk_requests': {},
            'comparison': {}
        }

        # Analyze single requests
        single_times = [r['response_time'] for r in self.results['single_requests'] if r['success'] and r['response_time']]
        single_sizes = [r['content_size'] for r in self.results['single_requests'] if r['success'] and r['content_size']]

        if single_times:
            analysis['single_requests'] = {
                'count': len(single_times),
                'success_rate': len(single_times) / len(self.results['single_requests']),
                'avg_response_time': statistics.mean(single_times),
                'median_response_time': statistics.median(single_times),
                'min_response_time': min(single_times),
                'max_response_time': max(single_times),
                'std_response_time': statistics.stdev(single_times) if len(single_times) > 1 else 0,
                'avg_content_size': statistics.mean(single_sizes) if single_sizes else 0,
                'total_content_size': sum(single_sizes) if single_sizes else 0
            }

        # Analyze bulk requests
        bulk_times = [r['response_time'] for r in self.results['bulk_requests'] if r['success'] and r['response_time']]
        bulk_sizes = [r['content_size'] for r in self.results['bulk_requests'] if r['success'] and r['content_size']]
        bulk_counts = [r['count'] for r in self.results['bulk_requests'] if r['success']]

        if bulk_times:
            analysis['bulk_requests'] = {
                'count': len(bulk_times),
                'success_rate': len(bulk_times) / len(self.results['bulk_requests']),
                'avg_response_time': statistics.mean(bulk_times),
                'median_response_time': statistics.median(bulk_times),
                'min_response_time': min(bulk_times),
                'max_response_time': max(bulk_times),
                'std_response_time': statistics.stdev(bulk_times) if len(bulk_times) > 1 else 0,
                'avg_content_size': statistics.mean(bulk_sizes) if bulk_sizes else 0,
                'total_content_size': sum(bulk_sizes) if bulk_sizes else 0,
                'avg_certificates_per_request': statistics.mean(bulk_counts) if bulk_counts else 0,
                'total_certificates': sum(bulk_counts) if bulk_counts else 0
            }

        # Comparison analysis
        if single_times and bulk_times:
            # Calculate time per certificate for bulk requests
            bulk_time_per_cert = []
            for r in self.results['bulk_requests']:
                if r['success'] and r['response_time'] and r['count'] > 0:
                    bulk_time_per_cert.append(r['response_time'] / r['count'])

            if bulk_time_per_cert:
                analysis['comparison'] = {
                    'single_avg_time': analysis['single_requests']['avg_response_time'],
                    'bulk_avg_time_per_cert': statistics.mean(bulk_time_per_cert),
                    'efficiency_ratio': statistics.mean(bulk_time_per_cert) / analysis['single_requests']['avg_response_time'],
                    'time_savings_per_cert': analysis['single_requests']['avg_response_time'] - statistics.mean(bulk_time_per_cert),
                    'bulk_vs_single_speedup': analysis['single_requests']['avg_response_time'] / statistics.mean(bulk_time_per_cert)
                }

        return analysis

    def save_results(self, analysis: Dict = None) -> None:
        """
        Save results and analysis to files.

        Args:
            analysis: Analysis results dictionary
        """
        # Save raw results
        results_file = os.path.join(self.output_dir, 'raw_results.json')
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        log_message(f"💾 Raw results saved to {results_file}", level='INFO')

        # Save analysis if provided
        if analysis:
            analysis_file = os.path.join(self.output_dir, 'analysis.json')
            with open(analysis_file, 'w') as f:
                json.dump(analysis, f, indent=2)
            log_message(f"📊 Analysis saved to {analysis_file}", level='INFO')

        # Create CSV files for easier analysis
        if self.results['single_requests']:
            single_df = pd.DataFrame(self.results['single_requests'])
            single_csv = os.path.join(self.output_dir, 'single_requests.csv')
            single_df.to_csv(single_csv, index=False)
            log_message(f"📋 Single requests CSV saved to {single_csv}", level='INFO')

        if self.results['bulk_requests']:
            bulk_df = pd.DataFrame(self.results['bulk_requests'])
            bulk_csv = os.path.join(self.output_dir, 'bulk_requests.csv')
            bulk_df.to_csv(bulk_csv, index=False)
            log_message(f"📦 Bulk requests CSV saved to {bulk_csv}", level='INFO')

    def print_summary(self, analysis: Dict) -> None:
        """
        Print a summary of the analysis results.

        Args:
            analysis: Analysis results dictionary
        """
        print("\n" + "="*80)
        print("🏆 USPTO API RESPONSE TIME ANALYSIS SUMMARY")
        print("="*80)

        if 'single_requests' in analysis and analysis['single_requests']:
            single = analysis['single_requests']
            print(f"\n📋 SINGLE CERTIFICATE REQUESTS:")
            print(f"   • Total requests: {single['count']}")
            print(f"   • Success rate: {single['success_rate']:.1%}")
            print(f"   • Average response time: {single['avg_response_time']:.2f}s")
            print(f"   • Median response time: {single['median_response_time']:.2f}s")
            print(f"   • Min/Max response time: {single['min_response_time']:.2f}s / {single['max_response_time']:.2f}s")
            print(f"   • Standard deviation: {single['std_response_time']:.2f}s")
            print(f"   • Average content size: {single['avg_content_size']:,.0f} bytes")

        if 'bulk_requests' in analysis and analysis['bulk_requests']:
            bulk = analysis['bulk_requests']
            print(f"\n📦 BULK CERTIFICATE REQUESTS (20 certificates):")
            print(f"   • Total requests: {bulk['count']}")
            print(f"   • Success rate: {bulk['success_rate']:.1%}")
            print(f"   • Average response time: {bulk['avg_response_time']:.2f}s")
            print(f"   • Median response time: {bulk['median_response_time']:.2f}s")
            print(f"   • Min/Max response time: {bulk['min_response_time']:.2f}s / {bulk['max_response_time']:.2f}s")
            print(f"   • Standard deviation: {bulk['std_response_time']:.2f}s")
            print(f"   • Average certificates per request: {bulk['avg_certificates_per_request']:.1f}")
            print(f"   • Average content size: {bulk['avg_content_size']:,.0f} bytes")

        if 'comparison' in analysis and analysis['comparison']:
            comp = analysis['comparison']
            print(f"\n⚖️  COMPARISON ANALYSIS:")
            print(f"   • Single request avg time: {comp['single_avg_time']:.2f}s")
            print(f"   • Bulk request avg time per certificate: {comp['bulk_avg_time_per_cert']:.2f}s")
            print(f"   • Efficiency ratio (bulk/single): {comp['efficiency_ratio']:.2f}")
            print(f"   • Time savings per certificate: {comp['time_savings_per_cert']:.2f}s")
            print(f"   • Bulk speedup factor: {comp['bulk_vs_single_speedup']:.2f}x")

            if comp['efficiency_ratio'] < 1:
                print(f"   🚀 BULK REQUESTS ARE {comp['bulk_vs_single_speedup']:.1f}x FASTER per certificate!")
            else:
                print(f"   ⚠️  Single requests are more efficient per certificate")

        print("\n" + "="*80)


async def run_comprehensive_analysis():
    """
    Run a comprehensive analysis of USPTO API response times.
    """
    # Test registration numbers - mix of real and test numbers
    test_reg_nos = [
        "6181442", "1871376", "5004543", "4083473", "3306673",
        "1346436", "5546145", "6731985", "6731984", "2854465",
        "7426341", "5276717", "6731983", "6069963", "1100741",
        "1075059", "5697522", "2772224", "1314299", "1312269",
        "1390935", "2276097", "7139772", "6294707", "5428677",
        "626035", "902190", "1177400", "1241264", "1241265",
        "1314511", "1347677", "1501898", "1733051", "1734822",
        "2559772", "2964843", "3025936", "3133139", "3134695",
        "3149203", "3890159", "4074269", "4241822", "5100448"
    ]

    analyzer = USPTOResponseTimeAnalyzer()

    try:
        await analyzer.setup()

        # Run single certificate analysis (first 10 reg_nos, 3 iterations each)
        await analyzer.run_single_certificate_analysis(test_reg_nos[:10], iterations=3)

        # Run bulk certificate analysis (all reg_nos, 3 iterations)
        await analyzer.run_bulk_certificate_analysis(test_reg_nos, iterations=3)

        # Analyze results
        analysis = analyzer.analyze_results()

        # Save and display results
        analyzer.save_results(analysis)
        analyzer.print_summary(analysis)

        log_message(f"🎉 Analysis completed! Results saved to: {analyzer.output_dir}", level='INFO')

    except Exception as e:
        log_message(f"💥 Analysis failed: {e}", level='ERROR')
        raise
    finally:
        await analyzer.cleanup()


if __name__ == "__main__":
    print("🚀 Starting USPTO API Response Time Analysis...")
    print("This will test single vs bulk certificate download performance.")
    print("Expected duration: ~15-20 minutes (due to rate limiting)")
    print("-" * 60)

    asyncio.run(run_comprehensive_analysis())
