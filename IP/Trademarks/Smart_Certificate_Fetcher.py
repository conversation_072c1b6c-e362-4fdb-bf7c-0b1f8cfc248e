"""
Smart Certificate Fetcher

Implements adaptive batching strategy based on USPTO API performance analysis:
- Single certificate: 4.12s
- 20 certificates bulk: 51.63s (2.58s per cert)
- Strategy: Batch size of 10 with individual fallback
"""

import asyncio
import time
import datetime
from typing import List, Dict, Tuple
import os

from IP.Trademarks.USPTO_TSDR_API import TSDRApi, format_reg_number
from logdata import log_message


class SmartCertificateFetcher:
    """
    Fetches USPTO certificates using adaptive batching strategy.
    Balances speed vs reliability based on actual API performance data.
    """
    
    def __init__(self, batch_size: int = 10):
        """
        Initialize the smart fetcher.
        
        Args:
            batch_size: Initial batch size (default 10 based on analysis)
        """
        self.batch_size = batch_size
        self.api_client = None
        self.stats = {
            'batch_requests': 0,
            'batch_successes': 0,
            'individual_requests': 0,
            'individual_successes': 0,
            'total_certificates_retrieved': 0,
            'total_time': 0,
            'start_time': None
        }
    
    async def setup(self):
        """Initialize API client."""
        self.api_client = TSDRApi()
        await self.api_client.start_session()
        self.stats['start_time'] = time.time()
        log_message("🚀 Smart Certificate Fetcher initialized", level='INFO')
    
    async def cleanup(self):
        """Clean up API client."""
        if self.api_client:
            await self.api_client.close_session()
        if self.stats['start_time']:
            self.stats['total_time'] = time.time() - self.stats['start_time']
        log_message("🏁 Smart Certificate Fetcher cleanup completed", level='INFO')
    
    async def fetch_certificates_batch(self, reg_nos: List[str]) -> Tuple[bool, bytes]:
        """
        Attempt to fetch a batch of certificates.
        
        Args:
            reg_nos: List of registration numbers
            
        Returns:
            Tuple of (success, content)
        """
        formatted_reg_nos = [format_reg_number(rn) for rn in reg_nos]
        formatted_reg_nos = [rn for rn in formatted_reg_nos if rn]
        
        if not formatted_reg_nos:
            return False, None
        
        try:
            self.stats['batch_requests'] += 1
            start_time = time.time()
            
            content = await self.api_client.get_casedocs_bundle(
                formatted_reg_nos,
                id_key='rn',
                format='zip',
                option='category=RC'
            )
            
            duration = time.time() - start_time
            
            if content:
                self.stats['batch_successes'] += 1
                log_message(f"✅ Batch of {len(formatted_reg_nos)} certificates successful ({duration:.2f}s)", level='INFO')
                return True, content
            else:
                log_message(f"❌ Batch of {len(formatted_reg_nos)} certificates failed - no content returned ({duration:.2f}s)", level='WARNING')
                return False, None
                
        except Exception as e:
            log_message(f"❌ Batch request error for {len(formatted_reg_nos)} certificates: {e}", level='ERROR')
            return False, None
    
    async def fetch_certificate_individual(self, reg_no: str) -> Tuple[bool, bytes]:
        """
        Fetch a single certificate.
        
        Args:
            reg_no: Registration number
            
        Returns:
            Tuple of (success, content)
        """
        formatted_reg_no = format_reg_number(reg_no)
        if not formatted_reg_no:
            return False, None
        
        try:
            self.stats['individual_requests'] += 1
            start_time = time.time()
            
            content = await self.api_client.get_casedocs_bundle(
                [formatted_reg_no],
                id_key='rn',
                format='zip',
                option='category=RC'
            )
            
            duration = time.time() - start_time
            
            if content:
                self.stats['individual_successes'] += 1
                log_message(f"✅ Individual certificate {formatted_reg_no} successful ({duration:.2f}s)", level='INFO')
                return True, content
            else:
                log_message(f"❌ Certificate {formatted_reg_no} not available ({duration:.2f}s)", level='WARNING')
                return False, None
                
        except Exception as e:
            log_message(f"❌ Individual request error for {formatted_reg_no}: {e}", level='ERROR')
            return False, None
    
    async def fetch_certificates_smart(self, reg_nos: List[str], save_dir: str = None) -> Dict:
        """
        Fetch certificates using smart batching strategy.
        
        Args:
            reg_nos: List of registration numbers to fetch
            save_dir: Directory to save certificates (optional)
            
        Returns:
            Dictionary with results and statistics
        """
        log_message(f"🎯 Starting smart certificate fetch for {len(reg_nos)} certificates", level='INFO')
        
        successful_certs = []
        failed_certs = []
        batch_contents = []
        individual_contents = []
        
        # Phase 1: Process in batches
        log_message(f"📦 Phase 1: Processing in batches of {self.batch_size}", level='INFO')
        
        failed_batches = []
        for i in range(0, len(reg_nos), self.batch_size):
            batch = reg_nos[i:i + self.batch_size]
            
            success, content = await self.fetch_certificates_batch(batch)
            
            if success:
                successful_certs.extend(batch)
                batch_contents.append({
                    'reg_nos': batch,
                    'content': content,
                    'size': len(content)
                })
                
                # Save batch content if directory provided
                if save_dir and content:
                    os.makedirs(save_dir, exist_ok=True)
                    batch_filename = f"batch_{i//self.batch_size + 1}_{len(batch)}_certs.zip"
                    batch_path = os.path.join(save_dir, batch_filename)
                    with open(batch_path, 'wb') as f:
                        f.write(content)
                    log_message(f"💾 Saved batch to {batch_path}", level='INFO')
            else:
                failed_batches.extend(batch)
            
            # Rate limit compliance (4 requests per minute = 15 seconds)
            await asyncio.sleep(15)
        
        # Phase 2: Process failed batches individually
        if failed_batches:
            log_message(f"🔄 Phase 2: Processing {len(failed_batches)} certificates individually", level='INFO')
            
            for reg_no in failed_batches:
                success, content = await self.fetch_certificate_individual(reg_no)
                
                if success:
                    successful_certs.append(reg_no)
                    individual_contents.append({
                        'reg_no': reg_no,
                        'content': content,
                        'size': len(content)
                    })
                    
                    # Save individual content if directory provided
                    if save_dir and content:
                        os.makedirs(save_dir, exist_ok=True)
                        individual_filename = f"individual_{reg_no}.zip"
                        individual_path = os.path.join(save_dir, individual_filename)
                        with open(individual_path, 'wb') as f:
                            f.write(content)
                        log_message(f"💾 Saved individual certificate to {individual_path}", level='INFO')
                else:
                    failed_certs.append(reg_no)
                
                # Rate limit compliance
                await asyncio.sleep(15)
        
        # Calculate statistics
        self.stats['total_certificates_retrieved'] = len(successful_certs)
        
        results = {
            'successful_certificates': successful_certs,
            'failed_certificates': failed_certs,
            'batch_contents': batch_contents,
            'individual_contents': individual_contents,
            'statistics': self.stats.copy(),
            'summary': {
                'total_requested': len(reg_nos),
                'total_successful': len(successful_certs),
                'total_failed': len(failed_certs),
                'success_rate': len(successful_certs) / len(reg_nos) if reg_nos else 0,
                'batch_success_rate': self.stats['batch_successes'] / self.stats['batch_requests'] if self.stats['batch_requests'] > 0 else 0,
                'individual_success_rate': self.stats['individual_successes'] / self.stats['individual_requests'] if self.stats['individual_requests'] > 0 else 0
            }
        }
        
        log_message(f"🎉 Smart fetch completed: {len(successful_certs)}/{len(reg_nos)} certificates retrieved", level='INFO')
        return results
    
    def print_summary(self, results: Dict):
        """Print a summary of the fetch results."""
        summary = results['summary']
        stats = results['statistics']
        
        print("\n" + "="*60)
        print("🏆 SMART CERTIFICATE FETCH SUMMARY")
        print("="*60)
        print(f"📊 Overall Results:")
        print(f"   • Total requested: {summary['total_requested']}")
        print(f"   • Total successful: {summary['total_successful']}")
        print(f"   • Total failed: {summary['total_failed']}")
        print(f"   • Success rate: {summary['success_rate']:.1%}")
        print(f"   • Total time: {stats['total_time']:.1f} seconds")
        
        print(f"\n📦 Batch Performance:")
        print(f"   • Batch requests: {stats['batch_requests']}")
        print(f"   • Batch successes: {stats['batch_successes']}")
        print(f"   • Batch success rate: {summary['batch_success_rate']:.1%}")
        
        print(f"\n📋 Individual Performance:")
        print(f"   • Individual requests: {stats['individual_requests']}")
        print(f"   • Individual successes: {stats['individual_successes']}")
        print(f"   • Individual success rate: {summary['individual_success_rate']:.1%}")
        
        if summary['total_successful'] > 0:
            avg_time_per_cert = stats['total_time'] / summary['total_successful']
            print(f"\n⚡ Efficiency:")
            print(f"   • Average time per certificate: {avg_time_per_cert:.2f} seconds")
            
            # Compare to pure individual approach
            estimated_individual_time = summary['total_requested'] * 4.12
            time_saved = estimated_individual_time - stats['total_time']
            print(f"   • Estimated time for all individual: {estimated_individual_time:.1f} seconds")
            print(f"   • Time saved: {time_saved:.1f} seconds ({time_saved/estimated_individual_time:.1%})")
        
        print("="*60)


async def test_smart_fetcher():
    """Test the smart certificate fetcher."""
    # Test with a mix of valid and potentially invalid registration numbers
    test_reg_nos = [
        "6181442", "1871376", "5004543", "4083473", "3306673",
        "1346436", "5546145", "6731985", "6731984", "2854465",
        "7426341", "5276717", "6731983", "6069963", "1100741",
        "1075059", "5697522", "2772224", "1314299", "1312269",
        "626035", "902190", "1177400", "1241264", "1241265"  # 25 total
    ]
    
    fetcher = SmartCertificateFetcher(batch_size=10)
    
    try:
        await fetcher.setup()
        
        # Create output directory
        output_dir = f"smart_fetch_test_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        results = await fetcher.fetch_certificates_smart(test_reg_nos, save_dir=output_dir)
        
        fetcher.print_summary(results)
        
        print(f"\n📁 Results saved to: {output_dir}")
        
    except Exception as e:
        log_message(f"💥 Test failed: {e}", level='ERROR')
        raise
    finally:
        await fetcher.cleanup()


if __name__ == "__main__":
    print("🚀 Testing Smart Certificate Fetcher...")
    asyncio.run(test_smart_fetcher())
