"""
Quick USPTO API Timing Test

Simple script to quickly measure and compare:
1. Single certificate request time
2. 20-certificate bulk ZIP request time

This answers your lead's question about USPTO response times.
"""

import asyncio
import time
import datetime
from typing import List

from IP.Trademarks.USPTO_TSDR_API import TSD<PERSON>pi, format_reg_number
from logdata import log_message


async def test_single_certificate_time(api_client: TSDRApi, reg_no: str) -> dict:
    """Test timing for a single certificate request."""
    formatted_reg_no = format_reg_number(reg_no)
    if not formatted_reg_no:
        return {'error': f'Invalid reg_no: {reg_no}'}
    
    print(f"📋 Testing SINGLE certificate request for reg_no: {formatted_reg_no}")
    
    start_time = time.time()
    try:
        content = await api_client.get_casedocs_bundle(
            [formatted_reg_no], 
            id_key='rn', 
            format='zip', 
            option='category=RC'
        )
        end_time = time.time()
        
        response_time = end_time - start_time
        content_size = len(content) if content else 0
        success = content is not None
        
        result = {
            'reg_no': formatted_reg_no,
            'success': success,
            'response_time': response_time,
            'content_size': content_size,
            'certificates_count': 1
        }
        
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"   {status}: {response_time:.2f} seconds, {content_size:,} bytes")
        return result
        
    except Exception as e:
        end_time = time.time()
        response_time = end_time - start_time
        print(f"   ❌ ERROR: {response_time:.2f} seconds, {str(e)}")
        return {
            'reg_no': formatted_reg_no,
            'success': False,
            'response_time': response_time,
            'error': str(e),
            'certificates_count': 1
        }


async def test_bulk_certificates_time(api_client: TSDRApi, reg_nos: List[str]) -> dict:
    """Test timing for bulk certificate request (20 certificates)."""
    # Limit to 20 certificates as per USPTO API limits
    if len(reg_nos) > 20:
        reg_nos = reg_nos[:20]
    
    formatted_reg_nos = [format_reg_number(reg_no) for reg_no in reg_nos]
    formatted_reg_nos = [rn for rn in formatted_reg_nos if rn]  # Filter out None values
    
    if not formatted_reg_nos:
        return {'error': 'No valid registration numbers provided'}
    
    print(f"📦 Testing BULK certificate request for {len(formatted_reg_nos)} certificates")
    print(f"   Reg numbers: {', '.join(formatted_reg_nos[:5])}{'...' if len(formatted_reg_nos) > 5 else ''}")
    
    start_time = time.time()
    try:
        content = await api_client.get_casedocs_bundle(
            formatted_reg_nos, 
            id_key='rn', 
            format='zip', 
            option='category=RC'
        )
        end_time = time.time()
        
        response_time = end_time - start_time
        content_size = len(content) if content else 0
        success = content is not None
        
        result = {
            'reg_nos': formatted_reg_nos,
            'success': success,
            'response_time': response_time,
            'content_size': content_size,
            'certificates_count': len(formatted_reg_nos),
            'time_per_certificate': response_time / len(formatted_reg_nos) if len(formatted_reg_nos) > 0 else 0
        }
        
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"   {status}: {response_time:.2f} seconds, {content_size:,} bytes")
        print(f"   Time per certificate: {result['time_per_certificate']:.2f} seconds")
        return result
        
    except Exception as e:
        end_time = time.time()
        response_time = end_time - start_time
        print(f"   ❌ ERROR: {response_time:.2f} seconds, {str(e)}")
        return {
            'reg_nos': formatted_reg_nos,
            'success': False,
            'response_time': response_time,
            'error': str(e),
            'certificates_count': len(formatted_reg_nos)
        }


async def run_quick_timing_test():
    """Run the quick timing test comparing single vs bulk requests."""
    
    # Test data - real registration numbers
    test_single_reg_no = "6181442"  # Single test
    test_bulk_reg_nos = [
        "6181442", "1871376", "5004543", "4083473", "3306673",
        "1346436", "5546145", "6731985", "6731984", "2854465",
        "7426341", "5276717", "6731983", "6069963", "1100741",
        "1075059", "5697522", "2772224", "1314299", "1312269"
    ]  # 20 certificates for bulk test
    
    print("🚀 USPTO API TIMING TEST")
    print("=" * 50)
    print(f"Start time: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Initialize API client
    api_client = TSDRApi()
    await api_client.start_session()
    
    try:
        # Test 1: Single certificate request
        print("TEST 1: Single Certificate Request")
        print("-" * 30)
        single_result = await test_single_certificate_time(api_client, test_single_reg_no)
        print()
        
        # Wait between requests to respect rate limits (4 requests per minute = 15 seconds)
        print("⏳ Waiting 15 seconds between requests (rate limit compliance)...")
        await asyncio.sleep(15)
        
        # Test 2: Bulk certificate request (20 certificates)
        print("TEST 2: Bulk Certificate Request (20 certificates)")
        print("-" * 45)
        bulk_result = await test_bulk_certificates_time(api_client, test_bulk_reg_nos)
        print()
        
        # Analysis and comparison
        print("📊 ANALYSIS & COMPARISON")
        print("=" * 50)
        
        if single_result.get('success') and bulk_result.get('success'):
            single_time = single_result['response_time']
            bulk_time = bulk_result['response_time']
            bulk_time_per_cert = bulk_result['time_per_certificate']
            
            print(f"Single certificate request time:     {single_time:.2f} seconds")
            print(f"Bulk request time (20 certificates): {bulk_time:.2f} seconds")
            print(f"Bulk time per certificate:           {bulk_time_per_cert:.2f} seconds")
            print()
            
            # Calculate efficiency
            if bulk_time_per_cert < single_time:
                speedup = single_time / bulk_time_per_cert
                time_saved = single_time - bulk_time_per_cert
                print(f"🚀 BULK REQUESTS ARE MORE EFFICIENT!")
                print(f"   • Speedup factor: {speedup:.2f}x faster per certificate")
                print(f"   • Time saved per certificate: {time_saved:.2f} seconds")
                print(f"   • For 20 certificates:")
                print(f"     - Individual requests would take: {single_time * 20:.1f} seconds")
                print(f"     - Bulk request takes: {bulk_time:.1f} seconds")
                print(f"     - Total time saved: {(single_time * 20) - bulk_time:.1f} seconds")
            else:
                slowdown = bulk_time_per_cert / single_time
                print(f"⚠️  Single requests are more efficient per certificate")
                print(f"   • Bulk requests are {slowdown:.2f}x slower per certificate")
            
            print()
            print(f"Content sizes:")
            print(f"   • Single request: {single_result['content_size']:,} bytes")
            print(f"   • Bulk request: {bulk_result['content_size']:,} bytes")
            print(f"   • Average per certificate in bulk: {bulk_result['content_size'] // bulk_result['certificates_count']:,} bytes")
            
        else:
            print("❌ Cannot perform comparison - one or both tests failed")
            if not single_result.get('success'):
                print(f"   Single request failed: {single_result.get('error', 'Unknown error')}")
            if not bulk_result.get('success'):
                print(f"   Bulk request failed: {bulk_result.get('error', 'Unknown error')}")
        
        print()
        print("=" * 50)
        print(f"Test completed: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"💥 Test failed with error: {e}")
        raise
    finally:
        await api_client.close_session()


if __name__ == "__main__":
    print("Starting quick USPTO timing test...")
    print("This will test single certificate vs 20-certificate bulk request times.")
    print("Expected duration: ~30-45 seconds")
    print()
    
    asyncio.run(run_quick_timing_test())
