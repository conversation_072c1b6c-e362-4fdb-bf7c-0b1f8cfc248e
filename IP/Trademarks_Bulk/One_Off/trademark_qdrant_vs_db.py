import os
import sys
sys.path.append(os.getcwd())
import psycopg2
import psycopg2.extras
from dotenv import load_dotenv
from qdrant_client import QdrantClient, models
from IP.Patents_Bulk.patent_db_grant import get_db_connection

# Load environment variables
load_dotenv()

def get_db_trademarks(conn):
    """
    Fetches all trademark IDs and reg_nos from the database that should be in Qdrant.
    Only trademarks with "mark_feature_code" equal to 2, 3, or 5 are included.
    """
    print("Fetching trademark data from the database...")
    db_trademarks = {}
    
    with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as pre_cursor:
        pre_cursor.execute("SELECT count(*) FROM trademarks WHERE mark_feature_code IN (2, 3, 5);")
        total_trademarks_to_fetch = pre_cursor.fetchone()[0]
        print(f"Total trademarks to fetch from DB: {total_trademarks_to_fetch}")

    with conn.cursor('db_trademarks_cursor', cursor_factory=psycopg2.extras.DictCursor) as cursor:
        cursor.execute("SELECT id, reg_no FROM trademarks WHERE mark_feature_code IN (2, 3, 5);")
        trademarks_processed = 0
        for row in cursor:
            db_trademarks[row['id']] = row['reg_no']
            trademarks_processed += 1
            if trademarks_processed % 50000 == 0:
                print(f"DB progress: {trademarks_processed}/{total_trademarks_to_fetch} trademarks processed.")

    print(f"Found {len(db_trademarks)} trademarks in the database that should be in Qdrant.")
    return db_trademarks

def get_qdrant_trademark_points(client, collection_name):
    """
    Fetches all trademark points from Qdrant.
    """
    print("Fetching trademark points from Qdrant...")
    qdrant_points = {}
    
    count_result = client.count(
        collection_name=collection_name,
        count_filter=models.Filter(
            must=[
                models.FieldCondition(
                    key="ip_type",
                    match=models.MatchValue(value="Trademark")
                )
            ]
        ),
        exact=True
    )
    total_trademark_points = count_result.count
    print(f"Total trademark points to fetch from Qdrant: {total_trademark_points}")
    
    offset = None
    points_fetched = 0
    while True:
        points, next_offset = client.scroll(
            collection_name=collection_name,
            scroll_filter=models.Filter(
                must=[
                    models.FieldCondition(
                        key="ip_type",
                        match=models.MatchValue(value="Trademark")
                    )
                ]
            ),
            limit=50000,
            offset=offset,
            with_payload=True,
            with_vectors=False
        )
        
        points_in_batch = len(points)
        points_fetched += points_in_batch
        if total_trademark_points > 0:
            print(f"Qdrant progress: {points_fetched}/{total_trademark_points} points fetched.")

        for point in points:
            qdrant_points[point.id] = point.payload
        
        if next_offset is None:
            break
        offset = next_offset

    print(f"Found {len(qdrant_points)} trademark points in Qdrant.")
    return qdrant_points

def run_comparison():
    """
    Runs the comparison between database and Qdrant for trademarks.
    """
    conn = None
    try:
        # Get DB connection
        conn = get_db_connection()
        if conn is None:
            print("Could not establish database connection. Exiting.")
            return

        # Get Qdrant client
        qdrant_url = os.environ.get("QDRANT_URL")
        qdrant_api_key = os.environ.get("QDRANT_API_KEY")
        qdrant_collection_name = "IP_Assets"

        if not qdrant_url or not qdrant_api_key:
            print("QDRANT_URL or QDRANT_API_KEY environment variables are not set. Exiting.")
            return

        qdrant_client = QdrantClient(url=qdrant_url, api_key=qdrant_api_key, timeout=60)

        # 1. Get data from DB
        db_trademarks = get_db_trademarks(conn)
        db_trademark_ids = set(db_trademarks.keys())

        # 2. Get data from Qdrant
        qdrant_trademark_points = get_qdrant_trademark_points(qdrant_client, qdrant_collection_name)
        qdrant_trademark_ids = set(qdrant_trademark_points.keys())

        # 3. Perform comparison
        db_trademarks_not_in_qdrant_ids = db_trademark_ids - qdrant_trademark_ids
        qdrant_points_not_in_db_ids = qdrant_trademark_ids - db_trademark_ids

        # 4. Print statistics
        print("\n--- Trademark Data Comparison: PostgreSQL vs. Qdrant ---")
        print(f"Total trademarks in database ('trademarks' table, mark_feature_code IN (2,3,5)): {len(db_trademark_ids)}")
        print(f"Total points in Qdrant ('IP_Assets' collection, ip_type='Trademark'): {len(qdrant_trademark_ids)}")
        print("-" * 60)
        
        print(f"Found {len(db_trademarks_not_in_qdrant_ids)} trademarks in the database that are not in Qdrant.")
        if db_trademarks_not_in_qdrant_ids:
            print("\n10 examples of trademark IDs from DB not in Qdrant:")
            for i, trademark_id in enumerate(list(db_trademarks_not_in_qdrant_ids)[:10]):
                reg_no = db_trademarks.get(trademark_id, "N/A")
                print(f"  - Trademark ID: {trademark_id}, Reg No: {reg_no}")
        
        print("-" * 60)

        print(f"Found {len(qdrant_points_not_in_db_ids)} points in Qdrant that are not in the database.")
        if qdrant_points_not_in_db_ids:
            with_plaintiff_id = []
            without_plaintiff_id = []
            for point_id in qdrant_points_not_in_db_ids:
                payload = qdrant_trademark_points.get(point_id)
                if payload and payload.get('plaintiff_id'):
                    with_plaintiff_id.append(point_id)
                else:
                    without_plaintiff_id.append(point_id)
            
            print(f"\nPoints in Qdrant but not in DB with a plaintiff_id in payload: {len(with_plaintiff_id)}")
            
            print(f"\nPoints in Qdrant but not in DB without a plaintiff_id in payload: {len(without_plaintiff_id)}")
            if without_plaintiff_id:
                print("10 examples of such points:")
                for i, point_id in enumerate(list(without_plaintiff_id)[:10]):
                    reg_no = qdrant_trademark_points[point_id].get("reg_no", "N/A")
                    print(f"  - Point ID: {point_id}, Reg No: {reg_no}")
        print("-" * 60)

    except Exception as e:
        import traceback
        print(f"An error occurred during the comparison: {e}")
        traceback.print_exc()
    finally:
        if conn:
            conn.close()
            print("Database connection closed.")

if __name__ == "__main__":
    run_comparison()