import pandas as pd
import itertools
from DatabaseManagement.ImportExport import get_table_from_GZ
from IP.Trademarks_Bulk.trademark_db import get_db_connection

def get_case_trademarks():
    """
    Fetches trademark registration numbers from the 'tb_case' table.
    Returns a set of registration numbers and a dictionary mapping them to case IDs.
    """
    print("Fetching cases from GZ...")
    cases_df = get_table_from_GZ("tb_case", force_refresh=True)
    
    cases_reg_no = set()
    cases_reg_no_info = {}

    print("Processing cases to extract trademark registration numbers...")
    for _, case_row in cases_df.iterrows():
        trademarks = (case_row.get("images") or {}).get("trademarks")
        if not trademarks:
            continue
            
        for trademark_info in trademarks.values():
            reg_numbers = trademark_info.get("reg_no")
            if not reg_numbers:
                continue

            for reg_no in reg_numbers:
                cleaned_reg_no = str(reg_no).strip()
                if cleaned_reg_no:
                    cases_reg_no.add(cleaned_reg_no)
                    case_id = case_row.get('case_id', 'N/A')
                    cases_reg_no_info.setdefault(cleaned_reg_no, []).append(case_id)
    
    return cases_reg_no, cases_reg_no_info

def get_tro_trademarks_from_db():
    """
    Fetches registration numbers for trademarks with the 'tro' flag set to True.
    """
    print("Fetching TRO trademarks from the database...")
    conn = None
    try:
        conn = get_db_connection()
        # Assuming 'tro' is a boolean field. Use 'IS TRUE' for standard SQL.
        query = "SELECT reg_no FROM trademarks WHERE tro IS TRUE"
        tro_true_df = pd.read_sql(query, conn)
        
        if 'reg_no' in tro_true_df.columns:
            return set(tro_true_df['reg_no'].astype(str).str.strip())
        else:
            print("Warning: 'reg_no' column not found in trademarks table query result.")
            return set()
            
    except Exception as e:
        print(f"Error connecting to the database or fetching data: {e}")
        return set()
    finally:
        if conn:
            conn.close()

def main():
    """
    Main function to compare trademarks and print statistics.
    """
    cases_reg_no, cases_reg_no_info = get_case_trademarks()
    database_reg_no = get_tro_trademarks_from_db()

    in_cases_not_in_db = cases_reg_no - database_reg_no
    in_db_not_in_cases = database_reg_no - cases_reg_no

    print("\n" + "="*50)
    print("STATISTICS")
    print("="*50)
    print(f"Unique registration numbers in cases: {len(cases_reg_no)}")
    print(f"Unique registration numbers in database with TRO=true: {len(database_reg_no)}")
    print("-" * 50)
    print(f"In cases but NOT in database (TRO flag likely missing): {len(in_cases_not_in_db)}")
    print(f"In database but NOT in cases: {len(in_db_not_in_cases)}")
    print("="*50 + "\n")

    print("--- Top 10 Examples: In cases but missing TRO flag in database ---")
    if in_cases_not_in_db:
        for i, reg_no in enumerate(itertools.islice(in_cases_not_in_db, 10)):
            case_ids = cases_reg_no_info.get(reg_no, ['N/A'])
            print(f"{i+1:>2}. Reg No: {reg_no:<15} (Found in case_ids: {case_ids})")
    else:
        print("None found.")

    print("\n--- Top 10 Examples: In database with TRO flag but not in any case ---")
    if in_db_not_in_cases:
        for i, reg_no in enumerate(itertools.islice(in_db_not_in_cases, 10)):
            print(f"{i+1:>2}. Reg No: {reg_no}")
    else:
        print("None found.")

if __name__ == "__main__":
    main()