import asyncio
import os
import time
import pandas as pd

from IP.Trademarks_Bulk.trademark_db import get_db_connection
from logdata import log_message
from Common import Constants
from IP.Trademarks.USPTO_TSDR_API import TSDRApi
from IP.Trademarks.Trademark_API import unzip_and_extract_certificates

CERTIFICATE_FOLDER = os.path.join(Constants.local_ip_folder, "Trademarks", "USPTO_Daily", "Certificates")

async def fetch_missing_certificates():
    # Get trademarks with empty certificate_source
    conn = get_db_connection()
    cur = conn.cursor()
    cur.execute("SELECT reg_no, ser_no FROM trademarks WHERE certificate_source IS NULL OR certificate_source = ''")
    trademarks = cur.fetchall()  # List of (reg_no, ser_no) tuples
    reg_to_ser = {reg_no: ser_no for reg_no, ser_no in trademarks}
    cur.close()
    conn.close()
    
    if not trademarks:
        log_message("No missing certificates to fetch.", level='INFO')
        return
    
    log_message(f"Found {len(trademarks)} trademarks with missing certificates. Starting fetch process.", level='INFO')
        
    api_client = TSDRApi()
    await api_client.start_session() # Start API session
    certificate_needed = []
    
    # Process in batches of 20
    # We need to pass reg_to_ser to process_batch, so we can't just pass reg_nos
    # We will pass the entire trademarks list and let process_batch handle the mapping
    for i in range(0, len(trademarks), 20):
        batch_trademarks = trademarks[i:i + 20]
        log_message(f"Processing batch of {len(batch_trademarks)} certificates.", level='INFO')
        await process_batch(api_client, batch_trademarks, reg_to_ser)

    await api_client.close_session() # Close API session
    log_message("Certificate fetching process completed.", level='INFO')

async def process_batch(api_client, batch_trademarks, reg_to_ser):
    reg_nos = [reg_no for reg_no, _ in batch_trademarks]
    log_message(f"Attempting to fetch certificate bundle for reg_nos: {reg_nos}", level='INFO')
    try:
        zip_content = await api_client.get_casedocs_bundle(reg_nos, id_key='rn', format='zip', option='category=RC')

        if zip_content is None:
            log_message(f"⚠️ No ZIP content received for batch: {reg_nos}. Skipping extraction.", level='WARNING')
            return

        zip_filename = f"reg_cert_multi_{time.time()}.zip"
        zip_path = os.path.join(CERTIFICATE_FOLDER, zip_filename)

        try:
            with open(zip_path, 'wb') as f:
                f.write(zip_content)
            log_message(f"Successfully saved ZIP bundle to {zip_path}", level='INFO')
        except IOError as e:
            log_message(f"🔥❌ Error saving ZIP file {zip_path}: {e}", level='ERROR')
            return

        !!! df_trademarks = pd.DataFrame(batch_trademarks, columns=["reg_no", "ser_no"])
        extracted_reg_nos = unzip_and_extract_certificates(
            df_trademarks,
            zip_path,
            CERTIFICATE_FOLDER
        )

        if extracted_reg_nos:
            conn = get_db_connection()
            cur = conn.cursor()
            for reg_no in extracted_reg_nos:
                cur.execute(
                    "UPDATE trademarks SET certificate_source = %s WHERE reg_no = %s",
                    ("USPTO_RC_ZIP", reg_no)
                )
            conn.commit()
            cur.close()
            conn.close()
            log_message(f"✅ Successfully processed and updated database for extracted reg_nos: {extracted_reg_nos}", level='INFO')
        else:
            log_message(f"⚠️ No certificates were successfully extracted for batch: {reg_nos}. Database not updated for this batch.", level='WARNING')

    except Exception as e:
        log_message(f"🔥❌ An unexpected error occurred while processing batch {reg_nos}: {e}", level='ERROR')

if __name__ == "__main__":
    asyncio.run(fetch_missing_certificates())