"""
This script fetches unique CPC classification section, class, and subclass combinations
associated with a predefined list of TRO utility patents from the database.
It clears the existing data in the 'tro_cpc_classification' table and populates it
with these unique classifications.
"""

import os
import pandas as pd
import psycopg2
import psycopg2.extras
import logging
from IP.Patents_Bulk.patent_db_grant import get_db_connection

tro_utility_patents = [
    "US-07012503-B2", "US-08115136-B2", "US-07012637-B1", "US-07806814-B2", "US-08079388-B2",
    "US-09889065-B2", "US-06919053-B2", "US-08546718-B2", "US-10047903-B2", "US-08541712-B2",
    "US-09578956-B2", "US-09398780-B1", "US-10441098-B2", "US-07398714-B1", "US-08978938-B2",
    "US-10669738-B2", "US-07532200-B2", "US-09737895-B2", "US-08960129-B2", "US-10624728-B2",
    "US-08294542-B2", "US-10349788-B1", "US-11635083-B2", "US-07530706-B2", "US-07819545-B2",
    "US-09661909-B2", "US-09995993-B2", "US-08117706-B2", "US-09522403-B2", "US-08491146-B2",
    "US-11478575-B1", "US-08881775-B2", "US-10653984-B2", "US-10696348-B2", "US-11408585-B1",
    "US-11661947-B2", "US-07374326-B2", "US-11572655-B1", "US-09738437-B2", "US-09285259-B1",
    "US-10556208-B2", "US-10696347-B2", "US-11644271-B1", "US-11719250-B2", "US-09671272-B1",
    "US-11112212-B2", "US-11642205-B2", "US-11981555-B2", "US-08884839-B2", "US-09812876-B2",
    "US-09198261-B2", "US-10082258-B2", "US-10710265-B2", "US-11338736-B2", "US-11351020-B2",
    "US-11445840-B1", "US-11633073-B2", "US-11744256-B2", "US-11920602-B2", "US-07477229-B2",
    "US-10603259-B2", "US-10646419-B2", "US-10684483-B2", "US-10822258-B2", "US-10875158-B2",
    "US-11060269-B2", "US-11143898-B2", "US-11219582-B2", "US-11372252-B2", "US-11409647-B2",
    "US-11478673-B2", "***********-B2", "***********-B2", "***********-B2", "***********-B1",
    "***********-B2", "***********-B1", "***********-B2", "***********-B1", "***********-B1",
    "***********-B2"
]



# Configure logging
log_file_path = os.path.join(os.path.dirname(__file__), '..', '..', 'logs', 'fetch_tro_cpc_classifications.log')
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def populate_tro_cpc_classification_table():
    """
    Fetches unique CPC classification section, class, and subclass for the specified
    tro_utility_patents and populates the tro_cpc_classification table.
    """
    conn = None
    try:
        conn = get_db_connection()
        if conn is None:
            logger.error("Failed to get database connection.")
            return

        conn.autocommit = True # Ensure autocommit is enabled
        cursor = conn.cursor()

        if not tro_utility_patents:
            logger.warning("No 'tro_utility_patents' found. Aborting.")
            cursor.close()
            return

        # SQL query to check if the utility patents exist in patents_records
        sql_query_check_patents = """
            SELECT document_id FROM patents_all WHERE document_id = ANY(%s);
        """
        cursor.execute(sql_query_check_patents, (tro_utility_patents,))
        existing_patents = [row[0] for row in cursor.fetchall()]

        if not existing_patents:
            logger.warning("None of the specified 'tro_utility_patents' found in the 'patents_records' table. Aborting.")
            cursor.close()
            return

        logger.info(f"Found {len(existing_patents)} of {len(tro_utility_patents)} utility patents in patents_records.")

        # Identify patents not found in patent_records
        not_found_patents = [p for p in tro_utility_patents if p not in existing_patents]
        if not_found_patents:
            logger.warning(f"The following patents were not found in 'patents_records': {not_found_patents}")

        # Clear existing data from tro_cpc_classification table
        logger.info("Clearing existing data from tro_cpc_classification table...")

        # SQL query to select unique section, class, subclass for the utility patents
        sql_query_fetch_unique_classifications = """
            SELECT DISTINCT
                pcid.section,
                pcid.class,
                pcid.subclass
            FROM
                patents_all pr
            JOIN
                patents_cpc_ipc_assignments_all pcpa ON pr.id = pcpa.patents_id
            JOIN
                patents_cpc_ipc_definitions pcid ON pcpa.cpc_ipc_id = pcid.id
            WHERE
                pr.document_id = ANY(%s) AND pcid.classification_type = 'CPC';
        """

        logger.info(f"Fetching unique CPC classification data for {len(existing_patents)} existing utility patents...")
        cursor.execute(sql_query_fetch_unique_classifications, (existing_patents,))
        unique_classifications = cursor.fetchall()

        if not unique_classifications:
            logger.warning("No unique CPC classification data found for the specified utility patents.")
            cursor.close()
            return

        # Insert unique classifications into tro_cpc_classification table
        sql_insert_classification = """
            INSERT INTO tro_cpc_classifications (section, class, subclass)
            VALUES (%s, %s, %s)
            ON CONFLICT (section, class, subclass) DO NOTHING; -- Use ON CONFLICT DO NOTHING due to unique constraint
        """
        logger.info(f"Inserting {len(unique_classifications)} unique CPC classifications into tro_cpc_classification table...")
        psycopg2.extras.execute_batch(cursor, sql_insert_classification, unique_classifications)
        logger.info("Unique CPC classifications inserted successfully.")

        cursor.close()

    except psycopg2.DatabaseError as db_err:
        logger.error(f"Database error: {db_err}", exc_info=True)
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}", exc_info=True)
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    populate_tro_cpc_classification_table()