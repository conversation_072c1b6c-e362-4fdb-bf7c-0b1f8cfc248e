# Large Patent Folders CPC Analysis

## Overview

This script (`what_cpc_big_folders_hit.py`) analyzes large patent folders to understand why they were selected for the TRO subset by identifying their matching CPC classifications.

## What the Script Does

1. **Scans Directory Structure**: Looks through the `/xx/yy/` directory structure for patent folders
2. **Identifies Large Folders**: Finds folders larger than 20MB
3. **Extracts Patent Numbers**: Extracts patent registration numbers from folder names (format: `US012345678-xxxxx`)
4. **Queries Database**: Gets CPC assignments for these patents from the database
5. **Filters by TRO Classifications**: Only includes CPC assignments that match TRO CPC classifications
6. **Saves Results**: Outputs analysis to a CSV file

## Directory Structure Expected

The script expects a directory structure like:
```
base_directory/
├── 12/
│   ├── 34/
│   │   ├── US12345678-20240101/
│   │   │   ├── image1.png
│   │   │   ├── image2.png
│   │   │   └── ...
│   │   └── US87654321-20240102/
│   └── 56/
└── 78/
    └── 90/
```

Where:
- `xx/yy/` are the last 4 digits of the patent number
- Each patent has its own folder named `US{patent_number}-{suffix}`
- PNG files are stored within each patent folder

## Database Tables Used

- `patents_all`: Main patent records table
- `patents_cpc_ipc_assignments_all`: Links patents to CPC classifications
- `patents_cpc_ipc_definitions`: CPC classification definitions
- `tro_cpc_classifications`: TRO-specific CPC classifications to match against

## Usage

1. **Prerequisites**:
   - Python environment with required packages (psycopg2, pathlib)
   - Database connection configured (via environment variables)
   - Access to the patent image directory structure

2. **Run the Script**:
   ```bash
   python what_cpc_big_folders_hit.py
   ```

3. **Input Required**:
   - When prompted, enter the base directory path containing the `/xx/yy/` structure

4. **Output**:
   - CSV file: `large_patent_folders_cpc_analysis.csv`
   - Console logs showing progress and summary

## Output CSV Format

The output CSV contains the following columns:

- `patent_reg_no`: Patent registration number
- `folder_path`: Full path to the patent folder
- `folder_size_mb`: Size of the folder in MB
- `patent_title`: Title of the patent
- `date_published`: Publication date
- `patent_type`: Type of patent (B1, B2, etc.)
- `cpc_section`: CPC section (A, B, C, etc.)
- `cpc_class`: CPC class (01, 02, etc.)
- `cpc_subclass`: CPC subclass (A, B, C, etc.)
- `cpc_main_group`: CPC main group
- `cpc_sub_group`: CPC sub group
- `tro_classification_key`: Combined classification key (section+class+subclass)
- `cpc_definition`: Text definition of the CPC classification

## Configuration

You can modify these parameters in the script:

- `SIZE_THRESHOLD_MB`: Change the folder size threshold (default: 20MB)
- `OUTPUT_FILE`: Change the output CSV file name/location

## Notes

- The script is designed to run on a different computer than where it was developed
- It includes comprehensive error handling and logging
- Large directory scans may take time depending on the number of folders
- Database queries are optimized to handle multiple patents efficiently

## Troubleshooting

1. **Database Connection Issues**: Ensure environment variables are set correctly
2. **Directory Not Found**: Verify the base directory path is correct
3. **No Large Folders Found**: Check if the size threshold is appropriate
4. **No CPC Matches**: Verify TRO classifications are populated in the database
