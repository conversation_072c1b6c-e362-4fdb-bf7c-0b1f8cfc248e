# script that processed all the historical CPC and IPC definitions from raw data into structure relational database tables and upsert them into the database.
# There is problem! main_group is lstrip(0) for CPC but not for IPC
import requests
import zipfile
import os
import re
import xml.etree.ElementTree as ET
from pathlib import Path
import logging
import asyncio
from concurrent.futures import ProcessPoolExecutor
import tempfile
import shutil
import psycopg2
from IP.Patents_Bulk.patent_db_grant import get_db_connection

# --- Constants and Configuration ---
INPUT_DIRECTORY = "Documents\cpc_ipc_xml_schemes"
SYMBOL_REGEX_STR = r"^([A-HY])(\d{2})([A-Z])(\d{1,4})/(\d+)$" # Assuming this regex works for both CPC and IPC symbols
MAX_CONCURRENT_XML_PARSERS = 5 # Reduced for lower memory usage

logger = logging.getLogger(__name__)

# --- Function Definitions ---

def unzip_files(zip_path: str, extract_path: str) -> bool:
    """
    Unzips a zip file to a specified extraction path.

    Args:
        zip_path: The path to the zip file.
        extract_path: The directory where the contents will be extracted.

    Returns:
        True if unzipping was successful, False otherwise.
    """
    try:
        os.makedirs(extract_path, exist_ok=True)
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_path)
        return True
    except FileNotFoundError:
        logging.error(f"Error: Zip file not found at {zip_path}")
    except zipfile.BadZipFile:
        logging.error(f"Error: Failed to unzip. The file at {zip_path} may be corrupted or not a zip file.")
    except PermissionError:
        logging.error(f"Error: Permission denied when trying to extract to {extract_path} or read {zip_path}.")
    except IOError as e:
        logging.error(f"IOError occurred during unzipping: {e}")
    except Exception as e:
        logging.error(f"An unexpected error occurred in unzip_files: {e}")
    return False

def parse_symbol(symbol_text: str, compiled_regex: re.Pattern) -> dict | None:
    """
    Parses a classification symbol string using a compiled regular expression.
    (Note: This function is currently not used in the unified parsing logic,
    but kept in case it's needed for future CPC variations).

    Args:
        symbol_text: The classification symbol string (e.g., "C03C27/048").
        compiled_regex: The compiled regular expression pattern for parsing.

    Returns:
        A dictionary with parsed components ("Section", "Class", "Subclass",
        "Main_Group", "Subgroup") if successful, None otherwise.
    """
    if not symbol_text:
        return None
    match = compiled_regex.match(symbol_text.strip())
    if match:
        return {
            "section": match.group(1),
            "class": match.group(2),
            "subclass": match.group(3),
            "main_group": match.group(4),
            "sub_group": match.group(5)
        }
    else:
        logging.debug(f"Symbol '{symbol_text}' did not match regex.")
        return None


def process_xml_file(file_path: str, classification_type: str, publish_date: str) -> list[dict]:
    """
    Parses an XML file to extract classification symbols and their definitions,
    including classification type and publish date, using unified logic.

    Args:
        file_path: The path to the XML file.
        classification_type: The type of classification ('IPC' or 'CPC').
        publish_date: The publication date of the classification version (YYYY-MM-DD).

    Returns:
        A list of dictionaries, where each dictionary contains parsed symbol
        components, definition, classification type, and publish date.
        Returns an empty list if errors occur or no valid items are found.
    """
    file_data: list[dict] = []
    try:
        logging.debug(f"Processing XML file: {file_path} (Type: {classification_type}, Date: {publish_date})")
        tree = ET.parse(file_path)
        root = tree.getroot()

        # Define namespace dictionary
        namespaces = {'ns': 'http://www.wipo.int/classifications/ipc/masterfiles'}

        # Process based on classification type
        if classification_type == 'CPC':
            # CPC specific parsing logic (with namespace and concatenated text)
            for item_element in root.findall(".//ns:ipcEntry", namespaces):
                kind = item_element.get("kind")
                lang = item_element.get("lang")

                # Rule 1: Filtering (applied to all files now)
                # i means index entry
                # g means group reference
                # n means notes of a section
                # t means title for some groups of entries
                if kind in ["i", "g", "n", 't']:
                    continue

                # Rule 2: language filtering - only process English entries
                if lang and lang.lower() != 'en':
                    continue

                item_info: dict[str, any] = {
                    "classification_type": classification_type,
                    "publish_date": publish_date,
                    "section": None,
                    "class": None,
                    "subclass": None,
                    "main_group": None,
                    "sub_group": None,
                    "definition": None
                }


                symbol_text = item_element.get("symbol")

                if not symbol_text:
                    logging.warning(f"Skipping entry with missing symbol in file {file_path}")
                    continue

                # Rule 2: Symbol Parsing (Fixed-Index Slicing) - applied to all files now
                symbol_len = len(symbol_text)
                if symbol_len == 1:
                    item_info["section"] = symbol_text[0]
                elif symbol_len == 3:
                    item_info["section"] = symbol_text[0]
                    item_info["class"] = symbol_text[1:3]
                elif symbol_len == 4:
                    item_info["section"] = symbol_text[0]
                    item_info["class"] = symbol_text[1:3]
                    item_info["subclass"] = symbol_text[3]
                elif symbol_len > 4:
                    item_info["section"] = symbol_text[0]
                    item_info["class"] = symbol_text[1:3]
                    item_info["subclass"] = symbol_text[3]
                    item_info["main_group"] = symbol_text[4:8].lstrip('0')
                    item_info["sub_group"] = symbol_text[8:].rstrip('0')

                    if item_info["sub_group"] == "":
                        item_info["sub_group"] = '00'

                else:
                    logging.warning(f"Skipping entry with malformed symbol '{symbol_text}' in file {file_path}")
                    continue # Skip this entry if symbol is malformed

                definition_text = ""

                first_textBody_obj = item_element.find(".//ns:textBody", namespaces)
                first_text_obj = item_element.find(".//ns:text", namespaces)

                if first_textBody_obj:
                    for text_obj in first_textBody_obj.findall(".//ns:text", namespaces):
                        definition_text += " ".join(text_obj.itertext())
                elif first_text_obj:
                    definition_text += " ".join(first_text_obj.itertext())
                else:
                    # If no textBody or text found, log and skip
                    logging.debug(f"No <textBody> or <text> element found for entry with symbol '{symbol_text}' in file {file_path}")
                    continue

                if definition_text:
                    item_info["definition"] = definition_text.strip() # Strip whitespace
                    file_data.append(item_info)
                else:
                     logging.debug(f"No <text> content found for entry with symbol '{symbol_text}' in file {file_path}")


        else: # IPC specific parsing logic (original logic)
            for item_element in root.findall(".//ipcEntry"):
                kind = item_element.get("kind")
                lang = item_element.get("lang")

                # Rule 1: Filtering (applied to all files now)
                # s means index section
                # g means group reference
                # s means section heading
                # t means title for some groups of entries
                if kind in ["i", "g", "n", "t"]:
                    continue

                # Rule 2: language filtering - only process English entries
                if lang and lang.lower() != 'en':
                    continue

                item_info: dict[str, any] = {
                    "classification_type": classification_type,
                    "publish_date": publish_date,
                    "section": None,
                    "class": None,
                    "subclass": None,
                    "main_group": None,
                    "sub_group": None,
                    "definition": None
                }

                symbol_text = item_element.get("symbol")

                if not symbol_text:
                    logging.warning(f"Skipping entry with missing symbol in file {file_path}")
                    continue

                # Rule 2: Symbol Parsing (Fixed-Index Slicing) - applied to all files now
                symbol_len = len(symbol_text)
                if symbol_len == 1:
                    item_info["section"] = symbol_text[0]
                elif symbol_len == 3:
                    item_info["section"] = symbol_text[0]
                    item_info["class"] = symbol_text[1:3]
                elif symbol_len == 4:
                    item_info["section"] = symbol_text[0]
                    item_info["class"] = symbol_text[1:3]
                    item_info["subclass"] = symbol_text[3]
                elif symbol_len > 4:
                    item_info["section"] = symbol_text[0]
                    item_info["class"] = symbol_text[1:3]
                    item_info["subclass"] = symbol_text[3]
                    item_info["main_group"] = symbol_text[4:8].lstrip('0')
                    item_info["sub_group"] = symbol_text[8:]

                else:
                    logging.warning(f"Skipping entry with malformed symbol '{symbol_text}' in file {file_path}")
                    continue # Skip this entry if symbol is malformed

                definition_text = ""

                first_textBody_obj = item_element.find(".//textBody")
                first_text_obj = item_element.find(".//text")

                if first_textBody_obj:
                    for text_obj in first_textBody_obj.findall(".//text"):
                        definition_text += " ".join(text_obj.itertext())
                elif first_text_obj:
                    definition_text += " ".join(first_text_obj.itertext())
                else:
                    # If no textBody or text found, log and skip
                    logging.debug(f"No <textBody> or <text> element found for entry with symbol '{symbol_text}' in file {file_path}")
                    continue


                if definition_text:
                    item_info["definition"] = definition_text.strip() # Strip whitespace
                    file_data.append(item_info)
                else:
                     logging.debug(f"No <text> content found for entry with symbol '{symbol_text}' in file {file_path}")


        logging.debug(f"Finished processing {file_path}. Found {len(file_data)} items.")

    except ET.ParseError as e:
        logging.error(f"XML ParseError in file {file_path}: {e}")
    except FileNotFoundError:
        logging.error(f"Error: XML file not found at {file_path}")
    except Exception as e:
        logging.error(f"An unexpected error occurred in process_xml_file for {file_path}: {e}")
    return file_data

def upsert_patent_cpc_definitions(cpc_definitions_data: list) -> int:
    """
    Upserts CPC (Cooperative Patent Classification) definitions into the
    patents_cpc_definitions table.

    Args:
        cpc_definitions_data (list): A list of dictionaries, where each dictionary
                                     represents a CPC definition. Expected keys are:
                                     'classification_type', 'publish_date', 'section',
                                     'class', 'subclass', 'main_group',
                                     'sub_group', 'definition'.

    Returns:
        int: The number of CPC definition entries successfully prepared and
             attempted to be upserted.
    """
    if not cpc_definitions_data:
        logger.info("No CPC definitions data provided for upsert.")
        return 0

    conn = None
    cursor = None
    processed_count = 0
    batch_values = []

    # Define the columns for INSERT/UPDATE
    # Table: patents_cpc_definitions (classification_type, publish_date, section, class, subclass, main_group, sub_group, definition)
    # PK: (classification_type, publish_date, section, class, subclass, main_group, sub_group)
    columns = ['classification_type', 'publish_date', 'section', 'class', 'subclass', 'main_group', 'sub_group', 'definition']
    column_names_str = ', '.join(columns)
    placeholders_str = ', '.join(['%s'] * len(columns))

    # On conflict, update the definition
    conflict_target_str = ', '.join(['classification_type', 'publish_date', 'section', 'class', 'subclass', 'main_group', 'sub_group'])
    update_set_str = "definition = EXCLUDED.definition"

    sql = f"""
    INSERT INTO patents_cpc_ipc_definitions ({column_names_str})
    VALUES ({placeholders_str})
    ON CONFLICT ({conflict_target_str})
    DO UPDATE SET {update_set_str};
    """
    logger.debug(f"Upsert CPC Definitions SQL: {sql}")

    try:
        conn = get_db_connection()
        if conn is None:
            logger.error("Failed to get database connection for CPC definitions upsert. Aborting.")
            return 0
        
        cursor = conn.cursor()

        for item in cpc_definitions_data:
            if not isinstance(item, dict):
                logger.warning(f"Skipping non-dictionary item in cpc_definitions_data: {item}")
                continue

            # Prepare data for the row
            row_values = (
                item.get('classification_type'),
                item.get('publish_date'),
                item.get('section'),
                item.get('class'),
                item.get('subclass'),
                item.get('main_group'),
                item.get('sub_group'),
                item.get('definition')
            )

            batch_values.append(row_values)
        
        if not batch_values:
            logger.info("No valid CPC definition data prepared for batch upsert after filtering.")
        else:
            psycopg2.extras.execute_batch(cursor, sql, batch_values, page_size=500)
            conn.commit()
            processed_count = len(batch_values)
            logger.info(f"Successfully prepared and attempted to upsert {processed_count} CPC definition records.")

    except psycopg2.DatabaseError as db_err:
        logger.error(f"Database error during CPC definition upsert: {db_err}")
        if conn:
            conn.rollback()
        processed_count = 0
    except Exception as e:
        logger.error(f"Unexpected error during CPC definition upsert: {e}", exc_info=True)
        if conn:
            conn.rollback()
        processed_count = 0
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
            # logger.info(f"Database connection closed after CPC definition upsert attempt.")
            
    return processed_count




async def main():
    """
    Main asynchronous function to orchestrate the processing of classification
    XML data from a local directory, handling both zipped and unzipped files.
    """
    all_extracted_data: list[dict] = []
    loop = asyncio.get_running_loop()

    date_regex = re.compile(r".*_scheme_(\d{8}).*")
    # compiled_symbol_regex = re.compile(SYMBOL_REGEX_STR) # Not needed in unified parsing


    # 2. List files and prepare for processing (handling zips)
    files_to_process = []
    temp_dirs = [] # To keep track of temporary directories for cleanup
    try:
        if not os.path.exists(INPUT_DIRECTORY) or not os.path.isdir(INPUT_DIRECTORY):
            logging.error(f"Input directory does not exist or is not a directory: {INPUT_DIRECTORY}. Exiting script.")
            return

        for filename in os.listdir(INPUT_DIRECTORY):
            full_file_path = os.path.join(INPUT_DIRECTORY, filename)
            if os.path.isfile(full_file_path):
                classification_type = 'CPC' # Default to CPC
                if filename.startswith('ipc_') or filename.startswith('ipcr_'):
                    classification_type = 'IPC'

                date_match = date_regex.search(filename)
                publish_date = date_match.group(1) if date_match else 'UNKNOWN' # Extract date or use UNKNOWN

                # Handle zip files
                if filename.endswith('.zip'):
                    temp_dir = tempfile.mkdtemp(dir=INPUT_DIRECTORY)
                    temp_dirs.append(temp_dir) # Add to cleanup list
                    if unzip_files(full_file_path, temp_dir):
                        # Find the XML file within the unzipped contents
                        extracted_files = os.listdir(temp_dir)
                        xml_files_in_zip = [f for f in extracted_files if f.endswith('.xml')]

                        if len(xml_files_in_zip) == 1:
                            xml_file_path = os.path.join(temp_dir, xml_files_in_zip[0])
                            files_to_process.append((xml_file_path, classification_type, publish_date))
                        elif len(xml_files_in_zip) > 1:
                            for zip_file in xml_files_in_zip:
                                if zip_file.lower().startswith('en'):
                                    xml_file_path = os.path.join(temp_dir, zip_file)
                                    files_to_process.append((xml_file_path, classification_type, publish_date))
                                    break
                    else:
                        logging.error(f"Failed to unzip {filename}. Skipping.")
                elif filename.endswith('.xml'):
                    files_to_process.append((full_file_path, classification_type, publish_date))
                else:
                    logging.debug(f" ❗ Encountered unsupported file type")

    except FileNotFoundError:
        logging.error(f"Error: Input directory not found at {INPUT_DIRECTORY} during file listing. Exiting script.")
        return
    except Exception as e:
        logging.error(f"An unexpected error occurred during file listing and zip handling: {e}")
        return

    # 3. Process XML files in parallel using ProcessPoolExecutor
    if files_to_process:
        logging.info(f"Starting to process {len(files_to_process)} files using up to {MAX_CONCURRENT_XML_PARSERS} processes.")

        with ProcessPoolExecutor(max_workers=MAX_CONCURRENT_XML_PARSERS) as process_executor:
            tasks = []
            for full_file_path, classification_type, publish_date in files_to_process:
                logging.info(f"Queueing processing for: {os.path.basename(full_file_path)} (Type: {classification_type}, Date: {publish_date})")
                # process_xml_file is a synchronous function, run in executor
                # Pass only necessary arguments to process_xml_file
                task = loop.run_in_executor(process_executor, process_xml_file, full_file_path, classification_type, publish_date)
                tasks.append(task)

            results_from_processes = await asyncio.gather(*tasks, return_exceptions=True)

            processed_file_count = 0
            for i, result in enumerate(results_from_processes):
                file_info = files_to_process[i]
                file_path_processed = file_info[0]
                if isinstance(result, Exception):
                    logging.error(f"Error processing file {os.path.basename(file_path_processed)}: {result}")
                elif result is not None: # process_xml_file returns list[dict] or empty list
                    all_extracted_data.extend(result)
                    logging.info(f"Successfully processed {os.path.basename(file_path_processed)}, extracted {len(result)} items.")
                    processed_file_count +=1
                else:
                    logging.warning(f"Received unexpected None result for {os.path.basename(file_path_processed)}") # Should not happen

            logging.info(f"Finished processing all queued files. {processed_file_count} files processed successfully out of {len(files_to_process)}.")

    # 4. Group data by publish date and process (upsert currently commented out)
    if all_extracted_data:
        # The upsert call is now active, processing all data at once.
        # Call the upsert function with the collected data.
        # The publish_date is expected to be within each item in all_extracted_data.
        upserted_count = upsert_patent_cpc_definitions(all_extracted_data)
        logging.info(f"Upsert operation completed. {upserted_count} records were processed for upsertion.")

    # Clean up temporary directories
    for temp_dir in temp_dirs:
        try:
            shutil.rmtree(temp_dir)
            logging.debug(f"Cleaned up temporary directory: {temp_dir}")
        except OSError as e:
            logging.error(f"Error cleaning up temporary directory {temp_dir}: {e}")

if __name__ == "__main__":

    asyncio.run(main())
