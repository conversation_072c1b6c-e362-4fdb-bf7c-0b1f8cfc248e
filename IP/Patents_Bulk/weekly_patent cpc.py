#!/usr/bin/env python3
"""
Weekly Patent Report

This script handles the downloading, processing, and database loading of USPTO patent data.
It processes weekly Patent Grant Full Text Data (PTGRDT) and monthly Cooperative Patent Classification (CPC) data.

Supported Modes:
- Grants:
    - latest: Process the most recent weekly grant file.
    - catchup: Process grant files from a start date to an end date.
    - specific_date: Process the grant file for a specific Tuesday date.
- CPC:
    - latest: Process the most recent monthly CPC file.
    - catchup: Process CPC files from a start month/year to an end month/year.
    - specific_file: Process a specific CPC file URL or path. # TODO: Implement specific_file if needed
"""

import logging
import datetime
from pathlib import Path
from dotenv import load_dotenv
from Common.Constants import local_ip_folder, nas_ip_folder

from dateutil.relativedelta import relativedelta, MO, TU, WE, TH, FR, SA, SU # For finding specific weekdays


# Load environment variables from .env file
load_dotenv()

# Configure logging
log_file_path = Path(__file__).parent / "weekly_patent_report_cpc.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# --- Constants ---
# Base URLs (API endpoints)
# Grant files are typically released on Tuesdays. Filename format: IYYYYMMDD.zip
PTGRDT_API_BASE_URL = "https://api.uspto.gov/api/v1/datasets/products/files/PTGRDT/{}/I{}.tar"
# CPC files are released monthly, often near the end. Filename format: CPCMCPT_YYYYMMDD.zip (Need to confirm exact format/date)
# Let's assume it uses the release date for now.
CPCMCPT_API_BASE_URL = "https://api.uspto.gov/api/v1/datasets/products/files/CPCMCPT/CPCMCPT_{}.zip" # Placeholder - need confirmation

# Directory structure
BASE_DIR = Path(local_ip_folder) / "Patents"
GRANT_ZIP_DIR = BASE_DIR / "USPTO_Grants" / "Zip"
GRANT_EXTRACT_DIR_BASE = BASE_DIR / "USPTO_Grants" / "Extracted" # Base for extracted content, organized by zip name
CPC_ZIP_DIR = BASE_DIR / "USPTO_CPC" / "Zip"
CPC_EXTRACT_DIR_BASE = BASE_DIR / "USPTO_CPC" / "Extracted" # Base for extracted content, organized by zip name

# Create local directories
GRANT_ZIP_DIR.mkdir(parents=True, exist_ok=True)
GRANT_EXTRACT_DIR_BASE.mkdir(parents=True, exist_ok=True)
CPC_ZIP_DIR.mkdir(parents=True, exist_ok=True)
CPC_EXTRACT_DIR_BASE.mkdir(parents=True, exist_ok=True)

# NAS paths (using f-strings for dynamic creation if needed, ensure base exists)
NAS_BASE_DIR = f"{nas_ip_folder}/Patents"
NAS_GRANT_ZIP_DIR = f"{NAS_BASE_DIR}/USPTO_Grants/Zip"
NAS_GRANT_EXTRACT_BASE = f"{NAS_BASE_DIR}/USPTO_Grants/Extracted"
NAS_CPC_ZIP_DIR = f"{NAS_BASE_DIR}/USPTO_CPC/Zip"
NAS_CPC_EXTRACT_BASE = f"{NAS_BASE_DIR}/USPTO_CPC/Extracted"

# Path for the NAS utility script on the NAS
NAS_UTIL_SCRIPT_DIR_ON_NAS = f"{nas_ip_folder}/Patents" # As per user confirmation
NAS_UNZIP_SCRIPT_NAME = "NAS_Patent_Unzip.py"
# Full path for SSH commands (prefixed with /volume1)
NAS_UNZIP_SCRIPT_PATH_ON_NAS_SSH = f"/volume1{NAS_UTIL_SCRIPT_DIR_ON_NAS}/{NAS_UNZIP_SCRIPT_NAME}"
# Path for SCP (typically relative to NAS user's home or without /volume1)
NAS_UNZIP_SCRIPT_PATH_ON_NAS_SCP = f"{NAS_UTIL_SCRIPT_DIR_ON_NAS}/{NAS_UNZIP_SCRIPT_NAME}"
# Local path to the script
LOCAL_NAS_UNZIP_SCRIPT_PATH = Path(__file__).resolve().parent.parent / "FileManagement" / "NAS_module" / NAS_UNZIP_SCRIPT_NAME


# TODO: Implement robust functions for CPC date calculation if needed
# This likely requires knowing the exact release schedule (e.g., last Thursday)
def get_latest_cpc_release_date(ref_date=None):
    """
    Placeholder: Determines the filename date for the latest CPC file.
    Needs logic based on actual USPTO release schedule (e.g., last Thursday of month).
    """
    if ref_date is None:
        ref_date = datetime.date.today()
    # Example: Assume release is last Thursday of the *previous* month
    first_of_current_month = ref_date.replace(day=1)
    last_day_of_previous_month = first_of_current_month - datetime.timedelta(days=1)
    # Find the last Thursday of that previous month
    last_thursday = last_day_of_previous_month + relativedelta(weekday=TH(-1))
    logger.warning("Using placeholder logic for latest CPC date (last Thursday of previous month). Verify USPTO schedule.")
    return last_thursday

def get_cpc_dates_in_range(start_date, end_date):
    """
    Placeholder: Generates expected CPC release dates within a range.
    Needs logic based on actual USPTO release schedule.
    """
    logger.warning("Using placeholder logic for CPC date range. Verify USPTO schedule.")
    current_target_month = start_date.replace(day=1)
    while current_target_month <= end_date:
        # Example: Assume release is last Thursday of the month
        first_of_next_month = current_target_month + relativedelta(months=1)
        last_day_of_current_month = first_of_next_month - datetime.timedelta(days=1)
        last_thursday = last_day_of_current_month + relativedelta(weekday=TH(-1))
        if last_thursday >= start_date and last_thursday <= end_date:
             yield last_thursday
        current_target_month += relativedelta(months=1)
        
        
async def process_cpc_for_period(start_date, end_date, headers):
    """Processes monthly CPC files within a date range."""
    logger.info(f"Processing CPC files potentially released between {start_date} and {end_date}")
    total_records = 0
    processed_files = 0

    # Use the placeholder function to get potential release dates
    for target_date in get_cpc_dates_in_range(start_date, end_date):
        date_str = target_date.strftime("%Y%m%d")
        # Confirm actual filename format - using placeholder
        zip_filename = f"CPCMCPT_{date_str}.zip"
        zip_url = CPCMCPT_API_BASE_URL.format(date_str)

        logger.info(f"\n=== Processing CPC File for Estimated Date: {target_date} ===")
        try:
            # CPC processing doesn't involve images in the same way grants do
            records, _ = await process_patent_zip(
                zip_url=zip_url,
                zip_filename=zip_filename,
                local_zip_dir=CPC_ZIP_DIR,
                local_extract_base=CPC_EXTRACT_DIR_BASE,
                nas_zip_dir=NAS_CPC_ZIP_DIR,
                nas_extract_base=NAS_CPC_EXTRACT_BASE,
                headers=headers,
                record_type='cpc'
            )
            total_records += records
            if records > 0:
                processed_files += 1
        except Exception as e:
            logger.error(f"Failed to process CPC file {zip_filename} for date {target_date}: {e}", exc_info=True)

    logger.info(f"\n=== CPC Processing Summary ({start_date} to {end_date}) ===")
    logger.info(f"Processed {processed_files} CPC files.")
    logger.info(f"Total records processed: {total_records}")
    logger.info("========================================================")
    return total_records, 0 # Return 0 for images for CPC