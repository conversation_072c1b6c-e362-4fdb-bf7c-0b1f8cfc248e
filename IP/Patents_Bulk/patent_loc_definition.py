import os
import re
import xml.etree.ElementTree as ET
from dotenv import load_dotenv # Ensure this is imported
from IP.Patents_Bulk.patent_db_loc import upsert_locarno_definitions # Import the function

# Constants
# User updated path, ensure it's correct for your environment or adjust.
# Using forward slashes for better cross-platform compatibility internally.
LOCAL_LOC_EDITIONS_PATH = "Documents/IP/Patents/loc_classification_editions" 
TOP_STRUCTURE_FILE_PATTERN = re.compile(r"loc-(\d+)-classification_top_structure.*\.xml")
TEXTS_FILE_PATTERN = re.compile(r"loc-(\d+)-en-classification_texts-(\d{8}|\d{4})\.xml")
NAMESPACE = {'loc': 'http://www.wipo.int/classifications/loc'}

def parse_top_structure(xml_file_path):
    """
    Parses a _top_structure XML file and produces the top_structure_data dictionary.
    """
    try:
        tree = ET.parse(xml_file_path)
        root = tree.getroot()
    except FileNotFoundError:
        print(f"Error: Cannot open XML file: {xml_file_path}. File not found.")
        return None
    except ET.ParseError as e:
        print(f"Error: Cannot parse XML file: {xml_file_path}. Details: {e}")
        return None

    loc_edition = root.get('edition')
    if not loc_edition:
        print(f"Critical Error: Missing 'edition' attribute in root of {xml_file_path}. Skipping this file.")
        return None

    output_data = {
        "loc_edition": loc_edition,
        "classes": {}
    }

    for class_element in root.findall('loc:Class', NAMESPACE):
        class_id = class_element.get('id')
        class_number = class_element.get('classNumber')

        if not class_number:
            class_id_info = f"ID {class_id}" if class_id else "with missing ID"
            print(f"Warning: Missing classNumber for Class {class_id_info} in {xml_file_path}. Skipping this Class.")
            continue
        if not class_id:
            print(f"Warning: Missing id for Class with classNumber {class_number} in {xml_file_path}. Skipping this Class.")
            continue

        subclasses_dict = {}
        for subclass_element in class_element.findall('loc:Subclass', NAMESPACE):
            subclass_id = subclass_element.get('id')
            # Per user feedback, this subclass_number is the specific part, e.g., "01", "12"
            subclass_number_key = subclass_element.get('subclassNumber') 

            if not subclass_number_key:
                subclass_id_info = f"ID {subclass_id}" if subclass_id else "with missing ID"
                print(f"Warning: Missing subclassNumber for Subclass {subclass_id_info} under Class {class_number} in {xml_file_path}. Skipping this Subclass.")
                continue
            if not subclass_id:
                print(f"Warning: Missing id for Subclass with subclassNumber {subclass_number_key} under Class {class_number} in {xml_file_path}. Skipping this Subclass.")
                continue
            
            subclasses_dict[subclass_number_key.strip()] = subclass_id # Store with stripped key
        
        output_data["classes"][class_number.strip()] = { # Store with stripped key
            "mapping_id": class_id,
            "subclasses": subclasses_dict
        }
    return output_data

def parse_texts(texts_xml_file_path, top_structure_data):
    """
    Parses a _texts XML file, using top_structure_data, 
    to produce the texts_output_data dictionary.
    """
    try:
        tree = ET.parse(texts_xml_file_path)
        texts_root = tree.getroot()
    except FileNotFoundError:
        print(f"Error: Cannot open XML file: {texts_xml_file_path}. File not found.")
        return None
    except ET.ParseError as e:
        print(f"Error: Cannot parse XML file: {texts_xml_file_path}. Details: {e}")
        return None

    texts_loc_edition = texts_root.get('edition')
    if not texts_loc_edition:
        print(f"Critical Error: Missing 'edition' attribute in root of {texts_xml_file_path}. Skipping this file.")
        return None

    class_text_map = {}
    subclass_text_map = {}

    for class_texts_element in texts_root.findall('.//loc:ClassTexts', NAMESPACE):
        id_ref = class_texts_element.get('idRef')
        heading_item_element = class_texts_element.find('loc:Heading/loc:HeadingItem', NAMESPACE)
        
        if id_ref and heading_item_element is not None and heading_item_element.text:
            class_text_map[id_ref] = heading_item_element.text.strip()
        else:
            id_ref_info = f"idRef {id_ref}" if id_ref else "missing idRef"
            print(f"Warning: Missing data for ClassTexts entry ({id_ref_info}) in {texts_xml_file_path}.")

    for subclass_texts_element in texts_root.findall('.//loc:SubclassTexts', NAMESPACE):
        id_ref = subclass_texts_element.get('idRef')
        heading_item_element = subclass_texts_element.find('loc:Heading/loc:HeadingItem', NAMESPACE)

        if id_ref and heading_item_element is not None and heading_item_element.text:
            subclass_text_map[id_ref] = heading_item_element.text.strip()
        else:
            id_ref_info = f"idRef {id_ref}" if id_ref else "missing idRef"
            print(f"Warning: Missing data for SubclassTexts entry ({id_ref_info}) in {texts_xml_file_path}.")

    texts_output_data = {
        "loc_edition": texts_loc_edition,
        "class_definitions": {}
    }

    if not top_structure_data or "classes" not in top_structure_data:
        print(f"Error: top_structure_data is missing or malformed for {texts_xml_file_path}. Cannot proceed with text mapping.")
        return None

    for class_number, class_info in top_structure_data["classes"].items():
        class_mapping_id = class_info.get("mapping_id")
        if not class_mapping_id:
            print(f"Warning: Missing 'mapping_id' for class number {class_number} in top_structure_data. Skipping text mapping for this class.")
            continue
            
        class_definition_text = class_text_map.get(class_mapping_id, "Class definition not found")
        if class_definition_text == "Class definition not found":
            print(f"Warning: No text definition found for Class mapping_id {class_mapping_id} (Class Number {class_number}) in {texts_xml_file_path}.")

        subclass_definitions_for_current_class = {}
        if "subclasses" in class_info:
            # Key is specific_subclass_num_key, value is subclass_mapping_id (id from Subclass element)
            for specific_subclass_num_key, subclass_mapping_id_val in class_info["subclasses"].items():
                if not subclass_mapping_id_val:
                    print(f"Warning: Missing mapping_id for subclass number {specific_subclass_num_key} under Class {class_number}. Skipping text mapping for this subclass.")
                    continue

                subclass_definition_text = subclass_text_map.get(subclass_mapping_id_val, "Subclass definition not found")
                if subclass_definition_text == "Subclass definition not found":
                    print(f"Warning: No text definition found for Subclass mapping_id {subclass_mapping_id_val} (Subclass Number {specific_subclass_num_key} under Class {class_number}) in {texts_xml_file_path}.")
                
                # Key is specific_subclass_num_key (e.g., "01", "12")
                subclass_definitions_for_current_class[specific_subclass_num_key] = subclass_definition_text 
        
        texts_output_data["class_definitions"][class_number] = {
            "class_definition": class_definition_text,
            "subclasses": subclass_definitions_for_current_class
        }
    return texts_output_data

def process_locarno_data():
    """
    Main function to discover XML file pairs, manage the processing flow,
    and call the parsing functions.
    Reads files directly from the specified local directory, parses them,
    and upserts the data into the PostgreSQL database.
    """
    load_dotenv() 

    current_local_path = LOCAL_LOC_EDITIONS_PATH.replace("\\", "/")
    # Ensure the path is absolute or correctly relative to the script's execution context
    # If LOCAL_LOC_EDITIONS_PATH is like "Documents/...", it's relative to CWD.
    # For robustness, one might consider making it absolute if issues arise.
    # For now, assume it's a valid relative path from where the script is run.

    if not os.path.isdir(current_local_path):
        print(f"Error: The local directory '{current_local_path}' does not exist or is not accessible.")
        print("Please ensure the XML files are placed in this directory.")
        return

    all_files_in_local_dir = []
    try:
        all_files_in_local_dir = [f for f in os.listdir(current_local_path) if os.path.isfile(os.path.join(current_local_path, f))]
    except OSError as e:
        print(f"Error: Could not list files in directory '{current_local_path}'. Details: {e}")
        return
            
    if not all_files_in_local_dir:
        print(f"No files found in local directory '{current_local_path}'. Exiting.")
        return

    texts_by_edition = {}
    for filename in all_files_in_local_dir:
        match = TEXTS_FILE_PATTERN.match(filename)
        if match:
            edition_number = match.group(1)
            if edition_number in texts_by_edition:
                print(f"Warning: Multiple text files found for edition {edition_number}. Using the last one encountered: {filename}. Previous: {os.path.basename(texts_by_edition[edition_number])}")
            texts_by_edition[edition_number] = os.path.join(current_local_path, filename)

    file_pairs_to_process = []
    processed_top_structures = set()

    for filename in all_files_in_local_dir:
        match = TOP_STRUCTURE_FILE_PATTERN.match(filename)
        if match:
            edition_number = match.group(1)
            top_structure_file_path = os.path.join(current_local_path, filename)
            
            if edition_number in processed_top_structures:
                continue

            if edition_number in texts_by_edition:
                texts_file_path = texts_by_edition[edition_number]
                file_pairs_to_process.append((top_structure_file_path, texts_file_path, edition_number))
            else:
                print(f"Warning: Texts file for edition {edition_number} (corresponding to '{filename}') not found in '{current_local_path}'. Skipping this edition.")
            processed_top_structures.add(edition_number)
    
    if not file_pairs_to_process:
        print(f"No valid file pairs found to process in '{current_local_path}'.")
        return

    all_editions_data = []

    for top_path, texts_path, edition_num_from_filename in file_pairs_to_process:
        print(f"Processing edition {edition_num_from_filename} from local files...")
        print(f"  Top Structure File: {os.path.basename(top_path)}")
        print(f"  Texts File: {os.path.basename(texts_path)}")

        top_structure_data = parse_top_structure(top_path)
        if top_structure_data is None:
            print(f"Error: Failed to parse top structure file {os.path.basename(top_path)}. Skipping edition {edition_num_from_filename}.")
            continue
        
        actual_edition = top_structure_data.get("loc_edition")
        if not actual_edition:
            print(f"Critical Error: 'loc_edition' not found in top_structure_data for {os.path.basename(top_path)}. Using filename edition '{edition_num_from_filename}' as fallback.")
            actual_edition = edition_num_from_filename
        elif actual_edition != edition_num_from_filename:
            print(f"Info: Using edition '{actual_edition}' from XML content of {os.path.basename(top_path)}, filename suggested '{edition_num_from_filename}'.")

        texts_output_data = parse_texts(texts_path, top_structure_data)
        if texts_output_data is None:
            print(f"Error: Failed to parse texts file {os.path.basename(texts_path)} for edition {actual_edition}. This edition's text data will be incomplete.")
            continue
        
        if texts_output_data.get("loc_edition") != actual_edition:
             print(f"Warning: Edition number mismatch between top structure XML ({actual_edition}) and texts XML ({texts_output_data.get('loc_edition')}). Forcing texts_output_data to use edition: {actual_edition}.")
             texts_output_data["loc_edition"] = actual_edition

        print(f"Successfully parsed data for edition {actual_edition}.")
        all_editions_data.append({
            "edition_from_filename": edition_num_from_filename,
            "final_texts_output": texts_output_data
        })

    if not all_editions_data:
        print("\nNo editions were successfully parsed to generate data.")
        return
        
    print(f"\nSuccessfully parsed data for {len(all_editions_data)} edition(s) from files.")

    records_for_db = []
    for edition_entry in all_editions_data:
        parsed_data = edition_entry.get('final_texts_output') 
        if not parsed_data:
            print(f"Warning: Missing 'final_texts_output' for filename group {edition_entry.get('edition_from_filename')}. Skipping DB record generation.")
            continue

        db_loc_edition = parsed_data.get('loc_edition')
        if not db_loc_edition:
            print(f"Critical Warning: Skipping data entry due to missing 'loc_edition' in final_texts_output for {edition_entry.get('edition_from_filename')}")
            continue

        for class_num_key, class_data_dict in parsed_data.get('class_definitions', {}).items():
            # class_num_key is the parent class number string, e.g., "1", "13"
            parent_class_definition = class_data_dict.get('class_definition', '') 

            # The key 'specific_subclass_num_key' is the direct subclass number, e.g., "01", "1", "12"
            for specific_subclass_num_key, subclass_text_val in class_data_dict.get('subclasses', {}).items():
                parent_class_padded = class_num_key.strip().zfill(2)
                
                # Validate specific_subclass_num_key: should be a string of digits
                cleaned_specific_subclass_key = specific_subclass_num_key.strip()
                if not cleaned_specific_subclass_key.isdigit():
                    print(f"Warning: Invalid subclass key '{specific_subclass_num_key}' for class '{class_num_key}'. Expected a digit string. Skipping this subclass.")
                    continue
                
                specific_subclass_padded = cleaned_specific_subclass_key.zfill(2)
                db_loc_code = parent_class_padded + specific_subclass_padded

                records_for_db.append({
                    "loc_edition": db_loc_edition,
                    "loc_code": db_loc_code,
                    "class_text": class_num_key.strip(),  # Store the parent class number
                    "class_definition_text": parent_class_definition,
                    "subclass_text": cleaned_specific_subclass_key, # Store the specific subclass number
                    "subclass_definition_text": subclass_text_val
                })
    
    if records_for_db:
        print(f"\nPrepared {len(records_for_db)} records for database upsert.")
        try:
            upsert_result = upsert_locarno_definitions(records_for_db)
            if upsert_result >= 0: 
                print(f"Database upsert attempt completed. Processed/Attempted: {upsert_result} records.")
            else:
                print("Database upsert failed (connection error or other critical issue in DB function).")
        except Exception as e_db:
            print(f"Error during database operations call: {e_db}")
            import traceback
            traceback.print_exc()
    else:
        print("\nNo records prepared for database upsert.")

if __name__ == "__main__":
    print("Starting Locarno Classification Data Processor...")
    process_locarno_data()
    print("Processing finished.")