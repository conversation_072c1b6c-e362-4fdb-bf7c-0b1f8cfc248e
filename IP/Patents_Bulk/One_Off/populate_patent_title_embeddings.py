import os
import sys
from dotenv import load_dotenv
from qdrant_client import QdrantClient, models
from sentence_transformers import SentenceTransformer
import tqdm
import numpy as np

# Add parent directory to path to import modules
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

from Common.uuid_utils import generate_uuid
from IP.Patents_Bulk.patent_db_grant import get_db_connection

load_dotenv()

COLLECTION_NAME = "Patent_Title_bge_small"
MODEL_NAME = 'BAAI/bge-small-en-v1.5'

def get_qdrant_client():
    """Initializes and returns the Qdrant client."""
    return QdrantClient(
        url=os.environ.get("QDRANT_URL"), 
        api_key=os.environ.get("QDRANT_API_KEY"),
        timeout=90,
        https=True
    )

def create_collection_if_not_exists(client: QdrantClient):
    """Creates the Qdrant collection if it doesn't already exist."""
    try:
        client.get_collection(collection_name=COLLECTION_NAME)
        print(f"Collection '{COLLECTION_NAME}' already exists.")
    except Exception:
        print(f"Collection '{COLLECTION_NAME}' not found. Creating new collection...")
        client.create_collection(
            collection_name=COLLECTION_NAME,
            vectors_config=models.VectorParams(size=384, distance=models.Distance.COSINE),
        )
        print(f"Collection '{COLLECTION_NAME}' created successfully.")

def get_existing_point_ids(client: QdrantClient) -> set:
    """Fetches all existing point IDs from the collection."""
    print("Retrieving existing point IDs from Qdrant...")
    existing_ids = set()
    next_offset = 0
    while next_offset is not None:
        records, next_offset = client.scroll(
            collection_name=COLLECTION_NAME,
            limit=50000,
            with_payload=False,
            with_vectors=False,
            offset=next_offset
        )
        for record in records:
            existing_ids.add(record.id)
    print(f"Found {len(existing_ids)} existing point IDs.")
    return existing_ids


def get_all_patents(conn):
    """Fetches all patent registration numbers and titles from the database."""
    with conn.cursor() as cur:
        cur.execute("SELECT reg_no, patent_title FROM patents WHERE patent_title IS NOT NULL;")
        patents = cur.fetchall()
    return patents

def update_vector_store(qdrant_client: QdrantClient, model: SentenceTransformer):
    """Creates embeddings for new patents and uploads them to Qdrant."""
    existing_ids = get_existing_point_ids(qdrant_client)
    
    db_conn = None
    try:
        db_conn = get_db_connection()
        patents = get_all_patents(db_conn)
        
        if not patents:
            print("No patents found in the database.")
            return

        print(f"Found {len(patents)} patents in the database.")
        patents_to_process = [p for p in patents if generate_uuid(p[0]) not in existing_ids]

        if not patents_to_process:
            print("All patent titles are already embedded and stored.")
        else:
            batch_size = 1000
            points_to_upsert = []

            print(f"Processing {len(patents_to_process)} new patents...")
            for i in tqdm.tqdm(range(0, len(patents_to_process), batch_size)):
                batch = patents_to_process[i:i+batch_size]
                reg_nos = [p[0] for p in batch]
                titles = [p[1] for p in batch]

                embeddings = model.encode(titles, normalize_embeddings=True, show_progress_bar=False)
                
                for reg_no, embedding in zip(reg_nos, embeddings):
                    point_id = generate_uuid(reg_no)
                    points_to_upsert.append(
                        models.PointStruct(
                            id=point_id,
                            vector=embedding.tolist(),
                            payload={"reg_no": reg_no}
                        )
                    )

                if points_to_upsert:
                    qdrant_client.upsert(
                        collection_name=COLLECTION_NAME,
                        points=points_to_upsert,
                        wait=True
                    )
                    points_to_upsert = []
            print("Finished uploading patent title embeddings.")

    except Exception as e:
        print(f"An error occurred during vector store update: {e}")
    finally:
        if db_conn:
            db_conn.close()

def retrieval_test(qdrant_client: QdrantClient, model: SentenceTransformer):
    """Tests the collection with a sample query and checks a specific patent."""
    print("\n--- Starting Retrieval Test ---")
    try:
        # Test the collection
        print("Testing the collection with a sample query...")
        test_query = "The Original Little ELF Gift Wrap Cutter (2 Pack) | As Seen On Shark Tank | Wrapping Paper Cutter"
        query_embedding = model.encode(test_query, normalize_embeddings=True)

        search_result = qdrant_client.search(
            collection_name=COLLECTION_NAME,
            query_vector=query_embedding,
            limit=50
        )

        print("Search results:")
        for result in search_result:
            print(f"  - ID: {result.id}, Score: {result.score}, Payload: {result.payload}")

        # Find similarity with a specific patent
        print("\nChecking similarity for reg_no = D1018662...")
        reg_no_to_check = 'D1018662'
        point_id_to_check = generate_uuid(reg_no_to_check)

        # Retrieve the vector for the specific point
        retrieved_points = qdrant_client.retrieve(
            collection_name=COLLECTION_NAME,
            ids=[point_id_to_check],
            with_vectors=True
        )

        if retrieved_points:
            retrieved_vector = np.array(retrieved_points[0].vector)
            similarity = np.dot(query_embedding, retrieved_vector)
            print(f"Similarity score for reg_no {reg_no_to_check} (ID: {point_id_to_check}): {similarity}")
        else:
            print(f"Point for reg_no {reg_no_to_check} (ID: {point_id_to_check}) not found in the collection.")

    except Exception as e:
        print(f"An error occurred during retrieval test: {e}")
    finally:
        print("--- Retrieval Test Finished ---")


def main():
    """Main function to run the vector store update and retrieval test."""
    qdrant_client = get_qdrant_client()
    create_collection_if_not_exists(qdrant_client)
    
    model = SentenceTransformer(MODEL_NAME)
    
    update_vector_store(qdrant_client, model)
    retrieval_test(qdrant_client, model)

if __name__ == "__main__":
    main()