 # IP/Patents/patent_db.py
"""
Patent Database Operations Module

This module provides functions to interact with the PostgreSQL database
for patent grant and CPC data.
"""

import os
import psycopg2
import psycopg2.extras
import logging
from tqdm import tqdm
from patent_db_grant import get_db_connection


# Configure logging
log_file_path = os.path.join(os.path.dirname(__file__), '..', '..', 'logs', 'patent_db.log')
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


def upsert_locarno_definitions(locarno_definition_records: list) -> int:
    """
    Upserts Locarno classification definitions into the patents_loc_definitions table.

    Args:
        locarno_definition_records (list): A list of dictionaries, where each dictionary
                                     represents a Locarno definition record.
                                     Expected keys in each dict:
                                     'loc_edition', 'loc_code',
                                     'class_text', 'class_definition_text',
                                     'subclass_text', 'subclass_definition_text'.

    Returns:
        int: The number of records successfully upserted or -1 if connection failed.
    """
    if not locarno_definition_records:
        logger.info("No Locarno definition records provided for upsert.")
        return 0

    conn = None
    cursor = None
    upserted_count = 0

    # Column names from schema: loc_edition, loc_code, class, class_defintion, subclass, subclass_defintion
    # Note the typos in DB column names: class_defintion, subclass_defintion from Qdrant/setup_postgres.py
    # These must match the actual table schema.
    # The patents_loc_definitions table does NOT have create_time/update_time columns.
    sql_upsert_final = """
    INSERT INTO patents_loc_definitions (
        loc_edition, loc_code,
        class, class_defintion,
        subclass, subclass_defintion
    ) VALUES (
        %(loc_edition)s, %(loc_code)s,
        %(class_text)s, %(class_definition_text)s,
        %(subclass_text)s, %(subclass_definition_text)s
    )
    ON CONFLICT (loc_edition, loc_code) DO UPDATE SET
        class = EXCLUDED.class,
        class_defintion = EXCLUDED.class_defintion,
        subclass = EXCLUDED.subclass,
        subclass_defintion = EXCLUDED.subclass_defintion;
    """

    try:
        conn = get_db_connection()
        if conn is None:
            logger.error("Failed to get database connection for Locarno definitions upsert. Aborting.")
            return -1 # Indicate connection failure

        cursor = conn.cursor()

        # Map dict keys to expected placeholders in the SQL query
        # The input dict keys are: loc_edition, loc_code, class_text, class_definition_text, subclass_text, subclass_definition_text
        # These match the placeholders %(key_name)s

        psycopg2.extras.execute_batch(cursor, sql_upsert_final, locarno_definition_records, page_size=500)
        conn.commit()
        # rowcount for execute_batch is not straightforward for upserts.
        # We'll count the number of records we attempted to process.
        upserted_count = len(locarno_definition_records)
        logger.info(f"Successfully prepared and attempted to upsert {upserted_count} Locarno definition records.")

    except psycopg2.DatabaseError as db_err:
        logger.error(f"Database error during Locarno definition upsert: {db_err}")
        if conn:
            conn.rollback()
        upserted_count = 0
    except Exception as e:
        logger.error(f"Unexpected error during Locarno definition upsert: {e}", exc_info=True)
        if conn:
            conn.rollback()
        upserted_count = 0
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
            # logger.info("Database connection closed after Locarno definition upsert attempt.")

    return upserted_count