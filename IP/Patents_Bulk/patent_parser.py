# IP/Patents/patent_parser.py

import os
import logging
import multiprocessing
import atexit
import platform
from datetime import datetime
from lxml import etree
import re
import cv2
from typing import Optional
import numpy as np
import functools
from Common.uuid_utils import generate_uuid
import psycopg2
from pathlib import Path # Import Path for directory handling

RESIZE_IMAGE_SIZE = 512  # Default size for resizing images

def _get_or_create_patent_img_destination_directory(patent_num: str, base_dir: str, image_folder_name: str) -> Optional[Path]:
    """
    take in a patent_number, calculates the destination
    subdirectory (e.g., base_dir/XX/YY), creates it, and returns its path.
    """
    dir_match = re.search(r'(\d{2})(\d{2})$', patent_num)
    if not dir_match:
        logging.warning(f"Could not determine directory structure from patent number: {patent_num}")
        return None
    yy, xx = dir_match.groups()
    final_sub_dir = Path(base_dir) / xx / yy / image_folder_name
    final_sub_dir.mkdir(parents=True, exist_ok=True)
    return final_sub_dir

from IP.Patents_Bulk.patent_db_uspc import validate_uspc_classification, load_uspc_definitions_cache, set_uspc_definitions_cache
from IP.Patents_Bulk.patent_parser_cpc_ipc_match import match_cpc_ipc, load_cpc_ipc_definitions_cache, set_cpc_ipc_definitions_df
from IP.Patents_Bulk.patent_db_grant import get_db_connection

def get_tro_cpc_ipc_classifications():
    """"
    Connecting with the database and retriveing the TRO CPC calassifications.
    """
    conn = None
    try:
        conn = get_db_connection()
        conn.autocommit = True # Ensure autocommit is enabled
        cursor = conn.cursor()

        sql_query_retrive_tro_classifications = """
            SELECT section, class, subclass, main_group FROM tro_cpc_classifications;
        """
        cursor.execute(sql_query_retrive_tro_classifications)
        return {"".join(row) for row in cursor.fetchall()}

    except psycopg2.DatabaseError as db_err:
        print(f"Database error: {db_err}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred: {e}", exc_info=True)
        return None
    finally:
        if conn:
            conn.close()


# Assuming Common.cpu_count exists and provides get_allocated_cpus
try:
    from Common.cpu_count import get_allocated_cpus
except ImportError:
    logging.warning("Common.cpu_count not found. Falling back to os.cpu_count().")
    def get_allocated_cpus(percentage=0.75):
        count = os.cpu_count()
        if count is None:
            return 1 # Fallback
        return max(1, int(count * percentage))

# --- Logging Setup ---
log_file_path = os.path.join(os.path.dirname(__file__), '..', '..', 'logs', 'patent_parser.log')
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)
logging.basicConfig(
    level=logging.DEBUG, # Changed to DEBUG to see detailed logs
    format='%(asctime)s - %(levelname)s - %(processName)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path),
        logging.StreamHandler()
    ]
)

# --- Multiprocessing Pool Management ---
_xml_parser_pool = None
_tro_cpc_ipc_classifications = None # Global variable to hold TRO CPC/IPC classifications in workers

def _worker_init(uspc_df, cpc_ipc_df, tro_cpc_ipc_classifications_data):
    """Initializer function for multiprocessing workers.
    Loads the USPC, CPC/IPC, and TRO CPC/IPC definitions cache into each worker process."""
    global _tro_cpc_ipc_classifications
    set_uspc_definitions_cache(uspc_df)
    set_cpc_ipc_definitions_df(cpc_ipc_df)
    _tro_cpc_ipc_classifications = tro_cpc_ipc_classifications_data
    logging.debug(f"USPC, CPC/IPC, and TRO CPC/IPC definitions cache loaded in worker process {os.getpid()}.")

def get_xml_parser_pool(initializer=None, initargs=None):
    """Initializes and returns the global multiprocessing pool."""
    global _xml_parser_pool
    if _xml_parser_pool is None:
        num_cpus = get_allocated_cpus()
        logging.info(f"Initializing multiprocessing pool with {num_cpus} workers.")
        # Use 'spawn' start method for better cross-platform compatibility, especially on Windows/macOS
        start_method = 'spawn' if platform.system() != 'Linux' else None
        context = multiprocessing.get_context(start_method)
        _xml_parser_pool = context.Pool(processes=num_cpus, initializer=initializer, initargs=initargs)
        logging.info("Multiprocessing pool initialized.")
    return _xml_parser_pool

def shutdown_xml_parser_pool():
    """Shuts down the global multiprocessing pool."""
    global _xml_parser_pool
    if _xml_parser_pool:
        logging.info("Shutting down multiprocessing pool...")
        _xml_parser_pool.close()
        _xml_parser_pool.join()
        _xml_parser_pool = None
        logging.info("Multiprocessing pool shut down.")

# Register shutdown function to be called on script exit
atexit.register(shutdown_xml_parser_pool)

# --- XML Helper Functions ---
def get_element_text(element, xpath, namespaces=None, default=''):
    """Safely extracts text content from an XML element using XPath."""
    try:
        result = element.xpath(xpath, namespaces=namespaces)
        # Handle cases where xpath might return multiple elements or attributes
        if isinstance(result, list):
            if not result:
                return default
            # If the result is a list of elements, get text from the first one
            # If the result is a list of attribute values (strings), join them (though usually we expect one)
            item = result[0]
            if hasattr(item, 'text'):
                return item.text.strip() if item.text else default
            elif isinstance(item, str):
                return item.strip() # It's an attribute value
            else:
                return default # Unexpected type
        elif isinstance(result, str):
             return result.strip() # Direct attribute value
        elif hasattr(result, 'text'): # Single element result
            return result.text.strip() if result.text else default
        else:
            return default # Handle other unexpected types if necessary
    except Exception as e:
        # Log the error but don't crash the parsing for one missing element
        # logging.debug(f"Error getting text for xpath '{xpath}': {e}")
        return default

# Removed @profile decorator
def get_element_date(element, xpath, namespaces=None, date_format='%Y%m%d', default=None):
    """Safely extracts a date string from an XML element."""
    date_str = get_element_text(element, xpath, namespaces=namespaces)
    if date_str:
        try:
            # Parse the date string using the expected format
            date_obj = datetime.strptime(date_str, date_format).date()
            # Format the date object into YYYY-MM-DD string
            return date_obj.strftime('%Y-%m-%d')
        except ValueError:
            # Log a warning if parsing fails and return the default value
            logging.warning(f"Could not parse date string '{date_str}' with format '{date_format}' for xpath '{xpath}'. Returning default.")
            return default
    return default # Return default (None) if no string found

# --- Worker Functions (Single File Parsers) ---

# Note: determine_and_parse_xml_chunk removed as type is known beforehand by the caller.
# Removed @profile decorator
def parse_grant_xml_chunk(xml_file_path, mode, dest_dir):
    """
    Parses a single patent grant XML file using iterparse.
    Assumes standard namespaces.

    Args:
        xml_file_path (str): The path to the XML file.
        # namespaces (dict): Dictionary of XML namespaces. (Removed - using standard)

    Returns:
        list: A list of parsed grant record dictionaries, or None on critical error.
    """
    records = []
    processed_records_in_file = 0
    # Define standard namespaces expected in grant files
    namespaces = {
        'uspat': 'urn:us:gov:doc:uspto:common',
        'pat': 'urn:us:gov:doc:uspto:patent',
        # Add others if commonly found and needed for parsing
    }

    logging.debug(f"Starting Grant parsing for: {xml_file_path}")
    try:
        # Use iterparse with 'end' event only for processing grant elements
        context = etree.iterparse(xml_file_path, events=('end',), recover=True, huge_tree=True)
        processed_root = False # Keep track if we processed the main grant element

        for event, elem in context:
            # Process the grant record element if it's the right tag
            # This now happens *before* clearing memory for this element
            if etree.QName(elem.tag).localname == 'us-patent-grant':
                processed_root = True # Mark that we found and processed it
                record = parse_grant_record(elem, namespaces)
                if record:
                    records.append(record)
                processed_records_in_file += 1

                # Efficiently clear memory *after* processing the grant element
                # Clear the current element itself
                elem.clear()
                # Clear preceding siblings to free memory incrementally
                # Important: This must happen *after* processing the element
                # and *before* the next iteration might access the parent.
                while elem.getprevious() is not None:
                    # Check if parent still exists (it should, unless this was the root)
                    if elem.getparent() is not None:
                        del elem.getparent()[0]
                # Note: If multiple grant elements could exist at the root level,
                # this clearing might need adjustment. Assuming one grant per file.

            # If the element is NOT the grant tag, we might still want to clear it
            # to save memory, especially if the XML structure is deep or has large
            # sibling elements before the grant tag.
            # However, be cautious not to clear elements needed by the grant processing.
            # For now, let's only clear *after* processing the grant tag.
            # else:
            #    elem.clear() # Optional: Clear non-grant elements too?

        if not processed_root: # If loop finished without finding the grant tag
             # This could happen if the file was empty, didn't contain the tag, or iterparse failed early
             logging.warning(f"Did not find or process a 'us-patent-grant' element in {xml_file_path}.")

        logging.debug(f"Finished parsing. Found {processed_records_in_file} grant record(s) in {os.path.basename(xml_file_path)}")


        to_insert_record = []

        if mode == "subset":
            items_to_enqueue = []
            for record in records:
                keep_patent = False

                if record['reg_no'].startswith('D'):
                    keep_patent = True
                else:
                    cpc_ipc_entries = record.get('clean_cpc_ipc_entries', [])
                    
                    # Check if any of the cpc_ipc_entries are in the global _tro_cpc_ipc_classifications subset
                    if _tro_cpc_ipc_classifications:
                        for entry in cpc_ipc_entries:
                            if "".join([entry['section'], entry['class'].zfill(2), entry['subclass'], entry['main_group']]) in _tro_cpc_ipc_classifications:
                                keep_patent = True
                                break # Found a match, no need to check further entries for this record
                    
                if keep_patent:
                    # save all the tif images in the base extracted directory
                    parent_dir = Path(xml_file_path).parent

                    for file_path in parent_dir.iterdir():
                        # Check if the file is a TIFF image
                        if file_path.suffix.lower() in ['.tiff', '.tif']:  
                            dest_path = _get_or_create_patent_img_destination_directory(record['reg_no'], dest_dir, parent_dir.stem) / (file_path.stem + ".png")
                            
                            # We only want the Drawing page – the numbered “FIG. 1, FIG. 2 …” sheets that form the bulk of patent drawings 
                            # We do not want the Chemical images (stat with C0), the Math formula (start with M0), the Sequence DNA (start with M), or the Uncategorized images (start with P)
                            if "D0" not in file_path.stem:
                                if dest_path.exists(): # Delete the file (one off clean up)
                                    os.remove(dest_path)
                                    logging.debug(f"Deleted existing file at {dest_path}")
                                continue

                            # Skip resize and copy if destination file already exists
                            if dest_path.exists():
                                # logging.debug(f"Skipping image resize and copy for {file_path.name} as destination file already exists: {dest_path}")
                                items_to_enqueue.append(("Patent", record['reg_no'], dest_path))
                                continue

                            # Open the image using cv2
                            image_cv2 = cv2.imread(str(file_path), cv2.IMREAD_UNCHANGED)

                            if image_cv2 is not None:
                                # Get the dimensions of the image
                                height, width = image_cv2.shape[:2]

                                # Check the aspect ratio condition
                                if width < 4 * height:

                                    # resize the image so that the longest side is 512 while keeping the ratio
                                    if height > width:
                                        scale_factor = RESIZE_IMAGE_SIZE / height
                                        new_height = RESIZE_IMAGE_SIZE
                                        new_width = int(width * scale_factor)
                                    else:
                                        scale_factor = RESIZE_IMAGE_SIZE / width
                                        new_width = RESIZE_IMAGE_SIZE
                                        new_height = int(height * scale_factor)

                                    # Resize the image
                                    resized_image_cv2 = cv2.resize(image_cv2, (new_width, new_height), interpolation=cv2.INTER_AREA)

                                    # Save as PNG with compression level 9
                                    cv2.imwrite(str(dest_path), resized_image_cv2, [cv2.IMWRITE_PNG_COMPRESSION, 6])
                                    items_to_enqueue.append(("Patent", record['reg_no'], dest_path))
                                    logging.debug(f"Resized and copied image to {dest_path}")
                                    
                                    # Add PNG filename to record's fig_files list
                                    record['fig_files'].append(dest_path.name)

                    to_insert_record.append(record)

        elif mode == "full":
            # For full mode, we insert all records without filtering
            to_insert_record = records
            items_to_enqueue = []  # In full mode we do not create embeddings
            
            
    except etree.XMLSyntaxError as e:
        logging.error(f"XML Syntax Error parsing grant file {xml_file_path}: {e}")
        # records list might contain partially parsed data, decide if returning it is useful
        # return records # Or return None/empty list on syntax error? Returning current records for now.
    except Exception as e:
        logging.error(f"Unexpected error parsing grant file {xml_file_path}: {e}", exc_info=True)
        return None # Indicate error

    return to_insert_record, items_to_enqueue


def _parse_and_validate_uspc(raw_uspc_string, patent_identifier):
    """
    Parses a raw USPC string by trying multiple strategies and validates against the database.

    This function attempts several parsing and transformation heuristics in a specific
    order of likelihood to find a valid class/subclass combination.

    Args:
        raw_uspc_string (str): The raw USPC string from the XML (e.g., 'D 6534', 'D25 485').
        patent_identifier (str): Identifier for logging.

    Returns:
        tuple: A (class, subclass) tuple if a valid combination is found, otherwise (None, None).
    """
    if not raw_uspc_string or not raw_uspc_string.upper().startswith('D'):
        # This advanced logic is specifically for Design patents.
        # Non-'D' classes can be handled by a simpler logic if needed, but here we focus on the problem cases.
        return None, None

    # Clean the input by removing all spaces to create a consistent base
    cleaned_str = raw_uspc_string.replace(' ', '')

    # A generator to produce candidate (class, subclass) pairs in order of priority
    def _generate_candidates(text):
        # Strategy 1: Prepend '0' if 'D' is followed by a single digit.
        # This is a very common pattern for classes D1-D9.
        # 'D6534' -> ('D06', '534'). 'D2946' -> ('D02', '946').
        if len(text) > 2 and text[1].isdigit():
            yield 'D0' + text[1], text[2:]

        # Strategy 2: Standard 3-character class (DXX).
        # This is the standard for classes D10 and above.
        # 'D241101' -> ('D24', '1101'). Fallback for 'D2946' -> ('D29', '46').
        if len(text) >= 4:
            yield text[:3], text[3:]

        # Strategy 3: Fallback for cases where the class is just 'D'.
        # 'D2840' -> ('D', '2840')
        if len(text) > 1:
            yield 'D', text[1:]

    for uspc_class, uspc_subclass in _generate_candidates(cleaned_str):
        if not uspc_subclass:
            continue

        # Attempt 1: Direct validation
        if validate_uspc_classification(uspc_class, uspc_subclass):
            logging.debug(f"[{patent_identifier}] Validated USPC '{raw_uspc_string}' as ('{uspc_class}', '{uspc_subclass}')")
            return uspc_class, uspc_subclass

        # Attempt 2 & 3: Try adding decimal points to subclass if it's all digits
        if uspc_subclass.isdigit():
            # Add dot before last digit (e.g., '485' -> '48.5')
            if len(uspc_subclass) > 1:
                sub_with_dot1 = f"{uspc_subclass[:-1]}.{uspc_subclass[-1]}"
                if validate_uspc_classification(uspc_class, sub_with_dot1):
                    logging.debug(f"[{patent_identifier}] Validated USPC '{raw_uspc_string}' as ('{uspc_class}', '{sub_with_dot1}') with decimal rule 1.")
                    return uspc_class, sub_with_dot1

            # Add dot before last two digits (e.g., '71823' -> '718.23')
            if len(uspc_subclass) > 2:
                sub_with_dot2 = f"{uspc_subclass[:-2]}.{uspc_subclass[-2:]}"
                if validate_uspc_classification(uspc_class, sub_with_dot2):
                    logging.debug(f"[{patent_identifier}] Validated USPC '{raw_uspc_string}' as ('{uspc_class}', '{sub_with_dot2}') with decimal rule 2.")
                    return uspc_class, sub_with_dot2

    # If all strategies fail
    # Log the raw_uspc_string to a file for later review
    uspc_not_found_file = os.path.join(os.path.dirname(__file__), "logs", 'uspc_not_found.txt')
    try:
        with open(uspc_not_found_file, 'a', encoding='utf-8') as f:
            f.write(f"[{patent_identifier}] USPC Not Found: '{raw_uspc_string}'\n")
        logging.warning(f"[{patent_identifier}] USPC classification not found for input: '{raw_uspc_string}'. Logged to {uspc_not_found_file}")
    except IOError as e:
        logging.error(f"[{patent_identifier}] Failed to write to uspc_not_found.txt: {e}")
    return None, None




# --- New Classification Helper Function ---
def _extract_classifications(bib_data_element, namespaces, patent_identifier, document_id):
    """
    Extracts Locarno, USPC, and CPC classifications from the bibliographic data element.

    Args:
        bib_data_element: The lxml element for <us-bibliographic-data-grant>.
        namespaces (dict): XML namespaces.
        patent_identifier (str): Identifier for logging (e.g., reg_no).

    Returns:
        dict: A dictionary containing 'locarno', 'uspc', and 'cpc' keys.
              Values are the extracted data or None/empty set if not found/invalid.
    """

    locarno_result = None
    uspc_result = None
    cpc_result = set() # Use a set to store CPC dicts (make dicts hashable later)

    locarno_elem = None
    uspc_elem = None # Store the valid USPC element for adjacency check

    # --- Find Potential Elements ---
    try:
        locarno_elem_potential = bib_data_element.find('classification-locarno', namespaces)
        uspc_elem_potential = bib_data_element.find('classification-national', namespaces)
    except Exception as e:
        logging.error(f"[{patent_identifier}] Error finding classification elements: {e}", exc_info=True)
        # Return default empty results if finding fails critically
        return {'locarno': None, 'uspc': None, 'cpc': set()}

    # --- 5.3 Locarno (LOC) Extraction ---
    if locarno_elem_potential is not None:
        edition = get_element_text(locarno_elem_potential, 'edition', namespaces)
        main_class_elements = locarno_elem_potential.xpath('main-classification', namespaces=namespaces)

        if not edition:
            logging.warning(f"[{patent_identifier}] Missing <edition> in <classification-locarno>. Discarding Locarno.")
        elif not main_class_elements:
            logging.warning(f"[{patent_identifier}] Missing <main-classification> in <classification-locarno>. Discarding Locarno.")
        elif len(main_class_elements) > 1:
            logging.error(f"[{patent_identifier}] Found multiple <main-classification> tags in <classification-locarno>. Discarding Locarno.")
        else:
            main_class = get_element_text(main_class_elements[0], 'text()') # Get text from the single element
            if not main_class:
                 logging.warning(f"[{patent_identifier}] Empty <main-classification> tag in <classification-locarno>. Discarding Locarno.")
            else:
                logging.debug(f"[{patent_identifier}] Valid Locarno found: edition='{edition}', code='{main_class}'.")
                locarno_result = {'edition': edition, 'code': main_class}
                locarno_elem = locarno_elem_potential # Store valid element for adjacency check

    # --- 5.4 USPC Extraction ---
    uspc_classifications = []  # List to store all USPC classifications
    try:
        if uspc_elem_potential is not None and get_element_text(uspc_elem_potential, 'country', namespaces) == 'US':
            
            # Reusable processing function for any USPC entry
            def process_uspc_entry(element, class_type):
                raw_text = get_element_text(element, 'text()')
                if not raw_text:
                    logging.warning(f"[{patent_identifier}] Found empty <{element.tag}> tag in USPC. Skipping.")
                    return None

                # Use the new robust parsing and validation helper
                uspc_class, uspc_subclass = _parse_and_validate_uspc(raw_text, patent_identifier)

                if uspc_class and uspc_subclass:
                    return {
                        'class': uspc_class,
                        'subclass': uspc_subclass,
                        'type': class_type
                    }
                else:
                    # This is not a USPC classification but a "U.S. Cl."
                    return None

            # Process main classification
            main_class_elements = uspc_elem_potential.xpath('main-classification', namespaces=namespaces)
            if len(main_class_elements) > 1:
                logging.error(f"[{patent_identifier}] Found multiple <main-classification> tags in USPC. Discarding all USPC.")
            elif main_class_elements:
                main_classification_data = process_uspc_entry(main_class_elements[0], 'main')
                if main_classification_data:
                    uspc_classifications.append(main_classification_data)
                    # For backward compatibility, populate the legacy tuple
                    uspc_result = (main_classification_data['class'], main_classification_data['subclass'])

            # Process further classifications
            further_class_elements = uspc_elem_potential.xpath('further-classification', namespaces=namespaces)
            for further_elem in further_class_elements:
                further_classification_data = process_uspc_entry(further_elem, 'extra')
                if further_classification_data:
                    uspc_classifications.append(further_classification_data)

    except Exception as e:
        logging.error(f"[{patent_identifier}] Error during classification extraction: {e}", exc_info=True)
        # Return default empty results on critical failure
        return {'locarno': None, 'uspc': None, 'cpc': set(), 'uspc_classifications': []}

    # --- 5.5 Adjacency Check ---
    if locarno_elem is not None and uspc_elem is not None:
        # Check if they are immediate siblings
        locarno_next = locarno_elem.getnext()
        locarno_prev = locarno_elem.getprevious()
        uspc_next = uspc_elem.getnext()
        uspc_prev = uspc_elem.getprevious()

        # More robust check: are they siblings and is one the next/prev of the other?
        is_adjacent = (locarno_elem.getparent() is uspc_elem.getparent()) and \
                      (locarno_next is uspc_elem or locarno_prev is uspc_elem)
                      # (uspc_next is locarno_elem or uspc_prev is locarno_elem) # Redundant check

        if not is_adjacent:
            logging.error(f"[{patent_identifier}] Adjacency Check FAILED: <classification-locarno> and <classification-national> (US) are not immediate siblings. Discarding both.")
            locarno_result = None
            uspc_result = None
            # Keep locarno_elem and uspc_elem as they were for potential debugging, but results are None
        else:
            logging.debug(f"[{patent_identifier}] Adjacency Check PASSED.")

    # --- 5.6 CPC Extraction ---
    cpc_list_elem = bib_data_element.find('classifications-cpc', namespaces)
    if cpc_list_elem is not None:
        # Find all classification-cpc under main-cpc and further-cpc
        cpc_elements = cpc_list_elem.xpath('.//classification-cpc', namespaces=namespaces) # Simpler XPath

        if not cpc_elements:
             logging.debug(f"[{patent_identifier}] <classifications-cpc> found, but no <classification-cpc> children within it.")

        processed_cpc_count = 0
        skipped_cpc_count = 0
        for cpc_elem in cpc_elements:
            cpc_data = parse_cpc_classification(cpc_elem, namespaces)

            if cpc_data: # Check if parsing returned data
                # Make the dictionary hashable by converting to frozenset of items
                try:
                    hashable_cpc_data = frozenset(cpc_data.items())
                    cpc_result.add(hashable_cpc_data)
                    processed_cpc_count += 1
                except TypeError as e:
                     logging.error(f"[{patent_identifier}] Could not make CPC data hashable: {cpc_data}. Error: {e}. Skipping this CPC entry.")
                     skipped_cpc_count += 1
            else:
                # parse_cpc_classification returned None (shouldn't happen with current implementation, but good practice)
                # or the element was somehow invalid before parsing attempt.
                skipped_cpc_count += 1


        logging.debug(f"[{patent_identifier}] Processed {processed_cpc_count} CPC entries, skipped {skipped_cpc_count}.")

    # --- IPC Extraction ---
    ipc_list_elem = bib_data_element.find('classifications-ipcr', namespaces)
    ipc_result_list = []
    if ipc_list_elem is not None:
        ipc_elements = ipc_list_elem.xpath('.//classification-ipcr', namespaces=namespaces)

        if not ipc_elements:
             logging.debug(f"[{patent_identifier}] <classifications-ipcr> found, but no <classification-ipcr> children within it.")

        processed_ipc_count = 0
        for ipc_elem in ipc_elements:
            ipc_data = parse_ipc_classification(ipc_elem, namespaces)
            if ipc_data:
                ipc_result_list.append(ipc_data)
                processed_ipc_count += 1

        logging.debug(f"[{patent_identifier}] Processed {processed_ipc_count} IPC entries.")

    # --- Final Logging & Return ---
    if locarno_result is None and uspc_result is None and not cpc_result and not ipc_result_list:
        logging.warning(f"[{patent_identifier}] No classification information (Locarno, USPC, CPC, or IPC) was successfully extracted for this patent.")


    # Convert frozensets back to dicts for the final output - return as a list
    final_cpc_result_list = [dict(fs) for fs in cpc_result]
    
    clean_cpc_ipc_entries = match_cpc_ipc(document_id, final_cpc_result_list, ipc_result_list)

    # Conditionally include keys using a dictionary comprehension
    result = {
        key: value for key, value in [
            ('locarno', locarno_result),
            ('uspc', uspc_result),  # Keep legacy tuple format for backward compatibility
            ('cpc', final_cpc_result_list),
            ('ipc', ipc_result_list), # Add IPC results
            ('clean_cpc_ipc_entries', clean_cpc_ipc_entries)
        ]
        if value is not None # Include if not None
    }

    # Add the new uspc_classifications list if it's not empty
    if uspc_classifications:
        result['uspc_classifications'] = uspc_classifications

    return result


# --- Main Record Parsing Functions ---

def parse_grant_record(elem, namespaces):
    """Parses a <us-patent-grant> element."""
    record = {'record_type': 'grant'}
    record['image_source'] = 'USPTO'
    record['fig_files'] = []  # Initialize list to store PNG filenames
    bib_data_list = None
    # Try finding using relative XPath without namespace
    try:
        bib_data_list = elem.xpath('./us-bibliographic-data-grant')
    except Exception as e:
        logging.error(f"XPath error searching for './us-bibliographic-data-grant': {e}")

    if not bib_data_list:
        # If not found, try with namespace using XPath
        try:
            bib_data_list = elem.xpath('./pat:us-bibliographic-data-grant', namespaces=namespaces)
        except Exception as e:
             logging.error(f"XPath error searching for './pat:us-bibliographic-data-grant' with namespaces: {e}")

    if bib_data_list:
        bib_data = bib_data_list[0] # Take the first match
    else:
        bib_data = None
        logging.warning("Could not find <us-bibliographic-data-grant> (tried XPath with/without namespace) in a grant record.")
        return None # Cannot proceed without bibliographic data

    # Publication Reference
    pub_ref = bib_data.find('publication-reference/document-id', namespaces)
    if pub_ref is not None:
        record['publication_country'] = get_element_text(pub_ref, 'country', namespaces)
        record['reg_no'] = get_element_text(pub_ref, 'doc-number', namespaces)
        
        # Apply transformation for doc numbers starting with 'D'
        doc_number_val = record['reg_no']
        if doc_number_val and doc_number_val.startswith('D'):
            # Remove leading zeros after 'D' if followed by a non-zero digit
            # Example: D01041423423 -> D1041423423
            # Example: D0005 -> D5
            # Example: D123 -> D123 (no change)
            # Example: D000 (if no non-zero digit after D0s) -> D000 (no change)
            processed_doc_number = re.sub(r'(?<=^D)0+(?=[1-9])', '', doc_number_val)
            record['reg_no'] = processed_doc_number
        
        record['id'] = generate_uuid(record['reg_no'])
        record['patent_type'] = get_element_text(pub_ref, 'kind', namespaces)
        record['date_published'] = get_element_date(pub_ref, 'date', namespaces)
        record['folder'] = "US" + doc_number_val + "-" + record['date_published'].replace('-', '')
        record['document_id'] = "-".join(filter(None, [record.get('publication_country'), record.get('reg_no'), record.get('patent_type')])) # Ensure no None values

    # --- Extract Classifications using Helper ---
    patent_identifier = record.get('reg_no', 'UNKNOWN_ID') # Get identifier for logging
    if bib_data is not None:
        classification_data = _extract_classifications(bib_data, namespaces, patent_identifier, record['document_id'])
        record.update(classification_data) # Merge results ('locarno', 'uspc', 'cpc')
    else:
        # Ensure default keys exist even if bib_data was missing
        record['locarno'] = None
        record['uspc'] = None
        record['cpc'] = set()

    # Application Reference
    app_ref = bib_data.find('application-reference', namespaces)
    if app_ref is not None:
        record['application_type'] = app_ref.get('appl-type', '')
        app_ref_doc_id = app_ref.find('document-id', namespaces)
        if app_ref_doc_id is not None:
            record['application_country'] = get_element_text(app_ref_doc_id, 'country', namespaces)
            record['application_doc_number'] = get_element_text(app_ref_doc_id, 'doc-number', namespaces)
            record['application_date'] = get_element_date(app_ref_doc_id, 'date', namespaces)

    # Patent Number (often same as publication doc number, but good to have explicitly if needed)
    record['patent_number'] = record.get('reg_no') # Use publication as primary patent identifier

    # Title
    # Handle potential nested tags like <i> using etree.tostring with method='text'
    invention_title_elem = bib_data.find('invention-title', namespaces)
    if invention_title_elem is not None:
        record['patent_title'] = etree.tostring(invention_title_elem, method='text', encoding='unicode').strip()
    else:
        record['patent_title'] = None # Or default=''

    # Parties
    parties = bib_data.find('us-parties', namespaces)
    if parties is not None:
        record['applicants'] = [parse_party(p, namespaces) for p in parties.xpath('us-applicants/us-applicant', namespaces=namespaces)]
        record['inventors'] = [parse_party(p, namespaces) for p in parties.xpath('inventors/inventor', namespaces=namespaces)]
        record['agents'] = [parse_party(p, namespaces) for p in parties.xpath('agents/agent', namespaces=namespaces)]

    # assignees
    assignees = bib_data.find('assignees', namespaces)
    if assignees is not None:
        record['assignees'] = [parse_party(p, namespaces) for p in assignees.xpath('assignee', namespaces=namespaces)]

    # Examiners
    examiners = bib_data.find('examiners', namespaces)
    if examiners is not None:
        record['primary_examiner'] = get_element_text(examiners, 'primary-examiner/last-name', namespaces) + ", " + get_element_text(examiners, 'primary-examiner/first-name', namespaces)
        record['assistant_examiner'] = get_element_text(examiners, 'assistant-examiner/last-name', namespaces) + ", " + get_element_text(examiners, 'assistant-examiner/first-name', namespaces)

    # Citations (US and Non-Patent Literature)
    citations = bib_data.xpath('.//us-citation', namespaces=namespaces)
    record['associated_patents'] = []
    for cit in citations:
        doc_id = get_element_text(cit, './/doc-number', namespaces)
        if doc_id:
            record['associated_patents'].append(doc_id)

    # Clean and validate associate_patent_id_candidates
    raw_candidates = record.get('associated_patents', [])
    valid_cleaned_candidates = []
    invalid_original_candidates_to_log = []
    # Log invalid IDs in the same directory as this script (patent_parser.py)
    # This path should ideally be managed more centrally or passed as a config
    invalid_log_file_path = os.path.join(os.path.dirname(__file__), "logs", 'invalid_patent_ids.txt')
    patent_doc_number_for_logging = record.get('reg_no', 'UNKNOWN_GRANT')


    if raw_candidates:
        for candidate_id in raw_candidates:
            # Ensure candidate_id is treated as a string for regex and clean it
            cleaned_id = re.sub(r'[^A-Z0-9]', '', str(candidate_id))
            # Validate patent length goes from 7 to 12 characters
            if 7 <= len(cleaned_id) <= 12:
                valid_cleaned_candidates.append(cleaned_id)
            else:
                # Log the original ID if validation fails
                invalid_original_candidates_to_log.append(candidate_id)

        # Log invalid original IDs if any were found
        if invalid_original_candidates_to_log:
            try:
                # Append invalid IDs to the log file
                with open(invalid_log_file_path, 'a', encoding='utf-8') as log_file:
                    for invalid_id in invalid_original_candidates_to_log:
                        log_file.write(f"Grant: {patent_doc_number_for_logging}, Invalid Raw ID: {invalid_id}\n")
            except IOError as e:
                logging.error(f"[{patent_doc_number_for_logging}] Failed to write invalid candidate IDs to {invalid_log_file_path}: {e}")

    record['associated_patents'] = valid_cleaned_candidates # Update with cleaned list

    # Description
    description_elem = elem.find('description', namespaces)
    if description_elem is not None:
        # Extracting full text might be memory intensive; consider alternatives if needed
        # For now, concatenate paragraphs or take the whole text
        record['description'] = etree.tostring(description_elem, method='text', encoding='unicode').strip()
        # Alternative: Just get top-level text if structure is simple
        # record['description'] = get_element_text(elem, 'description', namespaces)

    # Claims
    # Locate the first 'claim-text' element within the entire 'elem'
    first_claim_text_elem = elem.xpath('.//claim-text[1]', namespaces=namespaces)
    if first_claim_text_elem:
        # Extract and clean the text from the first 'claim-text' element
        record['abstract'] = first_claim_text_elem[0].xpath('string(.)').strip()
    else:
        # If no 'claim-text' element is found, set the value to None
        record['abstract'] = None
    return record

def parse_party(party_elem, namespaces):
    """Parses an applicant, inventor, or agent element."""
    party_data = {'sequence': party_elem.get('sequence')}
    addressbook = party_elem.find('addressbook', namespaces)
    if addressbook is not None:
        party_data['last_name'] = get_element_text(addressbook, 'last-name', namespaces)
        party_data['first_name'] = get_element_text(addressbook, 'first-name', namespaces)
        party_data['orgname'] = get_element_text(addressbook, 'orgname', namespaces) # For companies
        address = addressbook.find('address', namespaces)
        if address is not None:
            party_data['city'] = get_element_text(address, 'city', namespaces)
            party_data['state'] = get_element_text(address, 'state', namespaces)
            party_data['country'] = get_element_text(address, 'country', namespaces)
            party_data['postcode'] = get_element_text(address, 'postcode', namespaces)
            # Add street etc. if needed
    # Add role for agents if present
    party_data['role'] = get_element_text(party_elem, 'role', namespaces)
    return party_data


def parse_cpc_classification(cpc_elem, namespaces):
    """
    Parses a single CPC classification element from a patent grant file
    """
    classification = {}
    # Structure based on the classification-cpc element in grant files
    classification['version_indicator_date'] = get_element_date(cpc_elem, 'cpc-version-indicator/date', namespaces)
    classification['section'] = get_element_text(cpc_elem, 'section', namespaces)
    classification['class'] = get_element_text(cpc_elem, 'class', namespaces)
    classification['subclass'] = get_element_text(cpc_elem, 'subclass', namespaces)
    classification['main_group'] = get_element_text(cpc_elem, 'main-group', namespaces).lstrip('0')
    classification['subgroup'] = get_element_text(cpc_elem, 'subgroup', namespaces)

    # if subgroup only has zeros, do nothing else strip trailing zeros
    if set(classification['subgroup']) != {'0'}:
        classification['subgroup'] = classification['subgroup'].rstrip('0')
    else:
        classification['subgroup'] = '00'

    classification['symbol'] = (classification['section'] 
                                + classification['class']
                                + classification['subclass']
                                + classification['main_group']
                                + classification['subgroup']).strip()

    return classification


def parse_ipc_classification(ipc_elem, namespaces):
    """Parses a single IPC classification element."""
    classification = {}
    # Structure based on the provided example and similarity to CPC
    classification['version_indicator_date'] = get_element_date(ipc_elem, 'ipc-version-indicator/date', namespaces)
    classification['classification_level'] = get_element_text(ipc_elem, 'classification-level', namespaces)
    classification['section'] = get_element_text(ipc_elem, 'section', namespaces)
    classification['class'] = get_element_text(ipc_elem, 'class', namespaces)
    classification['subclass'] = get_element_text(ipc_elem, 'subclass', namespaces)
    classification['main_group'] = get_element_text(ipc_elem, 'main-group', namespaces).lstrip('0')
    classification['subgroup'] = get_element_text(ipc_elem, 'subgroup', namespaces)

    # if subgroup only has zeros, do nothing else strip trailing zeros
    if set(classification['subgroup']) != {'0'}:
        classification['subgroup'] = classification['subgroup'].rstrip('0')
    else:
        classification['subgroup'] = '00'

    classification['symbol'] = (classification['section']
                                + classification['class']
                                + classification['subclass']
                                + classification['main_group']
                                + classification['subgroup']).strip()

    return classification

# --- Orchestrator Functions (Parallel) ---
def parse_grant_xml_parallel(xml_file_paths, mode, image_extracton_base_path):
    """
    Parses multiple patent grant XML files in parallel.

    Args:
        xml_file_paths (list): A list of paths to patent grant XML files.

    Returns:
        list: An aggregated list of parsed grant record dictionaries.
    """
    if not xml_file_paths:
        logging.info("No Grant XML file paths provided for parallel parsing.")
        return []

    # Map the single-file grant parser function
    # Load USPC definitions cache in the parent process
    if not load_uspc_definitions_cache():
        logging.error("Failed to load USPC definitions cache in parent process. Aborting parallel parsing.")
        return []
    
    if not load_cpc_ipc_definitions_cache():
        logging.error("Failed to load CPC/IPC definitions cache in parent process. Aborting parallel parsing.")
        return []

    # Pass the loaded DataFrame to the worker processes via initializer
    logging.info(f"Starting parallel parsing for {len(xml_file_paths)} Grant XML files.")
    from IP.Patents_Bulk.patent_db_uspc import _uspc_definitions_df  # After it has been loaded!!!!!
    from IP.Patents_Bulk.patent_parser_cpc_ipc_match import _cpc_ipc_definitions_df  # After it has been loaded!!!!!
    
    tro_cpc_ipc_classifications_data = get_tro_cpc_ipc_classifications()
    
    pool = get_xml_parser_pool(initializer=_worker_init, initargs=(_uspc_definitions_df, _cpc_ipc_definitions_df, tro_cpc_ipc_classifications_data))
    
    parse_grant_xml_chunk_with_mode = functools.partial(parse_grant_xml_chunk, mode=mode, dest_dir=image_extracton_base_path)
    
    results = pool.imap_unordered(parse_grant_xml_chunk_with_mode, xml_file_paths)
    all_records = []
    all_items_to_enqueue = [] # New list to collect image data

    processed_files = 0
    total_files = len(xml_file_paths)
    for records_from_file, items_to_enqueue_from_file in results: # Now expecting two return values
        processed_files += 1
        if records_from_file is not None: # Check if parsing succeeded for the file
            all_records.extend(records_from_file)
            all_items_to_enqueue.extend(items_to_enqueue_from_file) # Collect image data
            logging.debug(f"Aggregated {len(records_from_file)} grant records and {len(items_to_enqueue_from_file)} image items ({processed_files}/{total_files})")
        else:
            logging.warning(f"Skipped aggregating results from one file due to parsing error ({processed_files}/{total_files})")


    logging.info(f"Finished parallel grant parsing. Aggregated {len(all_records)} records.")
    return all_records, all_items_to_enqueue

# --- Example Usage (Optional) ---
if __name__ == '__main__':
    grant_file_to_profile = "D:/Win10User/Downloads/USD1070806-20250415/USD1070806-20250415.XML"
    parse_grant_xml_chunk(grant_file_to_profile)
