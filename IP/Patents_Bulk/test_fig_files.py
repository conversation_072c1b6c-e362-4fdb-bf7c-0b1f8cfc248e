#!/usr/bin/env python3
"""
Test script to verify that fig_files functionality works correctly.
This script tests the patent parser modifications to ensure PNG filenames
are properly collected and stored in the fig_files field.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
from lxml import etree

# Add the project root to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from IP.Patents_Bulk.patent_parser import parse_grant_record

def create_test_xml():
    """Create a minimal test XML for patent grant parsing."""
    xml_content = '''<?xml version="1.0" encoding="UTF-8"?>
<us-patent-grant xmlns:pat="urn:us:gov:doc:uspto:patent" xmlns:uspat="urn:us:gov:doc:uspto:common">
    <us-bibliographic-data-grant>
        <publication-reference>
            <document-id>
                <country>US</country>
                <doc-number>D123456</doc-number>
                <kind>S1</kind>
                <date>20240101</date>
            </document-id>
        </publication-reference>
        <application-reference appl-type="design">
            <document-id>
                <country>US</country>
                <doc-number>29/123456</doc-number>
                <date>20230101</date>
            </document-id>
        </application-reference>
        <invention-title>Test Patent Title</invention-title>
        <us-parties>
            <inventors>
                <inventor sequence="1">
                    <addressbook>
                        <last-name>Doe</last-name>
                        <first-name>John</first-name>
                        <address>
                            <city>Test City</city>
                            <state>CA</state>
                            <country>US</country>
                        </address>
                    </addressbook>
                </inventor>
            </inventors>
        </us-parties>
    </us-bibliographic-data-grant>
    <description>
        <p>This is a test patent description.</p>
    </description>
    <claims>
        <claim id="CLM-00001">
            <claim-text>Test claim text for the patent.</claim-text>
        </claim>
    </claims>
</us-patent-grant>'''
    return xml_content

def test_fig_files_initialization():
    """Test that fig_files is properly initialized in parse_grant_record."""
    print("Testing fig_files initialization...")
    
    # Create test XML
    xml_content = create_test_xml()
    
    # Parse the XML
    root = etree.fromstring(xml_content)
    namespaces = {
        'uspat': 'urn:us:gov:doc:uspto:common',
        'pat': 'urn:us:gov:doc:uspto:patent',
    }
    
    # Parse the grant record
    record = parse_grant_record(root, namespaces)
    
    # Check that fig_files is initialized
    assert record is not None, "Record should not be None"
    assert 'fig_files' in record, "Record should contain fig_files key"
    assert isinstance(record['fig_files'], list), "fig_files should be a list"
    assert record['fig_files'] == [], "fig_files should be initialized as empty list"
    
    print("✓ fig_files initialization test passed")
    return True

def test_database_columns():
    """Test that fig_files is included in database columns."""
    print("Testing database column configuration...")
    
    # Import the database module
    from IP.Patents_Bulk.patent_db_grant import upsert_patent_grants
    
    # Check the source code to ensure fig_files is in columns
    import inspect
    source = inspect.getsource(upsert_patent_grants)
    
    # Check that fig_files is mentioned in the columns list
    assert "'fig_files'" in source, "fig_files should be in the database columns"
    
    print("✓ Database column configuration test passed")
    return True

def test_record_structure():
    """Test the overall record structure includes expected fields."""
    print("Testing record structure...")
    
    xml_content = create_test_xml()
    root = etree.fromstring(xml_content)
    namespaces = {
        'uspat': 'urn:us:gov:doc:uspto:common',
        'pat': 'urn:us:gov:doc:uspto:patent',
    }
    
    record = parse_grant_record(root, namespaces)
    
    # Check essential fields
    expected_fields = [
        'record_type', 'image_source', 'fig_files', 'reg_no', 
        'patent_title', 'date_published', 'inventors'
    ]
    
    for field in expected_fields:
        assert field in record, f"Record should contain {field} field"
    
    # Check specific values
    assert record['record_type'] == 'grant', "Record type should be 'grant'"
    assert record['image_source'] == 'USPTO', "Image source should be 'USPTO'"
    assert record['reg_no'] == 'D123456', "Registration number should be 'D123456'"
    
    print("✓ Record structure test passed")
    return True

def run_all_tests():
    """Run all tests and report results."""
    print("Running fig_files implementation tests...\n")
    
    tests = [
        test_fig_files_initialization,
        test_database_columns,
        test_record_structure,
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
                print(f"✗ {test.__name__} failed")
        except Exception as e:
            failed += 1
            print(f"✗ {test.__name__} failed with error: {e}")
    
    print(f"\nTest Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! The fig_files implementation is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
