"""
Patent File Management Module

This module provides functions to manage patent files from USPTO, 
including downloading, extracting XML from specific structures,
and sending to NAS.
"""

import os
import zipfile
import tarfile # Added for tar file handling
import requests
import logging
import time
import shutil
import tempfile
import io
import re # Added for regex matching
from pathlib import Path
from FileManagement.NAS import NASConnection  # Assuming NASConnection is importable
from typing import IO, List, Tuple, Optional


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("patent_file.log"), # Consider making log file configurable
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def download_file(url, local_path, headers=None, max_retries=5, retry_delay=5):
    """
    Download a file from a URL with retry logic.

    Args:
        url (str): URL to download from
        local_path (str): Path to save the file
        headers (dict, optional): HTTP headers
        max_retries (int): Maximum number of retry attempts
        retry_delay (int): Delay between retries in seconds

    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(f"Attempting to download file from {url} to {local_path}")
    local_path_obj = Path(local_path)
    # Create directory if it doesn't exist
    local_path_obj.parent.mkdir(parents=True, exist_ok=True)

    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, stream=True, timeout=120) # Increased timeout
            response.raise_for_status()

            # Save file
            # Log URL and path *after* successfully opening the file handle
            logger.info(f"Downloading from {url} to {local_path}")
            with open(local_path_obj, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            # Get file size after download
            try:
                file_size = os.path.getsize(local_path_obj)
                logger.info(f"Download complete for {local_path}. File size: {file_size} bytes.")
            except OSError as size_err:
                logger.error(f"Could not get size of downloaded file {local_path}: {size_err}")
                # Proceed to validation anyway, maybe it's still readable
                file_size = -1 # Indicate unknown size

            # --- Validate downloaded archive file ---
            file_extension = local_path_obj.suffix.lower()
            try:
                if file_extension == '.zip':
                    logger.info(f"Validating downloaded zip file: {local_path}")
                    with zipfile.ZipFile(local_path_obj, 'r') as zip_ref:
                        test_result = zip_ref.testzip() # Check for corrupt files within the archive
                        if test_result is not None:
                            raise zipfile.BadZipFile(f"First corrupt file in zip: {test_result}")
                    logger.info(f"ZIP Validation successful for {local_path}")
                    return True # Download and validation successful
                elif file_extension == '.tar':
                    logger.info(f"Validating downloaded tar file: {local_path}")
                    if not tarfile.is_tarfile(local_path_obj):
                         # is_tarfile doesn't raise specific errors easily, so we raise our own conceptually similar one
                        raise tarfile.ReadError(f"File {local_path} is not a valid tar file or is corrupted.")
                    # Optional: Could try opening and reading a member for more thorough validation
                    # with tarfile.open(local_path_obj, 'r') as tar_ref:
                    #     tar_ref.getmembers() # Try reading member list
                    logger.info(f"TAR Validation successful for {local_path}")
                    return True # Download and validation successful
                else:
                    logger.warning(f"Downloaded file {local_path} has an unsupported extension '{file_extension}'. Skipping validation.")
                    # Decide if unsupported extensions should be treated as success or failure.
                    # Assuming success for now, as download itself completed.
                    return True

            except (zipfile.BadZipFile, tarfile.ReadError) as archive_err:
                logger.error(f"Validation failed: Downloaded file {local_path} (size: {file_size} bytes) is invalid or corrupt: {archive_err}")
                try:
                    os.remove(local_path_obj)
                    logger.info(f"Deleted invalid downloaded file: {local_path}")
                except OSError as del_err:
                    logger.error(f"Failed to delete invalid downloaded file {local_path}: {del_err}")
                return False # Indicate download failure due to invalid archive
            except Exception as val_err: # Catch other potential errors during validation
                logger.error(f"An unexpected error occurred during archive validation for {local_path} (size: {file_size} bytes): {val_err}", exc_info=True)
                try:
                    os.remove(local_path_obj)
                    logger.info(f"Deleted file due to validation error: {local_path}")
                except OSError as del_err:
                    logger.error(f"Failed to delete file {local_path} after validation error: {del_err}")
                return False # Indicate failure

        except requests.exceptions.RequestException as e:
            status_code = getattr(e.response, 'status_code', None)
            if status_code == 404:
                logger.warning(f"File not found (404) at URL: {url}")
                return False # Don't retry on 404

            logger.warning(f"Download attempt {attempt + 1}/{max_retries} failed for {url}: {str(e)}")

            if attempt < max_retries - 1:
                logger.info(f"Retrying download in {retry_delay} seconds...")
                time.sleep(retry_delay)
            else:
                logger.error(f"Failed to download file {url} after {max_retries} attempts.")
                return False
    return False # Should not be reached, but added for clarity

def extract_patent_zip(archive_path: str) -> Tuple[List[str], Optional[str]]:
    """
    Extracts all files from USPTO patent archives to a temporary directory.
    Handles archives where patent data is located within nested .zip files
    in DESIGN/ or UTIL***/ subdirectories.
    """
    all_extracted_files_paths = []
    archive_path_obj = Path(archive_path)
    file_extension = archive_path_obj.suffix.lower()
    temp_extract_dir = None # Initialize to None

    def _process_nested_zip(
        inner_zip_name: str,
        inner_zip_fileobj: IO[bytes],
        temp_dir: Path, # Pass the temporary directory path
        extracted_files: List[str]
    ):
        """
        Extracts, processes, and cleans up a single inner .zip file,
        provided as a file-like object, into the temporary directory.
        """
        # A temporary path is still needed to use zipfile.ZipFile, which requires a path or path-like object
        # Create the temporary zip file within the main temporary extraction directory
        temp_zip_path = temp_dir / os.path.basename(inner_zip_name)
        try:
            # Write the in-memory file object to a temporary file on disk
            with open(temp_zip_path, 'wb') as f_out:
                shutil.copyfileobj(inner_zip_fileobj, f_out)
            logger.debug(f"Temporarily extracted inner zip to: {temp_zip_path}")

            # Process the newly extracted inner zip file
            with zipfile.ZipFile(temp_zip_path, 'r') as inner_zip_ref:
                for item in inner_zip_ref.infolist():
                    if item.is_dir():
                        continue
                    # Extract directly into the main temporary directory
                    extracted_path = inner_zip_ref.extract(item, path=temp_dir)
                    # Store the absolute path of the extracted file
                    extracted_files.append(str(Path(temp_dir) / item.filename))
            logger.debug(f"Successfully processed inner zip: {inner_zip_name}")

        except Exception as e:
            logger.error(f"Failed to process inner zip '{inner_zip_name}': {e}", exc_info=True)
        finally:
            # Clean up the temporary inner zip file
            if temp_zip_path.exists():
                temp_zip_path.unlink()

    try:
        # Create a temporary directory for extraction, base directory is the archive's directory
        archive_dir = str(Path(archive_path).parent)
        temp_extract_dir = tempfile.mkdtemp(prefix="patent_extract_", dir=archive_dir)
        logger.info(f"Created temporary extraction directory: {temp_extract_dir}")
        logger.info(f"Processing archive: {archive_path}")

        # --- Handle .tar file structure ---
        if file_extension == '.tar':
            with tarfile.open(archive_path_obj, 'r') as tar_ref:
                for member in tar_ref.getmembers():
                    # Find nested zip files in the correct directories
                    if member.isfile() and re.search(r'/(DESIGN|UTIL\d+)/', member.name) and 'supp' not in member.name.lower() and member.name.lower().endswith('.zip'):
                        with tar_ref.extractfile(member) as inner_zip_fileobj:
                            # Pass the temporary directory path
                            _process_nested_zip(member.name, inner_zip_fileobj, Path(temp_extract_dir), all_extracted_files_paths)

        # --- Handle .zip file structure ---
        elif file_extension == '.zip':
            with zipfile.ZipFile(archive_path_obj, 'r') as zip_ref:
                for member_info in zip_ref.infolist():
                    # Find nested zip files in the correct directories
                    if not member_info.is_dir() and re.search(r'/(DESIGN|UTIL\d+)/', member_info.filename) and 'supp' not in member_info.filename.lower() and member_info.filename.lower().endswith('.zip'):
                        with zip_ref.open(member_info.filename) as inner_zip_fileobj:
                            # Pass the temporary directory path
                            _process_nested_zip(member_info.filename, inner_zip_fileobj, Path(temp_extract_dir), all_extracted_files_paths)

        else:
            logger.error(f"Unsupported file type: '{file_extension}' for {archive_path}")
            # Clean up the temporary directory if created
            if temp_extract_dir and os.path.exists(temp_extract_dir):
                 shutil.rmtree(temp_extract_dir)
            return [], None

        if all_extracted_files_paths:
            logger.info(f"Successfully extracted {len(all_extracted_files_paths)} file(s) to {temp_extract_dir}")
        else:
            logger.warning(f"No processable inner zip files were found in {archive_path}")
            # Clean up the temporary directory if created but no files extracted
            if temp_extract_dir and os.path.exists(temp_extract_dir):
                 shutil.rmtree(temp_extract_dir)
            return [], None # Return empty list and None for dir if no files extracted

        # Return the list of extracted file paths and the temporary directory path
        return all_extracted_files_paths, temp_extract_dir

    except (zipfile.BadZipFile, tarfile.ReadError) as archive_err:
        logger.error(f"Failed to open or read archive file {archive_path}: {archive_err}", exc_info=True)
        # Clean up the temporary directory if created
        if temp_extract_dir and os.path.exists(temp_extract_dir):
             shutil.rmtree(temp_extract_dir)
        return [], None
    except Exception as e:
        logger.error(f"An unexpected error occurred while processing {archive_path}: {e}", exc_info=True)
        # Clean up the temporary directory if created
        if temp_extract_dir and os.path.exists(temp_extract_dir):
             shutil.rmtree(temp_extract_dir)
        return [], None


def cleanup_extracted_directory(extract_dir):
    """
    Removes the temporary extraction directory.

    Args:
        extract_dir (str): The path to the directory created by extract_patent_zip.

    Returns:
        bool: True if successful, False otherwise.
    """
    logger.info(f"Attempting to clean up extraction directory: {extract_dir}")
    if not extract_dir or not os.path.exists(extract_dir):
        logger.warning(f"Cleanup skipped: Directory {extract_dir} is invalid or does not exist.")
        return False
    try:
        shutil.rmtree(extract_dir)
        logger.info(f"Successfully removed extraction directory: {extract_dir}")
        return True
    except OSError as e:
        logger.error(f"Error removing directory {extract_dir}: {str(e)}", exc_info=True)
        return False

# Example usage (optional, for testing)
if __name__ == '__main__':
    # This block is for testing purposes only and won't run when imported
    logger.info("Patent File Module - Test Execution")
    
    # Create dummy files/folders for testing if needed
    # test_url = "SOME_TEST_URL" # Replace with a real small zip URL for testing
    # test_zip_path = "temp_patent_download.zip"
    # test_extract_base = "temp_patent_extract"
    # test_nas_remote_file = "/path/on/nas/test_file.xml"
    # test_nas_remote_folder = "/path/on/nas/test_folder"

    # print("\n--- Testing Download ---")
    # if download_file(test_url, test_zip_path):
    #     print(f"Downloaded test file to {test_zip_path}")

    #     print("\n--- Testing Extraction ---")
    #     extracted_files, unique_dir = extract_patent_zip(test_zip_path, test_extract_base)
    #     if unique_dir:
    #         print(f"Extraction directory: {unique_dir}")
    #         print(f"Extracted XML files: {extracted_files}")

    #         if extracted_files:
    #             print("\n--- Testing Send File to NAS ---")
    #             # send_to_nas(extracted_files[0], test_nas_remote_file) # Requires NAS setup

    #             print("\n--- Testing Send Folder to NAS ---")
    #             # send_folder_to_nas(unique_dir, test_nas_remote_folder) # Requires NAS setup

    #         print("\n--- Testing Cleanup ---")
    #         cleanup_extracted_directory(unique_dir)

    #     # Clean up downloaded zip
    #     if os.path.exists(test_zip_path):
    #         os.remove(test_zip_path)
    # else:
    #     print(f"Failed to download test file from {test_url}")

    print("\n--- Patent File Module Loaded ---")