import asyncio, os, json, time, re
from bs4 import BeautifulSoup
import pandas as pd
from AI.USPTO_Patent_API import PatentApi
from Common.Constants import nas_ip_folder
from DatabaseManagement.Connections import get_gz_connection, is_connection_alive
from FileManagement.NAS import NASConnection
from IP.ip_shared import get_patent_df
from AI.GC_VertexAI import vertex_genai_multi_async
from AI.LLM_shared import get_list
import Common.Constants as Constants
from Common.Constants import local_ip_folder
import traceback

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗


async def get_patent_data_by_reg_no(reg_nos, plaintiff_id=None, search_results=None, api=None):
    # Retrieve patent data using the USPTO Patent API by searching for specific patent registration numbers.
    # The format of the reg_no must be only the numbers!
    # search_results: Optional dictionary of document_id -> search_result to skip USPTO API search
    
    print(f"\033[33m      📥 Getting patent data by reg_no: {reg_nos}\033[0m")
    nas = NASConnection() # Initialize NAS connection
    db_connection = None # Initialize database connection
    patent_db_df = get_patent_df() # Get cached dataframe
    
    # Create a single shared API session if needed
    should_close_api = False
    if api is None and any(reg_no for reg_no in reg_nos if reg_no not in patent_db_df["reg_no"].values):
        api = PatentApi()
        await api.start_session()
        should_close_api = True
    
    try:
        # Create a list of coroutines to process each reg_no in parallel
        async def process_single_reg_no(reg_no):
            nonlocal db_connection  # Move the nonlocal declaration to the beginning of the function
            
            if not reg_no:
                return None

            found = False
            if reg_no in patent_db_df["reg_no"].values: 
                print(f"\033[33m 🔥 Patent already in database: {reg_no}\033[0m")
                df_entry = patent_db_df[patent_db_df["reg_no"] == reg_no].iloc[0]
                found, pdf_local_path, html_local_path = get_patent_from_NAS(df_entry["document_id"], nas)

                # Update plaintiff_ids in the database if needed
                if plaintiff_id and found:
                    if db_connection is None:
                        db_connection = get_gz_connection()
                    update_patent_plaintiff_ids(df_entry["document_id"], plaintiff_id, db_connection, patent_db_df)

            if found:
                files = {"pdf": pdf_local_path, "html": html_local_path}
                
                # Parse HTML file to extract additional data
                abstract, assignee, applicant = extract_patent_data_from_html(html_local_path)
                if isinstance(df_entry["design_page_numbers"], str): 
                    design_page_numbers = json.load(df_entry["design_page_numbers"])
                else:
                    design_page_numbers = df_entry["design_page_numbers"]
                    
                return {"document_id":  df_entry["document_id"], "reg_no": df_entry["reg_no"], 
                    "text": df_entry["text"], "inventors": df_entry["inventors"], "date_published": df_entry["date_published"], 
                    "type": df_entry["type"], "files": files, "abstract": abstract, "assignee": assignee, "applicant": applicant, "design_page_numbers": design_page_numbers}
            
            else: # Get from USPTO
                document_id = None
                result = None

                # If we have pre-found search results, use those instead of searching again (e.g. when we are getting the patents from the plaintiff name)
                if search_results and reg_no in search_results:
                    result = search_results[reg_no]
                    document_id = result.get("documentId")  # for the case where we have pre-found search results using search patent (not search assignee)
                if not document_id: # Need to search for the patent number using the USPTO API
                    nonlocal api
                    # Use the shared API session created above
                    if api:
                        # Search for the patent by its registration number
                        query = f"{reg_no}.pn."  # We add the .pn. to search only the field patent number
                        search_result = await api.search_patents(query=query, page_size=1)

                        if search_result and search_result.get("results") and len(search_result["results"]) > 0:
                            result = search_result["results"][0]
                            document_id = result.get("documentId")
                
                if document_id:
                    if search_results and reg_no in search_results and "patAssigneeName" in search_results[reg_no]:
                        result["assignee"] = search_results[reg_no]["patAssigneeName"]  # When we got the patent from Assignee Search, we keep that assignee name
                    if not db_connection:
                        db_connection = get_gz_connection()
                    return await get_single_patent_from_USPTO_and_send_to_NAS_and_save_to_db(document_id, result, nas, db_connection, api, patent_db_df, plaintiff_id)
            
            return None  # Return None if patent not found
        
        # Create tasks for all reg_nos & Execute all tasks in parallel, then filter out None results
        tasks = [process_single_reg_no(reg_no) for reg_no in reg_nos]
        results = await asyncio.gather(*tasks)
        patent_list = [result for result in results if result is not None]
                       
    except Exception as e:
        print(f"\033[91m ⚠️ get_patent_data_by_reg_no: Error retrieving patent data: {str(e)}\033[0m")
        print(f"Traceback:\n{traceback.format_exc()}")
        patent_list = []
    
    finally:
        # Only close the API session if we created it in this function
        if should_close_api and api: 
            await api.close_session()
        if nas:
            nas.close()
        if db_connection:
            db_connection.close()
    
    return patent_list



CHINESE_CITIES = {
    "beijing", "shanghai", "guangzhou", "shenzhen", "hangzhou",
    "chengdu", "chongqing", "tianjin", "wuhan", "nanjing",
    "xi'an", "suzhou", "dongguan", "foshan", "yiwu", "fuzhou",
    "qingdao", "zhengzhou", "changsha", "hefei", "xiamen"
}

# Helper function to build query with first two words maximum
def build_query(name, field_suffix):
    words = name.split()
    num_words = len(words)
    name_parts_to_use = []

    # Check if more than 2 words and the first word is a city (case-insensitive)
    if num_words > 2 and words[0].lower() in CHINESE_CITIES:
        # If it's a city, remove it and take the next two words (up to 2)
        name_parts_to_use = words[1:3]
    else:
        # Otherwise, take the first two words (up to 2)
        name_parts_to_use = words[:2]

    # Build the query based on the selected name parts
    if len(name_parts_to_use) == 2:
        query_terms = [f'("{part}").{field_suffix}.' for part in name_parts_to_use]
        return " AND ".join(query_terms)
    elif len(name_parts_to_use) == 1:
        # If only one part, quote it directly
        return f'("{name_parts_to_use[0]}").{field_suffix}.'
    return None # Return None if name is empty or no valid parts are left




async def _search_patent_by_name_field(api, name, field_suffix, readable_field_name, nb_results):
    """Helper to search patents by a specific name field."""
    database_filters_approved = [{"databaseName": "USPAT"}]
    database_filters_all = [{"databaseName": "USPAT"}, {"databaseName": "US-PGPUB"}, {"databaseName": "USOCR"}]
    search_results = []
    search_term_used = ""
    
    query = build_query(name, field_suffix)
    if not query:
        print(f"Skipping search for {readable_field_name} as name '{name}' resulted in invalid query.")
        return search_results, search_term_used # Skip if query is invalid

    print(f"Attempting search with {readable_field_name} query: {query}")
    
    # Search approved databases first
    search_results = await api.search_patents_paginated(query=query, page_size=nb_results, max_pages=1, database_filters=database_filters_approved)
    if len(search_results) > 20:
        search_results = await api.search_patents_paginated(query=f"{query} AND D$.ccls.", page_size=nb_results, max_pages=1, database_filters=database_filters_approved)
    elif len(search_results) > 0:
        search_term_used = f"{readable_field_name}: {name}"
    elif len(search_results) == 0:
        # If no results, search all databases
        print(f"No results in approved DBs for {readable_field_name}, trying all DBs...")
        search_results = await api.search_patents_paginated(query=query, page_size=nb_results, max_pages=1, database_filters=database_filters_all)
        if len(search_results) > 20:
            search_results = await api.search_patents_paginated(query=f"{query} AND D$.ccls.", page_size=nb_results, max_pages=1, database_filters=database_filters_all)
        if len(search_results) > 0:
            search_term_used = f"{readable_field_name}: {name} (all databases)"
            
    return search_results, search_term_used


async def get_patent_data_by_name(names, plaintiff_name, nb_results, plaintiff_id=None):
    # Retrieve patent data using the USPTO Patent API by searching for patents where the plaintiff is listed as an assignee.
    
    print(f"\033[33m   📥 Getting patent data by name: {plaintiff_name} and 💡 {names}\033[0m")
    api = PatentApi() # Initialize the USPTO Patent API client (always needed)
    await api.start_session()
    search_results = [] # Accumulate results here
    search_term_used = ""
    found_design_patent = False

    try:
        # --- First Pass: Search using standard fields (search_patents) ---
        search_attempts_std = [
            {"name_value": names.get("assignee"), "field_suffix": "as", "readable_name": "assignee"},
            {"name_value": names.get("applicant"), "field_suffix": "aanm", "readable_name": "applicant"},
            {"name_value": names.get("inventor"), "field_suffix": "in", "readable_name": "inventor"},
            {"name_value": plaintiff_name, "field_suffix": "as", "readable_name": "plaintiff name as assignee"} # Fallback
        ]

        for attempt in search_attempts_std:
            name_to_search = attempt.get("name_value")

            if name_to_search and len(str(name_to_search).strip()) > 0:
                current_search_results, current_search_term = await _search_patent_by_name_field(
                    api=api,
                    name=str(name_to_search).strip(),
                    field_suffix=attempt["field_suffix"],
                    readable_field_name=attempt["readable_name"],
                    nb_results=nb_results
                )
                
                if current_search_results:
                    search_results.extend(current_search_results) # Add results to the list
            else:
                 print(f"Skipping search for {attempt['readable_name']} as name is missing or empty.")
        
        # --- Second Pass: Search using Patent Assignment Search
        search_attempts_owner = [
            {"name_value": names.get("assignee")},
            {"name_value": plaintiff_name} # Fallback
        ]

        for attempt in search_attempts_owner:
            name_to_search = attempt.get("name_value")
            if name_to_search and len(str(name_to_search).strip()) > 0:
                    # This now returns 'patentNumber' key
                    owner_results = await api.search_patents_by_owner_in_Patent_Assignment_Search(name_to_search, nb_results=nb_results)
                    
                    if owner_results:
                        # Filter for design patents if too many results, otherwise keep all
                        if len(owner_results) > 20:
                            # Use 'patentNumber' for filtering now
                            design_owner_results = [result for result in owner_results if result.get("patentNumber", "").startswith("D")]
                            if design_owner_results:
                                print(f"Found {len(owner_results)} results in Assignment Search for '{name_to_search}', filtering for Design patents ({len(design_owner_results)} found).")
                                owner_results = design_owner_results # Limit to nb_results
                            else:
                                print(f"Found {len(owner_results)} results in Assignment Search for '{name_to_search}', but no Design patents after filtering.")

                        search_results.extend(owner_results[:nb_results]) # Add results


        if not search_results: # Check if the accumulated list is empty
            print("No search results found with any method")
            return [], ""  # No results found

        # --- Process accumulated results ---
        print(f"Processing a total of {len(search_results)} accumulated results.")
        # Remove duplicates based on patentNumber, keeping the first occurrence
        seen_patent_numbers = set()
        unique_search_results = []
        for result in search_results:
            patent_number = result.get("patentNumber")
            if patent_number and patent_number not in seen_patent_numbers:
                unique_search_results.append(result)
                seen_patent_numbers.add(patent_number)
        
        print(f"Processing {len(unique_search_results)} unique results after deduplication.")
        search_results = unique_search_results # Use the deduplicated list

        # Extract reg_nos from the final unique search results using patentNumber
        reg_nos = []
        reg_no_to_result = {}
        
        for result in search_results:
            patent_number = result.get("patentNumber")
            if patent_number:
                reg_no = clean_reg_no(patent_number) # Extract reg_no from patentNumber
                if reg_no: # Ensure clean_reg_no returned a valid number
                    reg_nos.append(reg_no)
                    reg_no_to_result[reg_no] = result # Store the result dict, keyed by the cleaned reg_no
        
        patent_list = await get_patent_data_by_reg_no(reg_nos, plaintiff_id, reg_no_to_result, api)
    
    except Exception as e:
        print(f"\033[91m ⚠️ get_patent_data_by_name: Error retrieving patent data: {str(e)}\033[0m")
        patent_list = []
        search_term_used = ""
    
    finally:
        await api.close_session() 
    
    return patent_list, search_term_used





async def get_single_patent_from_USPTO_and_send_to_NAS_and_save_to_db(document_id, result, nas, db_connection, api, patent_db_df, plaintiff_id=None):
    
    # Create document_id directory
    document_dir = os.path.join(local_ip_folder, "Patents", document_id)
    os.makedirs(document_dir, exist_ok=True)
    os.makedirs(os.path.join(document_dir, "images"), exist_ok=True)
    
    # Download the patent PDF and save it locally
    files = await api.save_patent(document_id, output_dir=document_dir)
    save_patent_files_on_NAS(document_id, nas)

    # Get design pages number using LLM
    design_page_numbers = await get_design_page_numbers(document_id)

    # Parse HTML file to extract additional data
    html_path = os.path.join(document_dir, f"{document_id}.html")
    abstract, assignee, applicant = extract_patent_data_from_html(html_path)
    if not assignee and "assignee" in result:
        assignee = result["assignee"] # From the search assignee
    
    # Replace empty strings with None for database NULL values
    inventors = result.get("inventors") or None
    title = result.get("title") or None
    date_published = result.get("datePublished") or None
    reg_no = result.get("patentNumber") or None
    type = result.get("type") or None

    
    # Create plaintiff_ids list with the current plaintiff_id if provided
    plaintiff_ids = [int(plaintiff_id)] if plaintiff_id and not pd.isna(plaintiff_id) else []

    # Ensure database connection is valid
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # Check if connection is alive and reconnect if needed
            if db_connection is None or not is_connection_alive(db_connection):
                print(f"\033[93m ⚠️ MySQL connection not available. Attempting to reconnect...\033[0m")
                db_connection = get_gz_connection()
                if db_connection is None:
                    raise Exception("Failed to establish database connection")
                    
            with db_connection.cursor() as cursor:
                cursor.execute(
                    "INSERT INTO tb_patent (reg_no, document_id, TRO, inventors, text, date_published, type, pdf_source, abstract, assignee, applicant, plaintiff_ids, design_page_numbers) "
                    "VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)", 
                    (reg_no, document_id, True, inventors, title, date_published, type, "USPTO", abstract, assignee, applicant, json.dumps(plaintiff_ids), json.dumps(design_page_numbers))
                )
                db_connection.commit()
            
            # Create a new row as a Series with the same columns as the dataframe then append the new row to the dataframe
            new_row = pd.Series({ 'reg_no': reg_no, 'document_id': document_id, 'TRO': True, 'inventors': inventors, 'text': title, 
                'date_published': date_published, 'type': type, 'pdf_source': "USPTO", 'abstract': abstract, 'assignee': assignee, 
                'applicant': applicant, 'plaintiff_ids': plaintiff_ids, 'design_page_numbers': design_page_numbers })
            patent_db_df.loc[len(patent_db_df)] = new_row
            
            # Operation successful, break the retry loop
            break
            
        except Exception as e:
            retry_count += 1
            if retry_count < max_retries:
                print(f"\033[93m ⚠️ Error saving patent to database: {str(e)}. Retrying ({retry_count}/{max_retries})...\033[0m")
                time.sleep(2)  # Wait before retrying
            else:
                print(f"\033[91m 🔥 Error saving patent to database: {str(e)}. Max retries reached.\033[0m")
                # Still return something even if DB save failed
                
    # Return patent data
    return {"document_id": document_id, "reg_no": reg_no, "text": title, "inventors": inventors, "date_published": date_published, 
        "type": type, "files": files, "abstract": abstract, "assignee": assignee, "applicant": applicant, "plaintiff_ids": plaintiff_ids, "design_page_numbers": design_page_numbers}



async def get_design_page_numbers(document_id):
    document_dir = os.path.join(local_ip_folder, "Patents", document_id)
    pdf_file_path = os.path.join(document_dir, f"{document_id}.pdf")
    prompt = """I am looking for the list of pages with the Designs / Figures / Sheets in this US patent. Please help me find them.
    The Pages with the Designs / Figures / Sheets are usually the ones with the Fig. or Sheet. written on them, big pictures and little text.
    You return your answer as a list of page numbers: [3, 4, 5, ...]
    """
    prompt_list = [("text", prompt), ("pdf_path", pdf_file_path)]
    ai_answer = await vertex_genai_multi_async(prompt_list, model_name=Constants.SMART_MODEL_FREE)
    design_page_numbers = get_list(ai_answer)
    design_page_numbers = [page_number for page_number in design_page_numbers if page_number != 1 and page_number != "1"]
    print(f"💡 Design page numbers for {document_id}: {design_page_numbers}")
    return design_page_numbers



def update_patent_plaintiff_ids(document_id, plaintiff_id, db_connection, patent_db_df):
    row = patent_db_df.loc[patent_db_df['document_id'] == document_id]
    # Convert plaintiff_id to regular Python int to avoid numpy type issues
    plaintiff_id_int = int(plaintiff_id)
    current_plaintiff_ids_list = []

    # First check if update is already needed
    if not row.empty:
        if isinstance(row['plaintiff_ids'], str):
            try:
                loaded_ids = json.loads(row['plaintiff_ids'])
                if isinstance(loaded_ids, list):
                    current_plaintiff_ids_list = loaded_ids
            except (json.JSONDecodeError, TypeError):
                pass # Keep it as empty list if parsing fails
        elif isinstance(row['plaintiff_ids'], list):
            current_plaintiff_ids_list = row['plaintiff_ids']
        
        current_plaintiff_ids_list = [int(id_val) for id_val in current_plaintiff_ids_list if id_val is not None]

        if plaintiff_id_int in current_plaintiff_ids_list:
            return # Exit the function early as the ID is already present
    
    # Update the database
    max_retries = 3
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            # Check if connection is alive and reconnect if needed
            if db_connection is None or not is_connection_alive(db_connection):
                print(f"\033[93m ⚠️ MySQL connection not available. Attempting to reconnect...\033[0m")
                db_connection = get_gz_connection()
                if db_connection is None:
                    raise Exception("Failed to establish database connection")
            
            # If we did not get the current plaintiff_ids from the dataframe, get it from the database
            with db_connection.cursor() as cursor:
                if not current_plaintiff_ids_list:
                    # Get current plaintiff_ids
                    cursor.execute("SELECT plaintiff_ids FROM tb_patent WHERE document_id = %s", (document_id,))
                    result = cursor.fetchone()
                    
                    if result:
                        # Parse the JSON string to get the list
                        try:
                            current_plaintiff_ids_list = json.loads(result[0]) if result[0] else []
                            current_plaintiff_ids_list = [int(id_val) for id_val in current_plaintiff_ids_list]
                        except (json.JSONDecodeError, TypeError):
                            current_plaintiff_ids_list = []
                    
                if plaintiff_id_int not in current_plaintiff_ids_list:
                    # Add the plaintiff_id to the list
                    updated_plaintiff_ids = current_plaintiff_ids_list + [plaintiff_id_int]
                    updated_plaintiff_ids_json = json.dumps(updated_plaintiff_ids)
                    
                    # Update the database
                    cursor.execute(
                        "UPDATE tb_patent SET plaintiff_ids = %s WHERE document_id = %s",
                        (updated_plaintiff_ids_json, document_id)
                    )
                    db_connection.commit()
                    
                    # Update the dataframe
                    idx = patent_db_df.index[patent_db_df['document_id'] == document_id].tolist()
                    if idx:
                        patent_db_df.at[idx[0], 'plaintiff_ids'] = updated_plaintiff_ids
                    
                    print(f"\033[92m ✅ Updated plaintiff_ids for patent {document_id}\033[0m")
            # If we got here, operation was successful, so break the retry loop
            break
                    
        except Exception as e:
            retry_count += 1
            if retry_count < max_retries:
                print(f"\033[93m ⚠️ Error updating plaintiff_ids for patent {document_id}: {str(e)}. Retrying ({retry_count}/{max_retries})...\033[0m")
                time.sleep(2)  # Wait before retrying
            else:
                print(f"\033[91m 🔥 Error updating plaintiff_ids for patent {document_id}: {str(e)}. Max retries reached.\033[0m")


def extract_patent_data_from_html(html_path):
    """Extract patent data from HTML document"""
    try:
        if not os.path.exists(html_path):
            return None, None, None
            
        with open(html_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
        soup = BeautifulSoup(content, 'html.parser')
        
        # Extract abstract
        abstract_elem = soup.find('article', class_='bottom-border')
        abstract = None
        if abstract_elem:
            abstract_p = abstract_elem.find('p')
            abstract = abstract_p.text.strip() if abstract_p else None

        # abstract_elems = soup.findAll('article', class_='bottom-border')
        # abstract = None
        # for abstract_elem in abstract_elems:
        #     abstract_h3 = abstract_elem.find('h3')
        #     if abstract_h3 and abstract_h3.text.strip() == "Abstract":
        #         abstract_p = abstract_elem.find('p')
        #         abstract = abstract_p.text.strip() if abstract_p else None
        #         break
            
        # Extract assignee and applicant
        assignee = None
        applicant = None
        
        grid_container = soup.find('section', class_='grid-container-12')
        if grid_container:
            # Find all grid columns
            grid_cols = grid_container.find_all('p', class_='grid-col-2')
            
            for col in grid_cols:
                label = col.text.strip().lower()
                if 'assignee' in label:
                    assignee_elem = col.find_next('p', class_='grid-col-10')
                    if assignee_elem and assignee_elem.find('strong'):
                        assignee = assignee_elem.find('strong').text.strip()
                elif 'applicant' in label:
                    applicant_elem = col.find_next('p', class_='grid-col-10')
                    if applicant_elem and applicant_elem.find('strong'):
                        applicant = applicant_elem.find('strong').text.strip()
                        
        return abstract, assignee, applicant
    except Exception as e:
        print(f"\033[91m 🔥 Error extracting patent data from HTML: {str(e)}\033[0m")
        return None, None, None


def get_patent_from_NAS(document_id, nas):
    document_dir = os.path.join(local_ip_folder, "Patents", document_id)
    pdf_local_path = os.path.join(document_dir, f"{document_id}.pdf")
    html_local_path = os.path.join(document_dir, f"{document_id}.html")
    
    # Check if files already exist locally
    if os.path.exists(pdf_local_path) and os.path.exists(html_local_path):
        return True, pdf_local_path, html_local_path
    else:
        # Need to get the entire folder from NAS
        nas_document_dir = f"{nas_ip_folder}/Patents/{document_id}"
        
        # Check if folder exists on NAS
        if nas.ssh_exists(nas_document_dir):
            os.makedirs(document_dir, exist_ok=True)
            
            # Get the entire folder from NAS to local
            if nas.ssh_nas_to_local(nas_document_dir, document_dir):
                # After transfer check if files exist
                if os.path.exists(pdf_local_path) and os.path.exists(html_local_path):
                    return True, pdf_local_path, html_local_path
    
    print(f"\033[91m 🔥 Trying to get patent from NAS but files not found: {document_id}\033[0m")
    return False, None, None

def save_patent_files_on_NAS(document_id, nas):
    document_dir = os.path.join(local_ip_folder, "Patents", document_id)
    
    # Check if document directory exists locally
    if os.path.exists(document_dir):
        # Create document directory on NAS
        nas_document_dir = f"{nas_ip_folder}/Patents/{document_id}"
        
        # Transfer the entire directory to NAS
        nas.ssh_local_to_nas(document_dir, nas_document_dir)
    else:
        print(f"\033[91m 🔥 Trying to save to NAS but document directory not found: {document_id}\033[0m")


def clean_reg_no(reg_no):
    reg_no = str(reg_no)
    reg_no = reg_no.replace(" ", "").replace(",", "")
    # Extract only the first sequence of digits
    match = re.search(r'D?\d+', reg_no)
    reg_no = match.group() if match else reg_no
    return reg_no