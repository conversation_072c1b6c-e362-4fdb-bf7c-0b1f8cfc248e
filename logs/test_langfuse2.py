import asyncio
from collections import defaultdict
from concurrent.futures import <PERSON>hr<PERSON><PERSON>oolExecutor
from time import sleep
from typing import Optional

import pytest
from langchain.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI

from langfuse import get_client, observe
from langfuse.langchain import <PERSON>back<PERSON>andler
from langfuse.media import LangfuseMedia
from logs.test_langfuse import sdc_test

mock_metadata = {"key": "metadata"}
mock_deep_metadata = {"key": "mock_deep_metadata"}
mock_session_id = "session-id-1"
mock_args = (1, 2, 3)
mock_kwargs = {"a": 1, "b": 2, "c": 3}


def test_nested_observations():
    mock_name = "test_nested_observations"
    langfuse = get_client()
    mock_trace_id = langfuse.create_trace_id()

    @observe(as_type="generation", name="level_3", capture_output=False)
    def level_3_function():
        langfuse.update_current_generation(metadata=mock_metadata)
        langfuse.update_current_generation(
            metadata=mock_deep_metadata,
            usage_details={"input": 150, "output": 50, "total": 300},
            model="gpt-3.5-turbo",
            output="mock_output",
        )
        langfuse.update_current_generation(version="version-1")
        langfuse.update_current_trace(session_id=mock_session_id, name=mock_name)

        langfuse.update_current_trace(
            user_id="user_id",
        )

        return "level_3"

    @observe(name="level_2_manually_set")
    def level_2_function():
        level_3_function()
        sdc_test()
        langfuse.update_current_span(metadata=mock_metadata)

        return "level_2"

    @observe()
    def level_1_function(*args, **kwargs):
        level_2_function()

        return "level_1"

    result = level_1_function(
        *mock_args, **mock_kwargs, langfuse_trace_id=mock_trace_id
    )
    langfuse.flush()



def test_decorated_class_and_instance_methods():
    mock_name = "test_decorated_class_and_instance_methods"
    langfuse = get_client()
    mock_trace_id = langfuse.create_trace_id()

    class TestClass:
        @classmethod
        @observe(name="class-method")
        def class_method(cls, *args, **kwargs):
            langfuse.update_current_span()
            return "class_method"

        @observe(as_type="generation", capture_output=False)
        def level_3_function(self):
            langfuse.update_current_generation(metadata=mock_metadata)
            langfuse.update_current_generation(
                metadata=mock_deep_metadata,
                usage_details={"input": 150, "output": 50, "total": 300},
                model="gpt-3.5-turbo",
                output="mock_output",
            )

            langfuse.update_current_trace(session_id=mock_session_id, name=mock_name)

            return "level_3"

        @observe()
        def level_2_function(self):
            TestClass.class_method()

            self.level_3_function()
            langfuse.update_current_span(metadata=mock_metadata)

            return "level_2"

        @observe()
        def level_1_function(self, *args, **kwargs):
            self.level_2_function()

            return "level_1"

    result = TestClass().level_1_function(
        *mock_args, **mock_kwargs, langfuse_trace_id=mock_trace_id
    )

    langfuse.flush()


def test_generator_as_return_value():
    langfuse = get_client()
    mock_trace_id = langfuse.create_trace_id()
    mock_output = "Hello, World!"

    def custom_transform_to_string(x):
        return "--".join(x)

    def generator_function():
        yield "Hello"
        yield ", "
        yield "World!"

    @observe(transform_to_string=custom_transform_to_string)
    def nested():
        return generator_function()

    @observe()
    def main(**kwargs):
        gen = nested()

        result = ""
        for item in gen:
            result += item

        return result

    result = main(langfuse_trace_id=mock_trace_id)
    langfuse.flush()


@pytest.mark.asyncio
async def test_async_generator_as_return_value():
    langfuse = get_client()
    mock_trace_id = langfuse.create_trace_id()
    mock_output = "Hello, async World!"

    def custom_transform_to_string(x):
        return "--".join(x)

    @observe(transform_to_string=custom_transform_to_string)
    async def async_generator_function():
        await asyncio.sleep(0.1)  # Simulate async operation
        yield "Hello"
        await asyncio.sleep(0.1)
        yield ", async "
        await asyncio.sleep(0.1)
        yield "World!"

    @observe()
    async def main_async(**kwargs):
        gen = async_generator_function()

        result = ""
        async for item in gen:
            result += item

        return result

    result = await main_async(langfuse_trace_id=mock_trace_id)
    langfuse.flush()



@pytest.mark.asyncio
async def test_async_nested_openai_chat_stream():
    from langfuse.openai import AsyncOpenAI

    mock_name = "test_async_nested_openai_chat_stream"
    langfuse = get_client()
    mock_trace_id = langfuse.create_trace_id()
    mock_tags = ["tag1", "tag2"]
    mock_session_id = "session-id-1"
    mock_user_id = "user-id-1"

    @observe(capture_output=False)
    async def level_2_function():
        gen = await AsyncOpenAI().chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "1 + 1 = "}],
            temperature=0,
            metadata={"someKey": "someResponse"},
            stream=True,
        )

        langfuse.update_current_trace(
            session_id=mock_session_id,
            user_id=mock_user_id,
            tags=mock_tags,
        )

        async for c in gen:
            print(c)

        langfuse.update_current_span(metadata=mock_metadata)
        langfuse.update_current_trace(name=mock_name)

        return "level_2"

    @observe()
    async def level_1_function(*args, **kwargs):
        await level_2_function()

        return "level_1"

    result = await level_1_function(
        *mock_args, **mock_kwargs, langfuse_trace_id=mock_trace_id
    )
    langfuse.flush()


def test_generator_as_function_input():
    langfuse = get_client()
    mock_trace_id = langfuse.create_trace_id()
    mock_output = "Hello, World!"

    def generator_function():
        yield "Hello"
        yield ", "
        yield "World!"

    @observe()
    def nested(gen):
        result = ""
        for item in gen:
            result += item

        return result

    @observe()
    def main(**kwargs):
        gen = generator_function()

        return nested(gen)

    result = main(langfuse_trace_id=mock_trace_id)
    langfuse.flush()


def test_nest_list_of_generator_as_function_IO():
    langfuse = get_client()
    mock_trace_id = langfuse.create_trace_id()

    def generator_function():
        yield "Hello"
        yield ", "
        yield "World!"

    @observe()
    def nested(list_of_gens):
        return list_of_gens

    @observe()
    def main(**kwargs):
        gen = generator_function()

        return nested([(gen, gen)])

    main(langfuse_trace_id=mock_trace_id)
    langfuse.flush()



def test_return_dict_for_output():
    langfuse = get_client()
    mock_trace_id = langfuse.create_trace_id()
    mock_output = {"key": "value"}

    @observe()
    def function():
        return mock_output

    result = function(langfuse_trace_id=mock_trace_id)
    langfuse.flush()



def test_media():
    langfuse = get_client()
    mock_trace_id = langfuse.create_trace_id()

    with open("static/bitcoin.pdf", "rb") as pdf_file:
        pdf_bytes = pdf_file.read()

    media = LangfuseMedia(content_bytes=pdf_bytes, content_type="application/pdf")

    @observe()
    def main():
        sleep(1)
        langfuse.update_current_trace(
            input={
                "context": {
                    "nested": media,
                },
            },
            output={
                "context": {
                    "nested": media,
                },
            },
            metadata={
                "context": {
                    "nested": media,
                },
            },
        )

    main(langfuse_trace_id=mock_trace_id)

    langfuse.flush()



def test_merge_metadata_and_tags():
    langfuse = get_client()
    mock_trace_id = langfuse.create_trace_id()

    @observe
    def nested():
        langfuse.update_current_trace(metadata={"key2": "value2"}, tags=["tag2"])

    @observe
    def main():
        langfuse.update_current_trace(metadata={"key1": "value1"}, tags=["tag1"])

        nested()

    main(langfuse_trace_id=mock_trace_id)

    langfuse.flush()
    
if __name__ == "__main__":
    test_nested_observations()
    