#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update the tb_case database table with filtered cases data.
This script removes specific case IDs and updates the database.
"""

from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
from DatabaseManagement.Connections import get_gz_connection

def update_database_with_filtered_cases():
    """
    Update the database by removing specific case IDs and updating the tb_case table.
    """
    print("🔄 Loading current cases from database...")
    
    # Load current cases data
    all_cases_df = get_table_from_GZ("tb_case", force_refresh=True)
    print(f"📊 Loaded {len(all_cases_df)} cases from database")
    
    # IDs to remove
    ids_to_remove = [14416, 14425, 14422, 14420, 14419, 14415, 14421, 14423, 14418, 14417, 14424, 14426]
    
    # Show cases that will be removed
    cases_to_remove = all_cases_df[all_cases_df['id'].isin(ids_to_remove)]
    print(f"\n🗑️  Cases to be removed ({len(cases_to_remove)}):")
    if len(cases_to_remove) > 0:
        print(cases_to_remove[['id', 'docket', 'court', 'date_filed']].to_string(index=False))
    else:
        print("   No cases found with the specified IDs")
    
    # Filter out the cases to remove
    filtered_cases_df = all_cases_df[~all_cases_df['id'].isin(ids_to_remove)]
    print(f"\n📈 Filtered dataset: {len(filtered_cases_df)} cases (removed {len(all_cases_df) - len(filtered_cases_df)} cases)")
    
    # Confirm before proceeding
    response = input(f"\n⚠️  This will update the database to remove {len(ids_to_remove)} cases. Continue? (y/N): ")
    if response.lower() != 'y':
        print("❌ Operation cancelled")
        return
    
    print("\n🔄 Updating database...")
    
    # Method 1: Use the standard update function (this will update existing records)
    # Note: This approach updates existing records but doesn't delete the removed ones
    # insert_and_update_df_to_GZ_batch(filtered_cases_df, "tb_case", "id")
    
    # Method 2: Delete specific records first, then update
    # This is more explicit about what we're doing
    delete_specific_cases(ids_to_remove)
    
    print("✅ Database update completed!")

def delete_specific_cases(case_ids):
    """
    Delete specific cases from the database by their IDs.
    
    Args:
        case_ids (list): List of case IDs to delete
    """
    if not case_ids:
        print("No cases to delete")
        return
    
    print(f"🗑️  Deleting {len(case_ids)} cases from database...")
    
    connection = get_gz_connection()
    cursor = connection.cursor()
    
    try:
        # Delete cases from tb_case table
        placeholders = ','.join(['%s'] * len(case_ids))
        delete_query = f"DELETE FROM tb_case WHERE id IN ({placeholders})"
        cursor.execute(delete_query, case_ids)
        
        deleted_count = cursor.rowcount
        print(f"   Deleted {deleted_count} cases from tb_case")
        
        # Also delete related case steps if they exist
        steps_delete_query = f"DELETE FROM tb_case_steps WHERE case_id IN ({placeholders})"
        cursor.execute(steps_delete_query, case_ids)
        
        deleted_steps_count = cursor.rowcount
        print(f"   Deleted {deleted_steps_count} related case steps from tb_case_steps")
        
        connection.commit()
        print("   ✅ Deletion completed successfully")
        
    except Exception as e:
        connection.rollback()
        print(f"   ❌ Error during deletion: {e}")
        raise
    finally:
        cursor.close()
        connection.close()

if __name__ == "__main__":
    update_database_with_filtered_cases()
