from DatabaseManagement.ImportExport import get_table_from_GZ
import pandas as pd
from Common.Constants import court_mapping
import zlib
import base64
import json
import os

metrics_file = os.path.join(os.getcwd(), "Errors", "Database_Reports", "database_metrics.csv")


def create_and_send_statistics_report():
    print("Starting database quality report generation...")
    
    # Gather data and calculate metrics
    metrics_dict = gather_data_for_report()
    
    # Store metrics in CSV
    store_metrics_to_csv(metrics_dict)

    # Create HTML report with changes
    html_report = create_html_report_with_changes(metrics_dict)
    print("Data gathering complete, generating report...")



    # Save the report locally
    report_file = f"database_quality_report_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.html"
    os.makedirs(os.path.join(os.getcwd(), "Errors"), exist_ok=True)
    os.makedirs(os.path.join(os.getcwd(), "Errors", "Database_Reports"), exist_ok=True)
    report_path = os.path.join(os.getcwd(), "Errors", "Database_Reports", report_file)
    save_report_to_file(html_report, report_path)
    
    # Ask user if they want to send the email
    send_email = input("Do you want to email the report? (y/n): ").lower().strip()
    if send_email == 'y':
        print("Sending email...")
        if send_report_email(html_report):
            pass
        else:
            print(f"Report is available at: {report_file}")
    else:
        print(f"Email not sent. Report is available at: {report_file}")
    
    print("Report generation complete!")



def gather_data_for_report():
    """
    Gathers data and returns a dictionary of metrics.
    """
    df_cases = get_table_from_GZ("tb_case", force_refresh=True)
    df_cases_steps = get_table_from_GZ("tb_case_steps", force_refresh=True)
    df_plaintiffs = get_table_from_GZ("tb_plaintiff", force_refresh=True)

    
    ### Cases
    # Cases no cause
    df_cases_scraped = df_cases[df_cases["cause"].notna()]
    number_of_cases_not_scraped = len(df_cases) - len(df_cases_scraped)
    
    
    # Cases no images
    df_no_images = df_cases_scraped[df_cases_scraped["images"] == {'trademarks': {}, 'patents': {}, 'copyrights': {}}]
    number_of_cases_without_images = len(df_no_images)

    # Cases no plaintiff_id in plaintiff_df
    df_cases_no_plaintiff_id = df_cases[~df_cases["plaintiff_id"].isin(df_plaintiffs["id"])]
    number_of_cases_no_plaintiff_id = len(df_cases_no_plaintiff_id)


    # Cases duplicates
    df_cases_duplicates = df_cases[df_cases.duplicated(subset=["docket", "date_filed"])]
    number_of_cases_duplicates = len(df_cases_duplicates)


    # Cases ai summary no translation
    aisummury = df_cases_scraped['aisummary'].apply(lambda x: json.loads(x) if (isinstance(x, str) and x[0] == "{") else None)
    df_cases_no_chinese_overview = df_cases_scraped[aisummury.isna()]
    number_of_cases_no_chinese_overview = len(df_cases_no_chinese_overview)
    number_of_cases_no_chinese_overview_above_10000 = len(df_cases_no_chinese_overview[df_cases_no_chinese_overview["id"] > 10000])


    # Cases no steps
    df_cases_no_steps = df_cases_scraped[~df_cases_scraped["id"].isin(df_cases_steps["case_id"])]
    number_of_cases_no_steps = len(df_cases_no_steps)


    # Cases court not in court_mapping
    df_cases_court_not_in_court_mapping = df_cases[~df_cases["court"].isin(court_mapping.values())]
    number_of_cases_court_not_in_court_mapping = len(df_cases_court_not_in_court_mapping)


    ### Plaintiffs
    # Duplicate plaintiffs
    df_plaintiffs_duplicates = df_plaintiffs[df_plaintiffs["plaintiff_name"].str.lower().duplicated(keep='first')]
    number_of_plaintiffs_duplicates = len(df_plaintiffs_duplicates)

    # Plaintiffs without cases
    df_plaintiffs_no_cases = df_plaintiffs[~df_plaintiffs["id"].isin(df_cases["plaintiff_id"])]
    number_of_plaintiffs_no_cases = len(df_plaintiffs_no_cases)

    # Plaintiffs no overview
    df_plaintiffs_with_overview = df_plaintiffs[df_plaintiffs["plaintiff_overview"].notna()]
    number_of_plaintiffs_no_overview = len(df_plaintiffs) - len(df_plaintiffs_with_overview)
    
    # Plaintiffs no overview translation
    number_of_plaintiff_overview_missing = sum(df_plaintiffs["plaintiff_overview"].isna())
    plaintiff_overview = df_plaintiffs["plaintiff_overview"].apply(lambda x: json.loads(x) if isinstance(x, str) and x.startswith("{") else None)
    number_of_plaintiff_overview_missing_translation = sum(plaintiff_overview.isna()) - number_of_plaintiffs_no_overview




    ### Steps
    # Steps without case
    df_steps_no_case = df_cases_steps[~df_cases_steps["case_id"].isin(df_cases["id"])]
    number_of_steps_no_case = len(df_steps_no_case)

    # Duplicate steps
    df_steps_duplicates = df_cases_steps[df_cases_steps.duplicated(subset=["case_id", "step_nb"])]
    number_of_steps_duplicates = len(df_steps_duplicates)

    # Steps no translation
    df_steps_no_translation = df_cases_steps[df_cases_steps["proceeding_text_cn"].isna()]
    number_of_steps_no_translation = len(df_steps_no_translation)
    df_steps_NULL_translation = df_cases_steps[df_cases_steps["proceeding_text_cn"]=="[NULL]"]
    number_of_steps_NULL_translation = len(df_steps_NULL_translation)
    
    # Steps no translation above case_id 10000
    df_steps_no_translation_above_10000 = df_cases_steps[(df_cases_steps["case_id"] > 10000) & (df_cases_steps["proceeding_text_cn"].isna())]
    number_of_steps_no_translation_above_10000 = len(df_steps_no_translation_above_10000)
    df_steps_NULL_translation_above_10000 = df_cases_steps[(df_cases_steps["case_id"] > 10000) & (df_cases_steps["proceeding_text_cn"]=="[NULL]")]
    number_of_steps_NULL_translation_above_10000 = len(df_steps_NULL_translation_above_10000)

    return {
        "timestamp": pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S"),
        "total_cases": len(df_cases),
        "cases_without_cause": number_of_cases_not_scraped,
        "cases_without_images": number_of_cases_without_images,
        "cases_without_plaintiff_id": number_of_cases_no_plaintiff_id,
        "duplicate_cases": number_of_cases_duplicates,
        "cases_without_chinese_overview": number_of_cases_no_chinese_overview,
        "cases_without_chinese_overview_above_10000": number_of_cases_no_chinese_overview_above_10000,
        "cases_without_steps": number_of_cases_no_steps,
        "cases_with_court_not_in_mapping": number_of_cases_court_not_in_court_mapping,
        "total_plaintiffs": len(df_plaintiffs),
        "duplicate_plaintiffs": number_of_plaintiffs_duplicates,
        "plaintiffs_without_cases": number_of_plaintiffs_no_cases,
        "plaintiffs_without_overview": number_of_plaintiffs_no_overview,
        "plaintiffs_without_chinese_overview": number_of_plaintiff_overview_missing_translation,
        "total_steps": len(df_cases_steps),
        "steps_without_case": number_of_steps_no_case,
        "duplicate_steps": number_of_steps_duplicates,
        "steps_without_translation": number_of_steps_no_translation,
        "steps_with_NULL_translation": number_of_steps_NULL_translation,
        "steps_without_translation_above_10000": number_of_steps_no_translation_above_10000,
        "steps_with_NULL_translation_above_10000": number_of_steps_NULL_translation_above_10000
    }



def store_metrics_to_csv(metrics_dict):
    """
    Stores the metrics dictionary to a CSV file.
    """
    
    try:
        if os.path.exists(metrics_file):
            df_metrics = pd.read_csv(metrics_file)
            # Convert 'timestamp' back to datetime objects (important for proper sorting/comparison)
            df_metrics['timestamp'] = pd.to_datetime(df_metrics['timestamp'])
        else:
            df_metrics = pd.DataFrame()

        df_metrics = pd.concat([df_metrics, pd.DataFrame([metrics_dict])], ignore_index=True)
        df_metrics.to_csv(metrics_file, index=False)
    except Exception as e:
        print(f"Error writing to CSV: {e}")


def create_html_report_with_changes(current_metrics):
    """
    Create a formatted HTML report of all database metrics, including changes.
    """
    # Load previous metrics
    previous_metrics = {}
    try:
        if os.path.exists(metrics_file):
            df_metrics = pd.read_csv(metrics_file)
            if len(df_metrics) > 1:  # Ensure there's a previous entry
                previous_metrics = df_metrics.iloc[-2].to_dict() # -2 gets the second to last row
    except Exception as e:
        print(f"Error reading from CSV: {e}")
        #If there is an error reading, continue, do not stop report generation

    html = [
        '<html>',
        '<head>',
        '<style>',
        'body { font-family: Arial, sans-serif; margin: 20px; }',
        'h1 { color: #2c3e50; }',
        'h2 { color: #3498db; margin-top: 30px; border-bottom: 1px solid #3498db; padding-bottom: 5px; }',
        '.metric { margin: 10px 0; padding: 10px; background-color: #f8f9fa; border-radius: 5px; }',
        '.metric-name { font-weight: bold; display: inline-block; width: 450px; }',
        '.metric-value { font-weight: bold; color: #e74c3c; }',
        '.metric-warning { background-color: #ffeaa7; }',
        '.metric-error { background-color: #fab1a0; }',
        '.visualization { margin-top: 30px; }',
        '.flex-container { display: flex; }',
        '.summary-box { flex: 1; padding: 15px; margin: 10px; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }',
        '.good { background-color: #d5f5e3; }',
        '.warning { background-color: #fef9e7; }',
        '.error { background-color: #fdedec; }',
        '</style>',
        '<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>',
        '<script type="text/javascript">',
        'google.charts.load("current", {packages:["corechart"]});',
        'google.charts.setOnLoadCallback(drawChart);',
        'function drawChart() {',
        '  // This is just a placeholder for future chart functionality',
        '}',
        '</script>',
        '</head>',
        '<body>',
        '<h1>Database Quality Report</h1>',
        f'<p>Report generated on: {pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")}</p>',
        
        '<h2>Cases Issues</h2>'
    ]

    # Helper function to add a metric to the report
    def add_metric(name, key, warning_threshold=10, error_threshold=100, max_value=None):
        value = current_metrics.get(key, 0)
        formatted_value = f"{value:,}"  # Format with comma

        # Calculate change
        change = 0
        if key in previous_metrics:
            change = value - previous_metrics[key]
        
        change_str = ""
        color = "black"  # Default color for no change
        if change > 0:
            change_str = f" (+{change:,})"  # Format with comma
            color = "red"
        elif change < 0:
            change_str = f" ({change:,})"  # Format with comma
            color = "green"

        css_class = 'metric'
        if value >= error_threshold:
            css_class += ' metric-error'
        elif value >= warning_threshold:
            css_class += ' metric-warning'
        
        # Calculate bar width percentage
        if max_value is None:
            max_value = error_threshold * 2
        bar_width_pct = min(100, (value / max_value) * 100)
        
        html.append(f'<div class="{css_class}"><span class="metric-name">{name}: </span><span class="metric-value">{formatted_value}<span style="color:{color};">{change_str}</span></span>')



    # Cases section metrics
    cases_metrics = [
        ("Total cases", "total_cases"),
        ("Cases without cause", "cases_without_cause"),
        ("Cases without images", "cases_without_images"),
        ("Cases without plaintiff id in plaintiff table", "cases_without_plaintiff_id"),
        ("Duplicate cases (same docket and date filed)", "duplicate_cases"),
        ("Cases without Chinese translation in overview", "cases_without_chinese_overview"),
        ("Cases without Chinese translation in overview (case id > 10,000)", "cases_without_chinese_overview_above_10000"),
        ("Cases without any steps", "cases_without_steps"),
        ("Cases with court not in court mapping", "cases_with_court_not_in_mapping")
    ]
    
    
    # Calculate max value for consistent bar scaling (using current values)
    cases_max = max([current_metrics.get(key, 0) for _, key in cases_metrics])
    for name, key in cases_metrics:
        add_metric(name, key, max_value=cases_max)

    # Plaintiffs section
    html.append('<h2>Plaintiffs Issues</h2>')

    plaintiff_metrics = [
        ("Total plaintiffs", "total_plaintiffs"),
        ("Duplicate plaintiffs (same name)", "duplicate_plaintiffs"),
        ("Plaintiffs without associated cases", "plaintiffs_without_cases"),
        ("Plaintiffs without overview", "plaintiffs_without_overview"),
        ("Plaintiffs without Chinese translation in overview", "plaintiffs_without_chinese_overview")
    ]

    plaintiff_max = max([current_metrics.get(key, 0) for _, key in plaintiff_metrics])
    for name, key in plaintiff_metrics:
        add_metric(name, key, max_value=plaintiff_max)

    # Steps section
    html.append('<h2>Steps Issues</h2>')

    steps_metrics = [
        ("Total steps", "total_steps"),
        ("Steps without associated case", "steps_without_case"),
        ("Duplicate steps (same case id and step nb)", "duplicate_steps"),
        ("Steps without translation", "steps_without_translation"),
        ("Steps with [NULL] translation", "steps_with_NULL_translation"),
        ("Steps without translation (case id > 10,000)", "steps_without_translation_above_10000"),
        ("Steps with [NULL] translation (case id > 10,000)", "steps_with_NULL_translation_above_10000")
    ]


    steps_max = max([current_metrics.get(key, 0) for _, key in steps_metrics])
    for name, key in steps_metrics:
        add_metric(name, key, max_value=steps_max)


    html.append('</body>')
    html.append('</html>')

    html_report = '\n'.join(html)
    return html_report



# Save the report to a file
def save_report_to_file(html_content, filename):
    """
    Save the HTML report to a file
    """    
    try:
        with open(filename, "w", encoding="utf-8") as f:
            f.write(html_content)
        print(f"Report saved to {filename}")
        return True
    except Exception as e:
        print(f"Failed to save report to file: {str(e)}")
        return False
    
def send_report_email(html_report):
    """
    Send the report as an email
    """
    import smtplib
    from email.mime.text import MIMEText
    from email.mime.multipart import MIMEMultipart
    
    
    # Check for environment variables
    if "OCI_EMAIL_USERNAME" not in os.environ or "OCI_EMAIL_PASSWORD" not in os.environ:
        print("Error: OCI email credentials not found in environment variables.")
        return False

    # Compose email
    msg = MIMEMultipart()
    msg['From'] = '<EMAIL>'
    msg['To'] = '<EMAIL>' #, <EMAIL>, <EMAIL>'
    msg['Subject'] = f'Database Quality Report - {pd.Timestamp.now().strftime("%Y-%m-%d")}'
    
    # Attach HTML version
    msg.attach(MIMEText(html_report, 'html'))
    
    # Send email via SMTP server
    try:
        with smtplib.SMTP('smtp.email.us-ashburn-1.oci.oraclecloud.com', 587) as server:
            server.starttls()
            server.login(os.getenv('OCI_EMAIL_USERNAME'), os.getenv('OCI_EMAIL_PASSWORD'))
            server.send_message(msg)
        
        print("Report email sent successfully!")
        return True
    except Exception as e:
        print(f"Failed to send email: {str(e)}")
        return False
    
    
if __name__ == "__main__":
    create_and_send_statistics_report()













    
    
    
    
