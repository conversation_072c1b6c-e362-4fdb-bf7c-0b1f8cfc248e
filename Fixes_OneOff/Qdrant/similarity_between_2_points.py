import os
import numpy as np
from qdrant_client import QdrantClient
from dotenv import load_dotenv

# Load environment variables from the correct .env file
load_dotenv(os.path.join(os.getcwd(), "Qdrant", ".env"))

# Get Qdrant API details from environment variables
QDRANT_URL = os.getenv("QDRANT_URL")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")

# Initialize Qdrant client
qdrant_client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY)

# Define the point IDs to compare
point_id_1 = "4cbba3e5-9cf4-5bf5-919c-b4e4a6613149"
point_id_2 = "b27b7f86-7923-5288-aa98-f1fd6449b806"

# Retrieve the vectors from Qdrant
try:
    points = qdrant_client.retrieve(
        collection_name="IP_Assets",
        ids=[point_id_1, point_id_2],
        with_vectors=True
    )

    if len(points) < 2:
        print("Could not retrieve one or both points.")
    else:
        # Extract vectors
        vector1 = None
        vector2 = None

        for point in points:
            if point.id == point_id_1:
                vector1 = point.vector['siglip_vector']
            elif point.id == point_id_2:
                vector2 = point.vector['siglip_vector']

        if vector1 and vector2:
            # Calculate cosine similarity
            vector1 = np.array(vector1)
            vector2 = np.array(vector2)
            cosine_similarity = np.dot(vector1, vector2) / (np.linalg.norm(vector1) * np.linalg.norm(vector2))
            
            print(f"Cosine similarity between {point_id_1} and {point_id_2}: {cosine_similarity}")
        else:
            print("Could not find vectors for one or both points.")

except Exception as e:
    print(f"An error occurred: {e}")