from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
from DatabaseManagement.Connections import get_gz_connection
from Common.Constants import court_mapping


def delete_duplicate_cases(df_cases):
    count = 0
    connection = get_gz_connection()
    cursor = connection.cursor()

    unique_cases = set()
    case_to_delete = []
    for index, row in df_cases.iterrows():
        if (row["docket"], row["date_filed"]) in unique_cases:
            df_same = df_cases[(df_cases["docket"] == row["docket"]) & (df_cases["date_filed"] == row["date_filed"])]
            print(f"Duplicate case: {row['docket']} - {row['date_filed']}: {', '.join(df_same['id'].astype(str))}")
            print(df_same[["id", "docket", "date_filed", "update_time", "court"]])
            if len(df_same.iloc[0]["images"]) >= len(df_same.iloc[1]["images"]):
                case_to_delete.append(df_same.iloc[1]["id"])
                print(f"Deleting case: {df_same.iloc[1]['id']}")
            print()
            count += 1
        else:
            unique_cases.add((row["docket"], row["date_filed"]))

    for case_id in case_to_delete:
        cursor.execute("DELETE FROM tb_case WHERE id = %s", (int(case_id),))

    connection.commit()
    cursor.close()
    connection.close()
    print(f"There were {count} duplicate cases.")
        

def fix_court_name(df_cases):
    cases_index_fixed = []
    for index, row in df_cases.iterrows():
        if row["court"] in court_mapping.keys():
            df_cases.loc[index, "court"] = court_mapping[row["court"]]
            cases_index_fixed.append(index)
        elif row["court"] not in court_mapping.values():
            print(f"Court {row['court']} not found in court_mapping. Please add it.")
            
    df_to_update = df_cases[df_cases.index.isin(cases_index_fixed)]
    insert_and_update_df_to_GZ_batch(df_to_update, "tb_case", "id")

    print(f"There were {len(cases_index_fixed)} courts fixed.")

if __name__ == "__main__":
    df_cases = get_table_from_GZ("tb_case", force_refresh=True)
    fix_court_name(df_cases)
    # delete_duplicate_cases(df_cases)
    

