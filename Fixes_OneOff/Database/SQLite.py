import sqlite3
import subprocess
import shutil
from pathlib import Path
import os

def attempt_database_recovery(db_path):
    """
    Attempts to recover a corrupted SQLite database using multiple methods
    Returns: (success: bool, recovered_path: str)
    """
    db_path = Path(db_path)
    backup_path = db_path.with_suffix('.db.backup')
    recovered_path = db_path.with_suffix('.db.recovered')
    
    # Create backup first
    shutil.copy2(db_path, backup_path)
    
    # Method 1: Try VACUUM
    try:
        conn = sqlite3.connect(str(db_path))
        conn.execute("VACUUM;")
        conn.close()
        
        # Verify if this fixed it
        conn = sqlite3.connect(str(db_path))
        conn.execute("SELECT 1")
        conn.close()
        print("Database recovered using VACUUM")
        return True, str(db_path)
    except sqlite3.DatabaseError:
        print("VACUUM recovery failed, trying next method...")
    
    # Method 2: Dump and reload
    try:
        # Dump the database
        dump_path = db_path.with_suffix('.sql')
        subprocess.run(['sqlite3', str(db_path), '.dump'], 
                      stdout=open(dump_path, 'w'),
                      check=True)
        
        # Create new database from dump
        subprocess.run(['sqlite3', str(recovered_path)],
                      stdin=open(dump_path, 'r'),
                      check=True)
        
        # Verify new database
        conn = sqlite3.connect(str(recovered_path))
        conn.execute("SELECT 1")
        conn.close()
        print("Database recovered using dump and reload")
        return True, str(recovered_path)
    except (subprocess.SubprocessError, sqlite3.DatabaseError) as e:
        print(f"Dump and reload recovery failed: {e}")
    
    # Method 3: Try sqlite_recover if available
    try:
        subprocess.run(['sqlite_recover', str(db_path), str(recovered_path)],
                      check=True)
        
        # Verify recovered database
        conn = sqlite3.connect(str(recovered_path))
        conn.execute("SELECT 1")
        conn.close()
        print("Database recovered using sqlite_recover")
        return True, str(recovered_path)
    except (subprocess.SubprocessError, sqlite3.DatabaseError) as e:
        print(f"sqlite_recover failed: {e}")
    except FileNotFoundError:
        print("sqlite_recover tool not installed")
    
    return False, None

# Usage example:
if __name__ == "__main__":
    success, recovered_file = attempt_database_recovery("/app/data/db/runs.db")
    success, recovered_file = attempt_database_recovery(os.path.join(os.getcwd(), 'runs.db'))
    if success:
        print(f"Database recovered successfully to: {recovered_file}")
    else:
        print("All recovery attempts failed")