import redis
 
# Replace with your WSL IP address and Redis port
wsl_ip_address = "127.0.0.1"
redis_port = 6379
 
try:
    # Create a Redis client object
    r = redis.Redis(host=wsl_ip_address, port=redis_port, decode_responses=True, password="wiuei787()2387")
 
    # Test the connection with a PING command
    response = r.ping()
 
    if response:
        print(f"Successfully connected to Redis server at {wsl_ip_address}:{redis_port}!")
    else:
        print(f"Connection to Redis server failed.")
 
except redis.exceptions.ConnectionError as e:
    print(f"Error connecting to Redis: {e}")