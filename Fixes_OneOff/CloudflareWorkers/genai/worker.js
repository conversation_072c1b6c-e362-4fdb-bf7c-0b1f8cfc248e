/**
 * Cloudflare Worker for Google API Relay (JavaScript)
 * This worker handles Google API authentication and content generation
 */

addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request));
});

/**
 * Handle the request by routing it to the appropriate Google API endpoint
 * @param {Request} request - Original request
 */
async function handleRequest(request) {
  // Handle CORS preflight requests
  if (request.method === 'OPTIONS') {
    return handleCORS();
  }
  
  try {
    const url = new URL(request.url);
    
    // Extract the API endpoint from the path
    const apiPath = url.pathname.replace('/', '');
    
    // Get request body
    let body;
    try {
      body = await request.json();
    } catch (e) {
      return jsonResponse({ error: 'Invalid JSON body' }, 400);
    }
    
    // Handle different types of requests
    if (apiPath === 'vertex_generate') {
      return handleVertexAIGeneration(body);
    } else if (apiPath === 'gemini_generate') {
      return handleGeminiGeneration(body);
    } else if (apiPath === 'token') {
      return handleTokenRequest(body);
    } else {
      return jsonResponse({ error: 'Invalid API endpoint' }, 400);
    }
  } catch (error) {
    console.error(`Error handling request: ${error.message}`);
    return jsonResponse({ error: error.message }, 500);
  }
}

/**
 * Handle Vertex AI content generation requests
 * @param {object} body - Request body
 */
async function handleVertexAIGeneration(body) {
  try {
    const { model_name, contents, config, project_id, credentials } = body;
    
    if (!model_name || !contents || !project_id || !credentials || !credentials.token) {
      console.error('Missing required parameters:', { 
        hasModel: !!model_name, 
        hasContents: !!contents, 
        hasProject: !!project_id, 
        hasCredentials: !!credentials,
        hasToken: credentials && !!credentials.token 
      });
      return jsonResponse({ error: 'Missing required parameters' }, 400);
    }
    
    console.log(`Making Vertex AI request for model: ${model_name}`);
    console.log(`Project ID: ${project_id}`);
    
    // Format request body properly for Vertex API
    // The structure is different from Gemini API
    let requestBody = {};
    
    // Add contents in the expected format
    if (Array.isArray(contents)) {
      requestBody.contents = contents;
    } else {
      requestBody.contents = [contents];
    }
    
    // Add generation config if provided
    if (config) {
      requestBody.generationConfig = config;
    }
    
    console.log(`Request body: ${JSON.stringify(requestBody).substring(0, 200)}...`);
    
    // Construct the Vertex AI request
    const vertexRequest = {
      method: 'POST',
      headers: { 
        'Authorization': `Bearer ${credentials.token}`, 
        'Content-Type': 'application/json' 
      },
      body: JSON.stringify(requestBody)
    };
    
    const apiUrl = `https://us-central1-aiplatform.googleapis.com/v1/projects/${project_id}/locations/us-central1/publishers/google/models/${model_name}:generateContent`;
    console.log(`Calling Vertex API URL: ${apiUrl}`);
    
    // Make request to Vertex AI
    const response = await fetch(apiUrl, vertexRequest);
    
    // Log the response for debugging
    const responseText = await response.clone().text();
    console.log(`Full response: ${responseText.substring(0, 500)}...`);
    
    const responseData = await response.json();
    console.log(`Vertex API response status: ${response.status}`);
    console.log(`Response preview: ${JSON.stringify(responseData).substring(0, 200)}...`);
    
    return jsonResponse(responseData, response.status);
  } catch (error) {
    console.error(`Vertex AI error: ${error.message}`);
    console.error(`Error stack: ${error.stack}`);
    return jsonResponse({ error: error.message }, 500);
  }
}

/**
 * Handle Gemini API content generation requests
 * @param {object} body - Request body
 */
async function handleGeminiGeneration(body) {
  try {
    const { model_name, contents, config, api_key } = body;
    
    if (!contents || !api_key) {
      console.error('Missing required parameters:', { 
        hasContents: !!contents, 
        hasApiKey: !!api_key 
      });
      return jsonResponse({ error: 'Missing required parameters' }, 400);
    }
    
    console.log(`Making Gemini API request for model: ${model_name}`);
    
    // Format request body properly for Gemini API
    const requestBody = {};
    
    // Add contents
    if (Array.isArray(contents)) {
      requestBody.contents = contents;
    } else {
      requestBody.contents = [contents];
    }
    
    // Add generation config if provided, but handle safety settings specially
    if (config) {
      const generationConfig = { ...config };
      
      // Extract safety settings if present
      if (generationConfig.safetySettings) {
        requestBody.safetySettings = generationConfig.safetySettings;
        delete generationConfig.safetySettings;
      }
      
      // Add the remaining config as generationConfig
      requestBody.generationConfig = generationConfig;
    }
    
    console.log(`Request body: ${JSON.stringify(requestBody).substring(0, 200)}...`);
    
    // Construct the Gemini request
    const geminiRequest = {
      method: 'POST',
      headers: { 
        'x-goog-api-key': api_key, 
        'Content-Type': 'application/json' 
      },
      body: JSON.stringify(requestBody)
    };
    
    const apiUrl = `https://generativelanguage.googleapis.com/v1/models/${model_name}:generateContent`;
    console.log(`Calling Gemini API URL: ${apiUrl}`);
    
    // Make request to Gemini API
    const response = await fetch(apiUrl, geminiRequest);
    
    // Log the response for debugging
    const responseText = await response.clone().text();
    console.log(`Full response: ${responseText.substring(0, 500)}...`);
    
    const responseData = await response.json();
    console.log(`Gemini API response status: ${response.status}`);
    console.log(`Response preview: ${JSON.stringify(responseData).substring(0, 200)}...`);
    
    return jsonResponse(responseData, response.status);
  } catch (error) {
    console.error(`Gemini API error: ${error.message}`);
    console.error(`Error stack: ${error.stack}`);
    return jsonResponse({ error: error.message }, 500);
  }
}

/**
 * Handle token requests for service account authentication
 * @param {object} body - Request body
 */
async function handleTokenRequest(body) {
  try {
    const { credentials } = body;
    
    if (!credentials || !credentials.private_key) {
      console.error('Missing required credentials parameters');
      return jsonResponse({ error: 'Missing required credentials parameters' }, 400);
    }
    
    console.log('Processing token request');
    
    // The private_key should be a JWT signed with the service account's private key
    const assertion = credentials.private_key;
    console.log(`JWT token length: ${assertion.length}`);
    
    // Construct the token request with the proper format
    const tokenRequest = {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: new URLSearchParams({ 
        grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer', 
        assertion: assertion
      })
    };
    
    console.log('Token request headers:', tokenRequest.headers);
    console.log('Token request body preview:', tokenRequest.body.toString().substring(0, 50) + '...');
    
    // Make request to Google OAuth
    const response = await fetch('https://oauth2.googleapis.com/token', tokenRequest);
    
    console.log(`Token response status: ${response.status}`);
    
    // Log full response for debugging
    const responseText = await response.clone().text();
    console.log(`Token response body: ${responseText}`);
    
    const responseData = await response.json();
    if (!response.ok) {
      console.error(`Token error response: ${JSON.stringify(responseData)}`);
    } else {
      console.log('Token response succeeded');
    }
    
    return jsonResponse(responseData, response.status);
  } catch (error) {
    console.error(`Token request error: ${error.message}`);
    console.error(`Error stack: ${error.stack}`);
    return jsonResponse({ error: error.message }, 500);
  }
}

/**
 * Create a CORS response for preflight requests
 */
function handleCORS() {
  return new Response(null, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
      'Access-Control-Max-Age': '86400'
    }
  });
}

/**
 * Helper function to create JSON responses with proper headers
 * @param {object} data - Response data
 * @param {number} status - HTTP status code
 */
function jsonResponse(data, status = 200) {
  return new Response(JSON.stringify(data), {
    status: status,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
    }
  });
} 