"""
Cloudflare Worker for Full HTTPS Proxy (Python)
This worker acts as a general proxy for all HTTPS traffic, useful for bypassing China's Great Firewall
"""

import json
import re
from io import BytesIO

async def fetch_with_retry(request, url, retry_count=3, retry_delay=1):
    """Fetch with retry logic for reliability"""
    # Clone headers, removing those that shouldn't be forwarded
    headers = {}
    for key, value in request.headers.items():
        if key.lower() not in ['host', 'cf-connecting-ip', 'cf-ipcountry', 'content-length', 'connection']:
            headers[key] = value
    
    # Set proper host header for the destination
    target_host = new URL(url).hostname
    headers['Host'] = target_host
    
    # Get the request body for non-GET requests
    body = await request.text() if request.method != "GET" else None
    
    # Retry logic with exponential backoff
    for attempt in range(retry_count):
        try:
            response = await fetch(url, {
                "method": request.method,
                "headers": headers,
                "body": body
            })
            return response
        except Exception as e:
            if attempt < retry_count - 1:
                # Wait before retrying with exponential backoff
                await asyncio.sleep(retry_delay * (2 ** attempt))
            else:
                return new Response(
                    json.dumps({"error": f"Failed after {retry_count} attempts: {str(e)}"}),
                    headers={"Content-Type": "application/json"},
                    status=500
                )

async def handle_request(request, env, ctx):
    """Main request handler for proxy functionality"""
    # Parse the request URL and get the proxy target from the path
    url = new URL(request.url)
    
    # The URL format should be: https://your-worker.domain.workers.dev/https://target-site.com/path
    # Extract the target URL from the path
    match = re.match(r'^\/(?:https?:\/\/)?(.*)', url.pathname)
    
    if not match:
        return new Response("Invalid proxy request. Use format: /https://example.com/path", status=400)
    
    target_path = match.group(1)
    
    # Construct the full target URL
    target_url = f"https://{target_path}"
    if url.search:
        target_url += url.search
    
    # Forward the request to the target URL
    response = await fetch_with_retry(request, target_url)
    
    # Forward the response back to the client
    response_headers = {}
    for key, value in response.headers.items():
        # Omit hop-by-hop headers
        if key.lower() not in ['connection', 'keep-alive', 'proxy-authenticate', 
                             'proxy-authorization', 'te', 'trailer', 'transfer-encoding', 'upgrade']:
            response_headers[key] = value
    
    # Create new response with the target site's content
    return new Response(await response.arrayBuffer(), {
        "status": response.status,
        "statusText": response.statusText,
        "headers": response_headers
    })

# Main handler function for Cloudflare Workers
async def on_fetch(request, env, ctx):
    return await handle_request(request, env, ctx) 