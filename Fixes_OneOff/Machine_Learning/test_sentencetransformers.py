# Based on torch!, not tensorflow!
# Need to install timm and einops (pip install timm, pip install einops)
from PIL import Image
from sentence_transformers import SentenceTransformer 
print("SentenceTransformer loaded")
import os
from tqdm import tqdm  # Import tqdm
import time

time_start = time.time()
model_clipv2 = SentenceTransformer('jinaai/jina-clip-v2', trust_remote_code=True)
time_end = time.time()
print(f"Time taken to load model: {time_end - time_start} seconds")


time_start2 = time.time()
images = []
image_dir = "data/IP/Copyrights"
files = [ os.path.join(image_dir, f) for f in os.listdir(image_dir) if os.path.isfile(os.path.join(image_dir, f)) ]
for image_path in files[:31]:
    with Image.open(image_path) as img:
        images.append(img.copy())  # Copy image data before closing file

# Use tqdm to wrap the iterable for a progress bar
embeddings = model_clipv2.encode(images, batch_size=32, show_progress_bar=True) #Added show_progress_bar=True
time_end2 = time.time()
print(f"Time taken to encode images: {time_end2 - time_start2} seconds")

print(f"Total time taken: {time_end2 - time_start} seconds")

# Save embeddings to a file
import pickle
with open('embeddings.pkl', 'wb') as f:
    pickle.dump(embeddings, f)
