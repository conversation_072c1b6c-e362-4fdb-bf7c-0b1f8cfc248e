import asyncio
import contextlib
import io
import json
import os
import re
import sys
import zlib
import base64
import copy
from datetime import datetime
import concurrent.futures

import langfuse
import pandas as pd

# Import the Celery app instance we defined
from .celery_app import celery

# Import the functions that the task will need
from Check.Do_Check import download_and_check
from Check.RAG.RAG_Inference import get_siglip_embeddings
from DatabaseManagement.ImportExport import (get_gz_connection,
                                             insert_and_update_df_to_GZ_batch)
from Check.Create_Report import create_product_url


# Moved from app_apistudio.py
def ansi_to_html(text):
    """Convert ANSI color codes to more readable format with color circle emojis for Langfuse UI"""
    ansi_to_emoji = {
        '\033[0m': '',
        '\033[31m': '🔴 ',  # Red
        '\033[32m': '🟢 ',  # Green
        '\033[33m': '🟠 ',  # Orange
        '\033[91m': '🔴 ',  # Light Red
        '\033[92m': '🟢 ',  # Light Green
        '\033[93m': '🟡 ',  # Light Yellow
    }
    # A simple regex to find all ANSI color codes
    ansi_escape = re.compile(r'(\033\[[0-9;]*m)')
    
    # Replace each code with its emoji equivalent
    for code, emoji in ansi_to_emoji.items():
        text = text.replace(code, emoji)
        
    # Remove any remaining unmapped codes
    text = ansi_escape.sub('', text)
    return text


# Moved from app_apistudio.py
@contextlib.contextmanager
def capture_stdout():
    """Context manager to capture stdout and return it as a string while still printing to console."""
    captured_output = io.StringIO()
    original_stdout = sys.stdout
    
    class TeeOutput:
        def write(self, data):
            captured_output.write(data)
            original_stdout.write(data)
            
        def flush(self):
            captured_output.flush()
            original_stdout.flush()
    
    sys.stdout = TeeOutput()
    try:
        yield captured_output
    finally:
        sys.stdout = original_stdout

# This is the Celery task. The `bind=True` argument makes the task instance (`self`) available.
@celery.task(bind=True)
def process_check_task(self, check_id, client_id, client_name, langfuse_trace_id, product_category, api_key, **kwargs):
    """
    Celery task to perform the check and store results in the database.
    This function is based on the original process_check_with_db.
    """
    captured_output = None
    connection = None
    
    # Wrap the entire task in a Langfuse span to ensure continuity
    with langfuse.get_client().start_as_current_span(
        name="celery-process-check-task",
        trace_context={"trace_id": langfuse_trace_id}
    ) as task_span:
        # Sanitize kwargs for logging to avoid storing large base64 images
        log_kwargs = copy.deepcopy(kwargs)
        image_keys = ['main_product_image', 'other_product_images', 'client_ip_images', 'reference_images']
        for key in image_keys:
            if key in log_kwargs:
                value = log_kwargs[key]
                if isinstance(value, list):
                    for i in range(len(value)):
                        if isinstance(value[i], str) and len(value[i]) > 500:
                            log_kwargs[key][i] = f"[base64 image: {len(value[i])} chars]"
                elif isinstance(value, str) and len(value) > 500:
                    log_kwargs[key] = f"[base64 image: {len(value)} chars]"
        task_span.update(input=log_kwargs)
        
        try:
            # Update the task state to 'PROCESSING'
            self.update_state(state='PROCESSING', meta={'check_id': check_id})
            
            # Capture all stdout during the entire execution
            with capture_stdout() as captured_output:
                # Prepare database insertion task for non-MiniApp/H5 clients
                db_insertion_future = None
                if client_name not in ["MiniApp", "H5", "MiniAppDev", "H5Dev"]:
                    # Store URLs instead of compressed image data - direct creation in dict
                    db_case_check_data = {
                        'id': int(check_id),
                        'user_id': f"{client_name}_{api_key}",
                        'product_category': product_category,
                        'images': json.dumps([create_product_url(check_id, f"client_ip_image_{i}") 
                                            for i, img in enumerate(kwargs.get('client_ip_images', [])) if img]),
                        'keyword': json.dumps(kwargs.get('ip_keywords', [])),
                        'product_images': create_product_url(check_id, "main_product_image") if kwargs.get('main_product_image') else "",
                        'other_product_images': json.dumps([create_product_url(check_id, f"other_product_image_{i}") 
                                                           for i, img in enumerate(kwargs.get('other_product_images', [])) if img]),
                        'product_describe': kwargs.get('description', ''),
                        'reference_source': kwargs.get('reference_text', ''),
                        'reference_images': json.dumps([create_product_url(check_id, f"reference_image_{i}") 
                                                       for i, img in enumerate(kwargs.get('reference_images', [])) if img])
                    }
                    
                    # Start database insertion in background thread - simpler approach
                    db_insertion_future = concurrent.futures.ThreadPoolExecutor().submit(
                        insert_and_update_df_to_GZ_batch, 
                        pd.DataFrame([db_case_check_data]), 'tb_case_check_api', 'id'
                    )
                    print(f"📥 Started database insertion for check_id: {check_id}")

                print(f"📥 Starting download_and_check for check_id: {check_id}")
                
                # The `download_and_check` is an async function, so we run it in an event loop.
                json_results = asyncio.run(download_and_check(check_id=check_id, client_id=client_id, **kwargs))
                
                # Wait for database insertion to complete if it was started
                if db_insertion_future:
                    try:
                        db_insertion_future.result(timeout=60)
                        print(f">> Database insertion completed for check_id: {check_id}")
                    except concurrent.futures.TimeoutError:
                        print(f"!! Database insertion timed out for check_id: {check_id}")
                    except Exception as e:
                        print(f"!! Database insertion failed for check_id: {check_id}, error: {str(e)}")
                
                # Store result in database
                print(f"📤 Storing result in database for check_id: {check_id}")
                
                db_result_data = {
                    'check_id': int(check_id),
                    'check_date': datetime.now().date(),
                    'result': json.dumps(json_results)  # Serialize JSON results to string
                }
                db_result_df = pd.DataFrame([db_result_data])

                # Determine the correct database connection and table
                if client_name in ["MiniApp", "H5"]:
                    insert_and_update_df_to_GZ_batch(db_result_df, 'tb_case_check_result', 'check_id')
                elif client_name in ["MiniAppDev", "H5Dev"]:
                    connection = get_gz_connection(host="maidalv.com", port=3307)
                    insert_and_update_df_to_GZ_batch(db_result_df, 'tb_case_check_result', 'check_id', conn=connection)
                else:
                    insert_and_update_df_to_GZ_batch(db_result_df, 'tb_case_check_result_api', 'check_id')
                
                print(f"✅ Successfully processed and stored result for check_id: {check_id}")

                # Log success inside the try block
                log_output = captured_output.getvalue()
                with langfuse.get_client().start_as_current_span(name="Success Logs") as log_span:
                    log_span.update(input=ansi_to_html(log_output), output="success")

                return json_results

        except Exception as e:
            error_msg = f"Error processing check {check_id}: {str(e)}"
            print(f"🔥 {error_msg}")
            
            # Update the main task span with error details
            task_span.update(output={"error": error_msg}, level='ERROR')

            # Log error details in a child span
            if captured_output:
                log_output = captured_output.getvalue()
                with langfuse.get_client().start_as_current_span(name="Error Logs") as log_span:
                    log_span.update(input=ansi_to_html(log_output), output={"error": f"🔥🔥🔥 {error_msg}"})
            else:
                with langfuse.get_client().start_as_current_span(name="Error Logs") as log_span:
                    log_span.update(output={"error": f"🔥🔥🔥 {error_msg}"})
            
            # This will mark the task as FAILED in Celery and propagate the exception.
            raise
        finally:
            # Ensure the database connection is closed
            if connection and connection.is_connected():
                connection.close()


@celery.task(bind=True)
def get_embeddings_task(self, images_base64):
    """Celery task to generate siglip embeddings."""
    try:
        # Decode base64 images to bytes
        decoded_images = [base64.b64decode(img_b64) for img_b64 in images_base64]

        # Get embeddings using the pre-loaded model in the Celery worker
        embeddings = get_siglip_embeddings(decoded_images, data_type="image")

        # Compress and encode for transport
        compressed_embeddings = zlib.compress(embeddings.tobytes())
        encoded_compressed_embeddings = base64.b64encode(compressed_embeddings).decode('utf-8')
        
        return {
            'status': 'success',
            'embeddings': encoded_compressed_embeddings,
            'compression_ratio': len(embeddings.tobytes()) / len(compressed_embeddings)
        }
    except Exception as e:
        # Proper error handling
        self.update_state(state='FAILURE', meta={'exc_type': type(e).__name__, 'exc_message': str(e)})
        raise