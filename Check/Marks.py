from Check.Data_Cache import get_tm_auto, get_tm_meta, norm

def get_all_matches(description: str):
    """Return a list of (start, end, metadata) for all matches."""
    text = norm(description)
    
    # Use getter functions to ensure data is loaded
    tm_auto = get_tm_auto()
    tm_meta = get_tm_meta()

    matches = []
    for end_idx, tm_id in tm_auto.iter(text):
        matched_word_normalized = tm_meta[tm_id][0] # Get the normalized mark text
        start_idx = end_idx - len(matched_word_normalized) + 1

        # Check for whole word boundaries
        is_start_boundary = (start_idx == 0) or (not text[start_idx - 1].isalnum())
        is_end_boundary = (end_idx == len(text) - 1) or (not text[end_idx + 1].isalnum())

        if is_start_boundary and is_end_boundary:
            matches.append((start_idx, end_idx, tm_meta[tm_id]))
    return matches

def is_perfect_trademark_match(brand_name: str) -> bool:
    """
    Checks if a given brand_name is a perfect, whole-word match within the trademark automaton.
    """
    normalized_brand = norm(brand_name)
    
    # Use getter functions to ensure data is loaded
    tm_auto = get_tm_auto()
    tm_meta = get_tm_meta()
    
    for end_idx, tm_id in tm_auto.iter(normalized_brand):
        matched_word_normalized = tm_meta[tm_id][0]
        start_idx = end_idx - len(matched_word_normalized) + 1
        
        # Check if the match covers the entire input string and is the exact word
        if start_idx == 0 and end_idx == len(normalized_brand) - 1 and matched_word_normalized == normalized_brand:
            return True # Found a perfect match
            
    return False