import json
import pandas as pd
from datetime import datetime
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch, insert_and_update_df_to_GZ_id
from Check.Do_Check import download_and_check
from Check.Data_Cache import get_cached_plaintiff_df, get_cached_cases_df
import time
import asyncio


def reprocess_all_checks():
    """
    Re-Processes all checks from tb_case_check, updates tb_case_check_result
    """

    checks = get_table_from_GZ("tb_case_check")
    results = get_table_from_GZ("tb_case_check_result")

    # checks = checks[checks["id"].isin([1876166875876929538])] # Skelleton in the midle of other items, infringing copyright
    checks = checks[checks["id"].isin([1878829748642054145])] # only skelleton
    # checks = checks[checks["id"].isin([1880274997257388033])] # Infringing Bra Patent
    

    for i, check in list(checks.iterrows())[:]:
        check_id = check["id"]
        print(f"Processing check {i}/{len(checks)}")
        
        # Find the corresponding result
        last_result = None
        for _, result in reversed(list(results.iterrows())):
            if result["check_id"] == check_id:
                last_result = result
                break

        # Prepare inputs for download_and_check
        main_product_image = extract_image_urls(check["product_images"]).split(',')[0]
        other_product_images = extract_image_urls(check["other_product_images"]).split(',') if check["other_product_images"] else []
        ip_images = extract_image_urls(check["images"]).split(',') if check["images"] else []
        ip_keywords = check["keyword"]
        description = check["product_describe"]
        reference_text = check["reference_source"]
        reference_images = extract_image_urls(check["reference_images"]).split(',') if check["reference_images"] else []

        # Run download_and_check
        start_time = time.time()
        check_output = asyncio.run(download_and_check(check_id, main_product_image, other_product_images, ip_images, ip_keywords, description, reference_text, reference_images))
        end_time = time.time()
        print(f"\033[92mTime taken for download_and_check: {end_time - start_time:.1f} seconds\033[0m")

        # Update tb_case_check_result
        if last_result is not None:
            # create the df from the json output and the id of last_result
            data = {
                "id": [last_result["id"]],
                "check_id": [check_id],
                "check_date": [datetime.now().date()],  # Date only
                "result": [json.dumps(check_output)],
                "check_status": 1  # Check status set to 1
            }
            df = pd.DataFrame(data)
            insert_and_update_df_to_GZ_batch(df, "tb_case_check_result", "id")
        else:
            print(f"Warning: No existing result found for check_id {check_id}. Creating a new entry ...")
            data = {
                "check_id": [check_id],
                "check_date": [datetime.now().date()],
                "result": [json.dumps(check_output)],
                "check_status": 1
            }
            df = pd.DataFrame(data)
            insert_and_update_df_to_GZ_id(df, "tb_case_check_result", "check_id", "check_date")


def extract_image_urls(json_str):
    """
    Extracts image urls from a JSON string.
    Returns a comma-separated string of urls, or None if the input is invalid.
    """
    if pd.isna(json_str):
        return None
    try:    
        data = json.loads(json_str)
        if isinstance(data, list):
            return ','.join([item['url'] for item in data if 'url' in item])
        elif isinstance(data, dict) and 'url' in data:
            return data['url']
        else:
            return None
    except (json.JSONDecodeError, TypeError):
        return None
    
if __name__ == "__main__":
    from Check.RAG.RAG_Inference import load_all_models
    load_all_models()
    cases_df = get_cached_cases_df()
    plaintiff_df = get_cached_plaintiff_df()
    
    # No longer needed because we are loading the tables from feather files
    # from concurrent.futures import ThreadPoolExecutor
    # with ThreadPoolExecutor() as executor:
    #     # Submit tasks to the thread pool
    #     future_cases = executor.submit(get_cached_cases_df)
    #     load_all_models()
    #     cases_df = future_cases.result()
    #     plaintiff_df = get_cached_plaintiff_df()
    reprocess_all_checks()
