from langfuse import observe
import langfuse
import ast

from AI.GCV_GetImageParts import get_image_parts_async
from AI.GC_VertexAI import vertex_genai_multi_async
from AI.LLM_shared import get_json
import shutil, time, os, asyncio
import Common.Constants as Constants
from Check.RAG.qdrant_search import find_similar_assets_qdrant
from Check.Create_Report import create_check_report, create_product_url
from Check.Marks import get_all_matches, is_perfect_trademark_match
from Check.Data_Cache import get_cached_int_cls_definitions_df
from FileManagement.Tencent_COS import async_upload_file_with_retry
from Check.Utils import create_ip_url

def search_tm_auto_for_brand(brand_name):
    """Search TM_AUTO for exact match of brand name."""
    if not brand_name or not brand_name.strip():
        return []

    if is_perfect_trademark_match(brand_name.strip()):
        # Get all matches for this brand to return metadata
        matches = get_all_matches(brand_name.strip())
        return matches
    return []


def search_tm_auto_for_text(text):
    """Search TM_AUTO using get_all_matches for description or reference text."""
    if not text or not text.strip():
        return []

    return get_all_matches(text.strip())

def format_trademark_matches_for_ai(matches):
    """Format trademark matches with metadata for AI analysis."""
    if not matches:
        return []

    int_cls_df = get_cached_int_cls_definitions_df()
    formatted_matches = []

    for match in matches:
        _, _, metadata = match  # We don't need start_idx and end_idx for this purpose
        # metadata format: (normalized_mark_text, plaintiff_id, reg_no, ser_no, applicant_name, int_cls)
        text, plaintiff_id, reg_no, ser_no, applicant_name, int_cls = metadata

        # Get class descriptions
        class_descriptions = []
        if int_cls:
            # The int_cls field from the database is in the format "{15,24,37}".
            # We need to convert this string representation of a set into a Python list of integers.
            try:
                # Safely evaluate the string as a Python literal (set), then convert to a list
                int_cls_set = ast.literal_eval(int_cls)
                int_cls_list = list(int_cls_set)
            except (ValueError, SyntaxError):
                # Fallback if parsing fails (e.g., if int_cls is not in the expected format)
                int_cls_list = [int_cls] if not isinstance(int_cls, list) else int_cls

            for cls in int_cls_list:
                desc_row = int_cls_df[int_cls_df['class'] == cls]
                if not desc_row.empty:
                    class_descriptions.append(f"Class {cls}: {desc_row.iloc[0]['description']}")
                else:
                    class_descriptions.append(f"Class {cls}: Description not available")

        formatted_match = {
            "text": text,
            "applicant_name": applicant_name or "Not available",
            "int_cls_list": int_cls,
            "int_cls_descriptions": class_descriptions,
            "plaintiff_id": plaintiff_id,
            "reg_no": reg_no,
            "ser_no": ser_no
        }
        formatted_matches.append(formatted_match)

    return formatted_matches

@observe()
async def analyze_trademark_text_relevance(check_id, product_image_path, description, keywords, reference_text, all_matches):
    """
    Analyze trademark matches for infringement relevance using AI.
    Returns list of relevant trademark matches with 'report' field added.
    """
    if not all_matches:
        return []

    # Format matches for AI analysis
    formatted_matches = format_trademark_matches_for_ai(all_matches)

    if not formatted_matches:
        return []

    # Create the AI prompt for trademark infringement analysis
    prompt = f"""You are a Trademark attorney specialized in analyzing potential trademark infringement cases.

**Task:** Analyze the following registered trademarks against a product being sold online to identify which trademarks might be relevant from an infringement perspective.

**Product Information:**
- Product Description: {description or "Not provided"}
- Keywords: {keywords or "Not provided"}
- Reference Text: {reference_text or "Not provided"}

**Registered Trademarks to Analyze:**
"""

    for i, match in enumerate(formatted_matches, 1):
        prompt += f"""
{i}. **Trademark Text:** {match['text']}
   **Owner:** {match['applicant_name']}
   **Registration Number:** {match.get('reg_no', 'Not available')}
   **International Classes:** {', '.join(match['int_cls_descriptions']) if match['int_cls_descriptions'] else 'Not available'}
"""

    prompt += """
**Analysis Criteria:**
For each trademark, consider the following factors to determine infringement relevance:

1. **Similarity of the Marks:** Compare the trademark text with the product description, keywords, and reference text in terms of:
   - Spelling similarity
   - Sound (phonetic similarity)
   - Meaning (connotation, overall commercial impression)

2. **Proximity/Relatedness of Goods:** Analyze the relationship between the goods offered under the registered trademark (based on international classes) and the product being sold:
   - Are they directly competitive, related, complementary, or unrelated?

3. **Strength of the Trademark:** Consider the distinctiveness of the trademark:
   - Is it fanciful, arbitrary, suggestive, descriptive, or generic?

4. **Likelihood of Consumer Confusion:** Would consumers be likely to confuse the product with goods/services offered under the registered trademark?

**Instructions:**
- Only select trademarks that have a reasonable likelihood of infringement based on the above criteria
- Focus on trademarks that are actually present or similar to text in the product description, keywords, or reference text
- Consider the relatedness of goods/services based on international class descriptions

**Response Format:**
Return a JSON array with the trademark numbers (1, 2, 3, etc.) that you consider relevant for potential infringement analysis:

{{"relevant_trademarks": [1, 3, 5]}}

If no trademarks are relevant, return: {{"relevant_trademarks": []}}
"""

    # Call AI for analysis
    prompt_list = [("text", prompt)]
    if product_image_path and os.path.exists(product_image_path):
        prompt_list.append(("image_path", product_image_path))

    ai_answer = await vertex_genai_multi_async(prompt_list, model_name=Constants.SMART_MODEL_FREE)

    try:
        analysis_result = get_json(ai_answer)
        relevant_indices = analysis_result.get("relevant_trademarks", [])
        # product_url = create_product_url(check_id, product_image_path)

        # Mark relevant matches for report creation
        results = []
        for idx in relevant_indices:
            if 1 <= idx <= len(all_matches):
                match = formatted_matches[idx - 1]
                results.append({        
                    "ip_type": "Trademark",
                    "internal_type": "text",
                    "reg_no": match.get("reg_no", []),
                    "int_cls_list": match.get("int_cls_list", []),
                    "int_cls_descriptions": match.get("int_cls_descriptions", []),
                    "text": str(match.get("text", "")),
                    "ip_owner": str(match.get("applicant_name", "")),
                    "plaintiff_id": match.get("plaintiff_id", ""),
                    "ser_no": match.get("ser_no", ""),
                    "product_local_path": product_image_path,
                    # "product_url": [product_url],  # Product url gets added in Create_Report
                    "ip_asset_urls": [create_ip_url("Trademark", match.get("ser_no", ""))],
                    "ip_local_paths": []
                })

        return results

    except Exception as e:
        print(f"Error parsing AI analysis result: {e}")
        return []


@observe()
async def process_single_image(client_id, query_image_path, check_id, temp_dir, cases_df, plaintiff_df, split_image=True):
    """
    New approach: Extract only brand names from images, then search TM_AUTO and analyze with AI.
    If split_image is False, the whole image is considered a logo.
    """
    # prompt_brand = 'Detect all of the Trademarks of generally known brands. For each trademark, assign a label that includes only the brand name/text visible in the trademark. Format: "Brand Name" (one brand name per detection).'
    prompt_brand = 'Detect all of the Trademarks of generally known brands. For each, assign a label that includes the type of trademark (text or logo), and the brand name  in this format: "Type, Brand Name", where Type can only be text or logo. Do not use "trademark" as the label, I need the Type and Brand Name.'

    # For Langfuse
    product_url = create_product_url(check_id, query_image_path)
    image_url = ["", product_url.replace(" ", "%20").replace("http:", "https:")]

    # We make a copy just for trademark because copyright will also create parts and it should not overwrite the copyright parts
    query_image_path_old = query_image_path
    query_image_path = os.path.join(os.path.dirname(query_image_path), "trademark", os.path.basename(query_image_path))
    os.makedirs(os.path.dirname(query_image_path), exist_ok=True)
    shutil.copy(query_image_path_old, query_image_path)
    
    if split_image:
        images_parts = await get_image_parts_async(prompt_brand, query_image_path, image_url, model_name=Constants.IMAGE_MODEL_FREE_LIMITED, useVertexAI=Constants.IMAGE_MODEL_FREE_LIMITED_VERTEX) #, model_name=Constants.IMAGE_MODEL_FREE_LIMITED, useVertexAI=Constants.IMAGE_MODEL_FREE_LIMITED_VERTEX)
    else:
        images_parts = [{"path": query_image_path, "label": "logo,unknown"}] # No brand name, it will be searched as a logo

    results = []
    brands = []
    for image_part in images_parts:
        label_parts = image_part["label"].split(",")
        label_type = label_parts[0].lower().strip() if len(label_parts) >= 2 else "unknown"
        brand_name = label_parts[1].strip() if len(label_parts) >= 2 else ""
        if label_type == "logo":
            results.extend(await process_brand_logo(client_id, image_part["path"], product_url, brand_name, check_id, temp_dir, cases_df, plaintiff_df))
        # elif label_type == "text":
        #     brands.extend(brand_name)
        if brand_name and brand_name not in brands and brand_name.lower() != "unknown" and brand_name.lower() != "trademark":
            brands.append(brand_name)

    langfuse.get_client().update_current_span(input=f'\n![Alt text]({product_url.replace(" ", "%20").replace("http:", "https:")})')

    return results, brands


@observe()
async def process_brand_logo(client_id, query_image_part_path, product_url, brand_name, check_id, temp_dir, cases_df, plaintiff_df):
    # Logic: if the brand name is one of the TRO plaintiff, we search Qdrant only for that plaintiff. Else we search everything (with higher treshold)
    
    match_items = search_tm_auto_for_brand(brand_name)
    applicant_names = set()
    plaintiff_ids = set()
    for match_item in match_items:
        if match_item[2][4]:
            applicant_names.add(match_item[2][4]) # 2 is metadata, 4 is applicant_name
        if match_item[2][1]:
            plaintiff_ids.add(match_item[2][1]) # 2 is metadata, 1 is plaintiff_id

    sim_results = []
    for plaintiff_id in plaintiff_ids:
        sim_results.extend(await find_similar_assets_qdrant(
            query_image_paths=[query_image_part_path],
            check_id=check_id,
            client_id=client_id,
            ip_type="Trademark",
            temp_dir=temp_dir,
            cases_df=cases_df,
            plaintiff_df=plaintiff_df,
            plaintiff_id=plaintiff_id,
            top_n=1,
            similarity_threshold=0.65
        ))   
        
    if not match_items or not sim_results:  # If we did not find using the plaintiff id, or we dont have a plaintiff id, we search amongst the logo with a higher threshold.
        # No match in plaintiff_list, so we search for the brand in trademark_texts.
        sim_results = await find_similar_assets_qdrant( 
            query_image_paths=[query_image_part_path],
            check_id=check_id,
            client_id=client_id,
            ip_type="Trademark",
            temp_dir=temp_dir,
            cases_df=cases_df,
            plaintiff_df=plaintiff_df,
            plaintiff_id=None,
            top_n=1,
            similarity_threshold=0.75
        )
    
    for sim_result in sim_results:
        sim_result["internal_type"] = "logo"

    langfuse.get_client().update_current_span(input=f'query_image_path: \n![Alt text]({product_url.replace(" ", "%20").replace("http:", "https:")}) \n\nquery_image_part_path: {query_image_part_path} \n\nbrand_name: {brand_name}')

    return sim_results



@observe()
# @profile
async def check_trademarks(client, bucket, temp_dir, client_id, check_id, local_product_images, local_client_ip_images, local_reference_images, description, ip_keywords, reference_text, cases_df, plaintiff_df):

    start_time = time.time()

    # Use new approach: process images with TM_AUTO search and AI analysis
    trademark_check_tasks = [
        process_single_image(
            client_id, query_image_path, check_id, temp_dir, cases_df, plaintiff_df,
            split_image=(query_image_path not in local_client_ip_images)
        )
        for query_image_path in local_product_images + local_client_ip_images + local_reference_images
    ]
    task_returns = await asyncio.gather(*trademark_check_tasks)
    results_logo = [item for sublist in task_returns for item in sublist[0] if item is not None]
    brands = [item for sublist in task_returns for item in sublist[1] if item is not None]
    
    
    ### Collect all trademark matches from different source
    # 1. Search for matches of brand names from AI
    text_matches = []
    for brand_name in brands:
        text_matches.extend(search_tm_auto_for_brand(brand_name))

    # 2. Search for exact matches of keywords
    for keyword in ip_keywords:
        text_matches.extend(search_tm_auto_for_brand(keyword))

    # 3. Search for matches in description text
    text_matches.extend(search_tm_auto_for_text(description))

    # 4. Search for matches in reference text
    text_matches.extend(search_tm_auto_for_text(reference_text))

    # Remove duplicates text_matches based on serial number
    unique_matches = []
    seen_ser_nos = set()
    for match in text_matches:
        _, _, metadata = match
        ser_no = metadata[3]  # reg_no is at index 2 in metadata tuple
        if ser_no not in seen_ser_nos:
            seen_ser_nos.add(ser_no)
            unique_matches.append(match)
            
    # unique_matches sort by len("text")
    unique_matches.sort(key=lambda x: len(x[2][0]), reverse=True) # 2 is metadata, 0 is text
    filtered_unique_matches = unique_matches[:15]  # Only keep the 15 longest text (we are not going to give 100 to the AI!)

    # Use AI to analyze which matches are relevant for infringement
    results_text = await analyze_trademark_text_relevance(check_id, local_product_images[0], description, ip_keywords, reference_text, filtered_unique_matches)
    
    results = results_logo + results_text
    
    # Merge results that have same ip_owner and same text:
    merged_results = []
    seen_keys = set()
    for result in results:
        key = (result["ip_owner"], result["text"])
        if key not in seen_keys:
            merged_results.append(result)
            seen_keys.add(key)
        else:
            merged_result = [merged_result for merged_result in merged_results if merged_result["ip_owner"] == result["ip_owner"] and merged_result["text"] == result["text"]][0]
            if result["product_local_path"] == local_product_images[0]:  # Favor the main picture
                merged_result["product_local_path"] = result["product_local_path"]  # Create report adds the product url
                
            merged_result["ip_asset_urls"].extend(result["ip_asset_urls"])
            merged_result["ip_local_paths"].extend(result["ip_local_paths"])
            
    print(f"📋 [CHECK:{check_id}] Logo results: {len(results_logo)}, Text results: {len(results_text)}, Merged results: {len(merged_results)}")
    
    report_tasks = []
    for result in merged_results:
        report_tasks.append(create_check_report("Trademark", check_id, result, client, bucket, description=description, keywords=ip_keywords, reference_text=reference_text))

    # Upload to COS the images parts (the full image is already uploaded in "check" function) used for these results. We start the upload now, we wait for the result after the "report_tasks" are done
    product_part_paths = set([result["product_local_path"] for result in results if "part" in result["product_local_path"]])
    trademark_product_image_upload_task = [asyncio.create_task(
        async_upload_file_with_retry(client=client, bucket=bucket, key=f"checks/{check_id}/query/{os.path.basename(product_part_path)}", file_path=product_part_path)
    ) for product_part_path in product_part_paths]
    
    report_results = await asyncio.gather(*report_tasks)
    await asyncio.gather(*trademark_product_image_upload_task)
    
    report_results = [res for res in report_results if res is not None]  # Filter out None values (i.e. where the report was not required because not similar at all)

    print(f"\033[32m ✅ Trademark: Trademark Analysis DONE, for {len(local_product_images+local_client_ip_images+local_reference_images)} pictures in {time.time() - start_time:.1f} seconds\033[0m")
    return report_results