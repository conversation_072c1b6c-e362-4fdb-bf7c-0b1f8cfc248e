print("Loading RAG.py")
import os
import sys
current_directory = os.getcwd()
sys.path.insert(0, current_directory)
import numpy as np
import time
import torch # Added for Siglip
import gc
from Check.RAG.siglip_model import SiglipModel # Import the new SiglipModel
import requests # Added for API calls
import base64 # Added for base64 encoding
import json # Added for JSON handling
import asyncio # Added for async operations
import aiohttp # Added for async HTTP requests
from Common.Constants import local_ip_tro_folder
from langfuse import observe
import langfuse

# Global model instance - using single SigLIP model for all embeddings
model_siglip = None
structured_patent_image_array = None 
structured_patent_text_array = None 
structured_copyright_array = None
structured_trademark_array = None


def load_all_models():
    load_siglip_model()
    
    # global structured_patent_image_array, structured_patent_text_array, structured_copyright_array, structured_trademark_array
    # if structured_patent_image_array is None:
    #     print("Loading patent image embeddings...")
    #     structured_patent_image_array = np.load(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', 'EmbeddingsPatentImages.npy'), allow_pickle=True)
    #     print("Patent image embeddings loaded.")

    # if structured_patent_text_array is None:
    #     print("Loading patent text embeddings...")
    #     structured_patent_text_array = np.load(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', 'EmbeddingsPatentTexts.npy'), allow_pickle=True)
    #     print("Patent text embeddings loaded.")

    # if structured_copyright_array is None:
    #     print("Loading copyright embeddings...")
    #     structured_copyright_array = np.load(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', 'EmbeddingsCopyright.npy'), allow_pickle=True)
    #     print("Copyright embeddings loaded.")

    # if structured_trademark_array is None:
    #     print("Loading trademark embeddings...")
    #     structured_trademark_array = np.load(os.path.join(os.getcwd(), 'data', 'EmbeddingsDescriptors', 'EmbeddingsTrademarkLogo.npy'), allow_pickle=True)
    #     print("Trademark embeddings loaded.")

def load_siglip_model():
    """Load the single SigLIP model used for all IP types."""
    global model_siglip

    if model_siglip is None:
        print(f"\nLoading SigLIP model google/siglip2-large-patch16-512")
        siglip_config = {
            "vector_size": 1024,
            "model_name_or_path": "google/siglip2-large-patch16-512"
        }
        model_siglip = SiglipModel(model_id="siglip2_large_patch16_512", config=siglip_config)
        model_siglip.load()
        print("SigLIP model loaded successfully.")

    return model_siglip


@observe(capture_input=False, capture_output=False)
def get_siglip_embeddings(data_list, data_type="image", batch_size=1):
    """
    Get SigLIP embeddings for images or text.

    Args:
        data_list: List of image paths or text strings
        data_type: "image" or "text"
        batch_size: Batch size for processing

    Returns:
        numpy array of embeddings
    """
    langfuse.get_client().update_current_span(input=f"Getting SigLIP embeddings for {len(data_list)} {data_type}(s)")
    print(f"🔥 Getting SigLIP embeddings for {len(data_list)} {data_type}(s)")
    if not model_siglip:
        load_siglip_model()

    all_embeddings_list = []
    
    # Batch processing
    current_batch_size = batch_size
    while current_batch_size >= 1:
        all_embeddings_list = [] # Reset for each batch size attempt
        try:
            for i in range(0, len(data_list), current_batch_size):
                batch = data_list[i:i+current_batch_size]
                # The SiglipModel class handles image loading internally if paths are passed for images
                # For text, it expects a list of strings.
                batch_embeddings = model_siglip.compute_features(batch, data_type=data_type)
                all_embeddings_list.append(batch_embeddings)
            break # Success
        except RuntimeError as e:
            if 'CUDA out of memory' in str(e) or 'out of memory' in str(e).lower():
                print(f"OOM error with Siglip batch size {current_batch_size} for {data_type}, reducing...")
                current_batch_size = current_batch_size // 2
                if model_siglip.device.type == 'cuda':
                    torch.cuda.empty_cache()
                gc.collect()
            else:
                raise e # Re-raise other runtime errors
    if not all_embeddings_list: # Failed even with batch size 1
         raise RuntimeError(f"Failed to compute Siglip embeddings for {data_type} even with batch size 1.")
    embeddings = np.concatenate(all_embeddings_list, axis=0)
    return embeddings



def test_speed_siglip():
    # All 50 pics
    # CPU laptop: 1=144 sec, 2=130sec
    # GPU laptop: 1=8sec, 2=7.4-9.9sec, 4=6.7-9.4sec, 8=50sec
    # GPU 3070:  1 =5.4sec, 2=5.4, 4 = 5.4sec
    # CPU Server: 1=104sec, 2=112sec

    # Conclusion: GPU is way way fast. This model is same speed as Jina clipv2


    folder_path = os.path.join(local_ip_tro_folder, "copyrights", "Production")
    image_paths = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'))]
    image_paths = image_paths[:50]

    embeddings = get_siglip_embeddings(image_paths[:1]) # to get cuDNN loaded

    start_time = time.time()
    embeddings = get_siglip_embeddings(image_paths, batch_size=2)
    end_time = time.time()
    print(f"Total time taken batch size 4: {end_time - start_time:.1f} seconds")

    print(f"Embeddings shape: {embeddings.shape}")


def test_speed_api_siglip():
    # 1 pic: 2 sec
    # 50 pics: 18sec instead of 5.4 (GPU) and 104sec (CPU)
    # 200pics: 84sec, with batch of 25: 47sec instead of 22 (GPU)
    # 400pics: batch of 25: 122sec
    
    API_URL = "https://api.maidalv.com/get_image_embeddings"
    # Replace with your actual API key or retrieve from environment variables
    API_KEY = os.environ.get("API_BEARER_TOKEN")

    folder_path = os.path.join(local_ip_tro_folder, "copyrights", "Production")
    image_paths = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'))]
    image_paths = image_paths[:400]

    def encode_image_to_base64(image_path):
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    print("\n--- Testing API with 1 picture ---")
    single_image_base64 = [encode_image_to_base64(image_paths[0])]
    payload_single = {
        "api_key": API_KEY,
        "images": single_image_base64
    }

    start_time_single = time.time()
    try:
        response_single = requests.post(API_URL, json=payload_single)
        response_single.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)
        result_single = response_single.json()
        end_time_single = time.time()
        print(f"Time taken for 1 picture: {end_time_single - start_time_single:.2f} seconds")
        print(f"Result for 1 picture: {result_single.get('status')}, Shape: {result_single.get('embedding_shape')}")
    except requests.exceptions.RequestException as e:
        print(f"Error calling API for 1 picture: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"Response content: {e.response.text}")

    print("\n--- Testing API with batched requests (batch_size=25) ---")
    BATCH_SIZE = 25
    all_images_base64 = [encode_image_to_base64(path) for path in image_paths]

    async def send_batch_request(session, images_batch):
        payload = {
            "api_key": API_KEY,
            "images": images_batch
        }
        async with session.post(API_URL, json=payload) as response:
            response.raise_for_status()
            return await response.json()

    async def main_async_test():
        all_results = []
        start_time_batch = time.time()
        async with aiohttp.ClientSession() as session:
            tasks = []
            for i in range(0, len(all_images_base64), BATCH_SIZE):
                batch = all_images_base64[i:i+BATCH_SIZE]
                tasks.append(send_batch_request(session, batch))
            
            try:
                responses = await asyncio.gather(*tasks, return_exceptions=True)
                for res in responses:
                    if isinstance(res, Exception):
                        print(f"Error in batch request: {res}")
                        if hasattr(res, 'response') and res.response is not None:
                            print(f"Response content: {res.response.text}")
                    else:
                        all_results.append(res)
            except aiohttp.ClientError as e:
                print(f"Error during async requests: {e}")

        end_time_batch = time.time()
        print(f"Total time taken for {len(image_paths)} pictures (batched): {end_time_batch - start_time_batch:.2f} seconds")
        
        # Aggregate results
        total_embeddings_count = 0
        first_embedding_shape = None
        for res in all_results:
            if res.get('status') == 'success' and 'embeddings' in res:
                total_embeddings_count += len(res['embeddings'])
                if first_embedding_shape is None:
                    first_embedding_shape = res.get('embedding_shape')
        end_time = time.time()
        
        print(f"Aggregated Result: Processed {total_embeddings_count} embeddings.Total time taken: {end_time - start_time_batch:.2f} seconds")

    asyncio.run(main_async_test())





if __name__ == "__main__":
    time_start = time.time()
    # test_speed_siglip() # Commented out to run the API speed test
    test_speed_api_siglip()
    time_end = time.time()
    print(f"Total script execution time: {time_end - time_start:.1f} seconds")