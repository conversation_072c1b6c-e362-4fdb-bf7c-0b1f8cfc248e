if ! git rev-parse --is-inside-work-tree > /dev/null 2>&1; then
    git init
    git remote add origin https://forgejo.sergedc.com/sergedc/TRO-USside.git
fi

git fetch origin
git reset --hard origin/main
chmod 600 /workspace/.ssh/id_rsa
source ./set_env.sh

# ask the user if to rename .env to .env.test and .env.prod to .env
echo "!!! Do not forget to update the .env file and rerun source ./set_env.sh if required !!!"