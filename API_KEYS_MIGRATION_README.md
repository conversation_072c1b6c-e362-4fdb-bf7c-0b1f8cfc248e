# API Keys Migration: JSON to Database

This document describes the migration of API key management from JSON file storage to database storage with caching.

## Overview

The migration moves API key storage from `api_keys.json` to a PostgreSQL database table `check_client_api_keys` with a 24-hour caching mechanism for improved performance and reliability.

## Files Modified

### Database Layer (`Qdrant/api/utils/db.py`)
- ✅ Added `check_client_api_keys` table schema
- ✅ Added migration functions
- ✅ Added caching mechanism with 24-hour expiry
- ✅ Added fallback error handling

### Application Layer (`app_apistudio.py`)
- ✅ Replaced JSON file loading with database-backed loading
- ✅ Updated `refresh_api_keys()` function to use database
- ✅ Added fallback to JSON file if database fails

### API Layer (`Qdrant/api/routes/reverse_check_status.py`)
- ✅ Replaced JSON file loading with database-backed loading
- ✅ Added same caching pattern as app_apistudio.py
- ✅ Added fallback to JSON file if database fails

## Migration Steps

### 1. Run the Migration Script
```bash
python migrate_api_keys.py
```

This script will:
- Create the `check_client_api_keys` table
- Migrate all data from `api_keys.json` to the database
- Verify the migration was successful

### 2. Test the Migration
```bash
python test_api_keys_migration.py
```

This script will:
- Compare JSON data with database data
- Test the caching mechanism
- Validate sample API keys
- Ensure data consistency

### 3. Deploy and Monitor
- Deploy the updated code
- Monitor application logs for any issues
- Verify API key authentication works correctly

### 4. Remove JSON File (After Confirmation)
Once you've confirmed everything works correctly:
```bash
# Backup the file first
cp api_keys.json api_keys.json.backup

# Remove the original file
rm api_keys.json
```

## Database Schema

```sql
CREATE TABLE check_client_api_keys (
    api_key VARCHAR(255) PRIMARY KEY,
    client_id INTEGER NOT NULL,
    client_name VARCHAR(255) NOT NULL,
    rate_limit INTEGER NOT NULL DEFAULT 1,
    daily_limit INTEGER NOT NULL DEFAULT 100,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_client_api_keys_client_id ON check_client_api_keys(client_id);
```

## Caching Mechanism

- **Cache Duration**: 24 hours
- **Cache Location**: Module-level variables in `db.py`
- **Cache Validation**: Automatic expiry check on each access
- **Fallback Strategy**: Uses stale cache if database fails
- **Refresh Function**: `refresh_api_keys_cache()` for manual refresh

## API Functions

### Database Functions
- `create_api_keys_table()` - Creates the database table
- `migrate_api_keys_from_json(json_file_path)` - Migrates data from JSON
- `get_api_keys_as_dict()` - Gets API keys in original JSON format
- `get_all_api_keys()` - Gets API keys as pandas DataFrame

### Caching Functions
- `get_cached_api_keys()` - Main function with caching logic
- `refresh_api_keys_cache()` - Forces cache refresh
- `_is_cache_valid()` - Checks cache validity (internal)
- `_update_cache(api_keys_dict)` - Updates cache (internal)

## Error Handling

The migration includes comprehensive error handling:

1. **Database Connection Failures**: Falls back to JSON file
2. **JSON File Missing**: Continues with empty API keys dict
3. **Cache Failures**: Uses stale cache if available
4. **Migration Errors**: Detailed error messages and rollback

## Performance Benefits

- **Reduced I/O**: Caching eliminates repeated file reads
- **Better Concurrency**: Database handles concurrent access better than file locks
- **Scalability**: Database can handle larger datasets efficiently
- **Reliability**: Database transactions ensure data consistency

## Monitoring

Monitor these aspects after deployment:

1. **Application Startup**: Check for successful API key loading messages
2. **Authentication**: Verify API key validation still works
3. **Cache Performance**: Monitor cache hit/miss patterns
4. **Database Performance**: Monitor query performance on the new table

## Rollback Plan

If issues occur, you can quickly rollback:

1. Restore the original code versions
2. Ensure `api_keys.json` is still available
3. Restart the applications

The database table can remain for future migration attempts.

## Support

For issues or questions about this migration:
1. Check application logs for error messages
2. Run the test script to validate data consistency
3. Verify database connectivity and table structure
4. Check that all required environment variables are set
