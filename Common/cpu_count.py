import os
from logdata import log_message
import platform

def get_allocated_cpus():
    try:
        # Platform-specific configuration
        if platform.system() == "Windows":
            return max(1, os.cpu_count() - 4)
        else:
            # macOS/Linux configuration
            if platform.system() == "Darwin":
                # macOS doesn't use cgroups, use physical cores
                return os.cpu_count()
            else:
                print("Platform is linux...")
                
                # First try cgroup v2
                if os.path.exists('/sys/fs/cgroup/cgroup.controllers'):
                    log_message("📦 Detected cgroup v2")
                    try:
                        cgroup_path = '/sys/fs/cgroup/cpu.max'
                        log_message(f"Checking if {cgroup_path} exists...")
                        if not os.path.exists(cgroup_path):
                            log_message(f"⚠️ {cgroup_path} does not exist")
                            # ARM containers might use a different path
                            alt_paths = ['/sys/fs/cgroup/cpu/cpu.max', '/sys/fs/cgroup/cpuset.cpus.effective']
                            for alt_path in alt_paths:
                                if os.path.exists(alt_path):
                                    log_message(f"Found alternative path: {alt_path}")
                                    cgroup_path = alt_path
                                    break
                            else:
                                log_message(f"⚠️ Cgroup v2 read failed: {str(e)}")
                        
                        log_message(f"Reading from {cgroup_path}...")
                        with open(cgroup_path, 'r') as f:
                            content = f.read().strip()
                            log_message(f"File content: '{content}'")
                            
                            if cgroup_path.endswith('cpuset.cpus.effective'):
                                # This file contains a list like "0-3" or "0,1,2,3"
                                if '-' in content:
                                    start, end = map(int, content.split('-'))
                                    allocated = end - start + 1
                                else:
                                    allocated = len(content.split(','))
                                log_message(f"📦 Container CPU allocation: {allocated} cores")
                                return allocated
                            
                            # Normal cpu.max processing
                            quota_str, period_str = content.split()
                            if quota_str == "max":
                                return os.cpu_count()
                            quota = int(quota_str)
                            period = int(period_str)
                            
                        allocated = max(1, int(quota / period))
                        log_message(f"📦 Container CPU allocation: {allocated} cores")
                        return allocated
                    except Exception as e:
                        log_message(f"⚠️ Cgroup v2 read failed: {str(e)}")
                
                # Try cgroup v1
                log_message("📦 Detected cgroup v1")
                try:
                    with open('/sys/fs/cgroup/cpu,cpuacct/cpu.cfs_quota_us', 'r') as f:
                        quota = int(f.read())
                    with open('/sys/fs/cgroup/cpu,cpuacct/cpu.cfs_period_us', 'r') as f:
                        period = int(f.read())
                    
                    if quota == -1:
                        return os.cpu_count()
                    allocated = max(1, int(quota / period))
                    log_message(f"📦 Container CPU allocation: {allocated} cores")
                    return allocated
                except Exception as e:
                    log_message(f"⚠️ Cgroup v1 read failed: {e}")
                
                # If both cgroup v1 and v2 fail, try to read from cpu.stat
                if os.path.exists('/sys/fs/cgroup/cpu.stat'):
                    try:
                        with open('/sys/fs/cgroup/cpu.stat', 'r') as f:
                            stats = dict(line.split() for line in f)
                            if 'usage_usec' in stats:
                                # If we can read usage, assume we have access to CPU
                                return os.cpu_count()
                    except Exception as e:
                        log_message(f"⚠️ Cgroup cpu.stat read failed: {e}")
                
                # Final fallback
                log_message("⚠️ Using fallback CPU count")
                return os.cpu_count()
            
    except Exception as e:
        log_message(f"⚠️ All CPU detection methods failed, using fallback: {e}")
        return os.cpu_count()