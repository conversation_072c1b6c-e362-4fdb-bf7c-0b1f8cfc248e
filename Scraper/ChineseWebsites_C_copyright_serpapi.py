import os
import sys
sys.path.append(os.getcwd())
import time
import requests
import argparse
import uuid
from PIL import Image, ImageChops
from io import BytesIO
import random
import torch
from torchvision import models, transforms
from torchvision.models import MobileNet_V2_Weights
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import shutil
import glob
import json
import re
from logdata import log_message
from langfuse import observe
from PIL.ExifTags import TAGS

# SerpApi configuration
SERPAPI_KEY = "784cdabdad64dfa445bc6d7d3fa4ccad83ae291e89a30e70b65105fd3ce93502"

# Constants for image similarity
SIMILARITY_THRESHOLD = 0.30

# List of websites to avoid
BLACKLISTED_DOMAINS = [
    "qqdip.com",
    "sellerguard.com.cn",
    "saibeiip.com",
    "maijiazhichi.com",
    "mooting.cn",
    "fangtion.com",
    "daxinfawu.com",
    "10100.com",
    "sellerdefense.cn"
]

def is_blacklisted_url(url):
    """Check if URL is from a blacklisted domain"""
    if not url:
        return False
        
    # Extract domain from URL
    try:
        # Remove protocol and get domain
        domain = re.sub(r'https?://', '', url.lower())
        domain = domain.split('/')[0]
        
        # Check against blacklisted domains
        for blacklisted_domain in BLACKLISTED_DOMAINS:
            if blacklisted_domain in domain:
                log_message(f"  ⚠️ Blacklisted domain detected: {domain} matches {blacklisted_domain}")
                return True
                
        return False
    except Exception as e:
        log_message(f"  ❌ Error checking domain: {e}")
        return False

def extract_image_url_from_metadata(image_path):
    """Extract original image URL from image metadata/EXIF data"""
    try:
        with Image.open(image_path) as img:
            # Get EXIF data
            exif_data = img._getexif()
            if exif_data:
                for tag_id, value in exif_data.items():
                    tag = TAGS.get(tag_id, tag_id)
                    if isinstance(value, str) and value.startswith('http'):
                        log_message(f"  ✅ Found URL in EXIF {tag}: {value}")
                        return value
            
            # Check for custom metadata fields that might contain URLs
            info = img.info
            if info:
                for key, value in info.items():
                    if isinstance(value, str) and value.startswith('http'):
                        log_message(f"  ✅ Found URL in image info {key}: {value}")
                        return value
                        
    except Exception as e:
        log_message(f"  ⚠️ Could not extract URL from metadata: {e}")
    
    return None

def remove_white_background(image_path):
    """Remove extra white background around an image"""
    try:
        # First make sure the file exists and has content
        if not os.path.exists(image_path) or os.path.getsize(image_path) == 0:
            log_message(f"  ❌ Cannot process empty or non-existent file: {image_path}")
            return False
            
        # Open the image in RGB mode first to ensure it's a valid image
        try:
            original_img = Image.open(image_path)
            # Verify format
            if original_img.format not in ['JPEG', 'PNG', 'WEBP']:
                log_message(f"  ❌ Invalid image format for {image_path}: {original_img.format}")
                return False
        except Exception as e:
            log_message(f"  ❌ Cannot open image file {image_path}: {e}")
            return False
            
        # Make a copy of the original image for safety, ensuring a valid extension
        base, ext = os.path.splitext(image_path)
        temp_path = base + ".tempcopy" + ext # e.g., image.jpg -> image.tempcopy.jpg

        original_img.save(temp_path)
        
        # Try to open in RGBA
        try:
            img = Image.open(temp_path).convert('RGBA')
        except Exception as e:
            log_message(f"  ❌ Cannot convert image to RGBA: {e}")
            # Cleanup and keep original file
            if os.path.exists(temp_path):
                os.remove(temp_path)
            return False
        
        # Get the data
        data = img.getdata()
        
        # Create a new image with transparent background
        new_data = []
        for item in data:
            # If pixel is almost white (threshold can be adjusted)
            if item[0] > 245 and item[1] > 245 and item[2] > 245:
                # Make it transparent
                new_data.append((255, 255, 255, 0))
            else:
                new_data.append(item)
        
        # Update the image with transparent background
        img.putdata(new_data)
        
        # Trim the transparent edges
        bg = Image.new(img.mode, img.size, (0, 0, 0, 0))
        diff = ImageChops.difference(img, bg)
        bbox = diff.getbbox()
        
        if bbox:
            img = img.crop(bbox)
        
        # Save the trimmed image with proper format
        try:
            # Get file extension
            _, ext = os.path.splitext(image_path)
            if ext.lower() in ['.jpg', '.jpeg']:
                # Convert RGBA to RGB for JPEG
                img = img.convert('RGB')
                img.save(image_path, 'JPEG', quality=95)
            elif ext.lower() == '.png':
                img.save(image_path, 'PNG')
            else:
                # Default to JPEG
                img = img.convert('RGB')
                img.save(image_path, 'JPEG', quality=95)
                
            log_message(f"  ✅ Removed white background from: {os.path.basename(image_path)}")
            
            # Clean up temp file
            if os.path.exists(temp_path):
                os.remove(temp_path)
                
            return True
        except Exception as e:
            log_message(f"  ❌ Failed to save processed image: {e}")
            # Restore original if we failed
            if os.path.exists(temp_path):
                shutil.copy2(temp_path, image_path)
                os.remove(temp_path)
            return False
            
    except Exception as e:
        log_message(f"  ❌ Error removing white background from {os.path.basename(image_path)}: {e}")
        return False

def extract_feature_vector(image_path):
    """Extract feature vector from image using MobileNetV2"""
    # First verify the image exists and has content
    if not os.path.exists(image_path):
        log_message(f"  ❌ Image file does not exist: {image_path}")
        return None
        
    if os.path.getsize(image_path) == 0:
        log_message(f"  ❌ Image file is empty: {image_path}")
        return None
    
    # Try to validate the image before processing
    try:
        with Image.open(image_path) as img:
            # Check if image is valid
            img.verify()
            if img.format not in ['JPEG', 'PNG', 'WEBP', 'BMP', 'GIF']:
                log_message(f"  ❌ Unsupported image format in {image_path}: {img.format}")
                return None
    except Exception as e:
        log_message(f"  ❌ Invalid image file {image_path}: {e}")
        return None
    
    # Load model with pre-trained weights
    weights = MobileNet_V2_Weights.DEFAULT
    model = models.mobilenet_v2(weights=weights).features.eval()
    preprocess = weights.transforms()
    
    try:
        # Open in RGB mode explicitly
        image = Image.open(image_path).convert('RGB')
        # Ensure the image has actual content
        if image.width <= 10 or image.height <= 10:
            log_message(f"  ❌ Image too small to process: {image_path}")
            return None
            
        tensor = preprocess(image).unsqueeze(0)
        with torch.no_grad():
            output_tensor = model(tensor)
            squeezed_tensor = output_tensor.squeeze()
            numpy_features = squeezed_tensor.numpy()
        return numpy_features.flatten()
    except Exception as e:
        log_message(f"  ❌ Error extracting features from {image_path}: {e}")
        return None

def compute_similarity(source_vector, target_vector):
    """Compute cosine similarity between two feature vectors"""
    if source_vector is None or target_vector is None:
        return 0
    return cosine_similarity([source_vector], [target_vector])[0][0]

def save_image_data_to_json(image_data, json_file):
    """Save image data to a JSON file"""
    try:
        # Load existing data if the file exists
        if os.path.exists(json_file):
            with open(json_file, 'r') as f:
                existing_data = json.load(f)
        else:
            existing_data = {}
        
        # Update with new data
        existing_data.update(image_data)
        
        # Save updated data
        with open(json_file, 'w') as f:
            json.dump(existing_data, f, indent=4)
            
        log_message(f"  ✅ Updated image data in {json_file}")
    except Exception as e:
        log_message(f"  ❌ Error saving image data to JSON: {e}")

def reverse_image_search_serpapi(image_url, api_key, num_results=10):
    """Perform reverse image search using SerpApi Google Lens"""
    try:
        params = {
            "engine": "google_lens",
            "url": image_url,
            "api_key": api_key,
            "hl": "en",
            "country": "us",
            "no_cache": "true"  # Get fresh results
        }
        
        log_message(f"  🔍 Searching for similar images using SerpApi...")
        response = requests.get("https://serpapi.com/search", params=params, timeout=30)
        response.raise_for_status()
        
        data = response.json()
        
        # Check for errors
        if "error" in data:
            log_message(f"  ❌ SerpApi error: {data['error']}")
            return []
        
        # Get visual matches
        visual_matches = data.get("visual_matches", [])
        
        if not visual_matches:
            log_message("  ℹ️ No visual matches found")
            return []
        
        log_message(f"  ✅ Found {len(visual_matches)} visual matches")
        return visual_matches[:num_results]
        
    except requests.exceptions.RequestException as e:
        log_message(f"  ❌ Network error during SerpApi request: {e}")
        return []
    except Exception as e:
        log_message(f"  ❌ Error during SerpApi reverse search: {e}")
        return []

def upload_image_to_public_url(image_path, image_url=None):
    """
    Upload image to a public hosting service and return URL.
    If image_url is provided, use it directly. Otherwise, try to extract from metadata first, 
    then fallback to a simple approach.
    In production, you might want to use services like ImgBB, Cloudinary, etc.
    """
    # If image_url is provided directly, use it
    if image_url:
        log_message(f"  ✅ Using provided image URL: {image_url}")
        return image_url
    
    # First try to extract URL from metadata
    metadata_url = extract_image_url_from_metadata(image_path)
    if metadata_url:
        log_message(f"  ✅ Using URL from image metadata: {metadata_url}")
        return metadata_url
    
    # If no URL in metadata, we need to upload the image somewhere
    # For this example, we'll use a simple approach - you might want to implement
    # a proper image hosting service integration
    log_message(f"  ⚠️ No URL found in metadata for {image_path}")
    log_message(f"  ℹ️ You may need to implement image hosting service integration")
    
    # Return None to indicate we couldn't get a public URL
    return None

############################################################reverse search function#####################################

@observe()
def reverse_search_and_download(image_path, image_url=None, output_folder="similar_images", final_folder="final_selection", max_images=10):
    """
    Perform reverse image search using SerpApi Google Lens, download similar images,
    and filter based on similarity to the original image.
    Skips images from blacklisted websites and keeps images with
    similarity > 0.4 in the final selection folder.
    
    Args:
        image_path: Local path to the image file
        image_url: Optional direct URL to the image (if available)
        output_folder: Folder to store all downloaded images
        final_folder: Folder to store filtered similar images
        max_images: Maximum number of images to download
    """
    # Create output folders if they don't exist
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    if not os.path.exists(final_folder):
        os.makedirs(final_folder)
    
    # Define JSON files for storing image URLs
    all_images_json = os.path.join(output_folder, "image_urls.json")
    final_images_json = os.path.join(final_folder, "final_image_urls.json")
    
    # Data dictionaries for JSON storage
    all_image_data = {}
    final_image_data = {}
    
    # Dictionary to track all URLs and their status (including blacklisted ones)
    url_tracking = {
        "total_attempts": 0,
        "successful_downloads": 0,
        "blacklisted_urls": [],
        "failed_downloads": []
    }
    
    # Extract features from the source image
    log_message(f"✨ Extracting features from source image: {image_path}")
    source_features = extract_feature_vector(image_path)
    if source_features is None:
        log_message("❌ Error: Could not extract features from source image.")
        return []

    downloaded_images = []
    downloaded_urls = {}  # Track URL for each downloaded image
    
    try:
        log_message(f"✨ Starting reverse image search for: {image_path}")
        
        # Get public URL for the image (use provided URL, from metadata, or upload)
        search_image_url = upload_image_to_public_url(image_path, image_url)
        
        if not search_image_url:
            log_message("❌ Could not get public URL for image. Cannot proceed with reverse search.")
            return []
        
        # Perform reverse image search using SerpApi
        visual_matches = reverse_image_search_serpapi(search_image_url, SERPAPI_KEY, max_images)
        
        if not visual_matches:
            log_message("❌ No visual matches found from SerpApi")
            return []
        
        # Process and download images from visual matches
        downloaded_count = 0
        
        for i, match in enumerate(visual_matches):
            if downloaded_count >= max_images:
                break
                
            # Get image URL from the match
            img_url = match.get("thumbnail") or match.get("link")
            
            if not img_url:
                log_message(f"  ⚠️ No image URL found in match {i+1}")
                continue
            
            # Track the attempt
            url_tracking["total_attempts"] += 1
            
            log_message(f"  ⬇️ [{i+1}/{len(visual_matches)}] Trying to download: {img_url[:50]}...")
            
            # Check if URL is from blacklisted website
            if is_blacklisted_url(img_url):
                url_tracking["blacklisted_urls"].append(img_url)
                log_message(f"  ⚠️ Skipping blacklisted image URL: {img_url[:50]}...")
                continue
            
            # Download the image
            downloaded_path, url = download_google_image(img_url, output_folder)
            if downloaded_path:
                downloaded_count += 1
                downloaded_images.append(downloaded_path)
                downloaded_urls[downloaded_path] = url  # Track URL for each image
                url_tracking["successful_downloads"] += 1
                
                # Store the image data for JSON with additional metadata
                image_metadata = {
                    "url": url,
                    "source": match.get("source", ""),
                    "title": match.get("title", ""),
                    "position": i + 1
                }
                all_image_data[os.path.basename(downloaded_path)] = image_metadata
            else:
                url_tracking["failed_downloads"].append(img_url)
        
        log_message(f"  ✅ Downloaded {len(downloaded_images)} images out of requested {max_images}")
        
        # Save the URLs of all downloaded images to JSON
        save_image_data_to_json(all_image_data, all_images_json)
        
        # Now compare similarity and move the most similar images to final_folder
        if downloaded_images:
            log_message("\n📊 Analyzing image similarity using MobileNetV2...")
            similarities = []
            valid_images = []
            
            # First verify which downloaded images are valid
            for img_path in downloaded_images:
                if os.path.exists(img_path) and os.path.getsize(img_path) > 0:
                    try:
                        with Image.open(img_path) as test_img:
                            # Just open to check if it's valid
                            valid_images.append(img_path)
                    except Exception as e:
                        log_message(f"  ⚠️ Skipping invalid image {os.path.basename(img_path)}: {e}")
            
            if not valid_images:
                log_message("  ℹ️ No valid images downloaded to process")
                return downloaded_images
                
            log_message(f"  ✅ Found {len(valid_images)} valid images out of {len(downloaded_images)} downloaded")
            
            # Extract features and compute similarity for each valid downloaded image
            for img_path in valid_images:
                features = extract_feature_vector(img_path)
                if features is not None:
                    similarity = compute_similarity(source_features, features)
                    similarities.append((img_path, similarity))
                    log_message(f"    🖼️ Image: {os.path.basename(img_path)}, Similarity: {similarity:.4f}")
            
            if not similarities:
                log_message("  ℹ️ Could not compute similarity for any images")
                return downloaded_images
            
            # Sort by similarity (highest first)
            similarities.sort(key=lambda x: x[1], reverse=True)
            
            # Copy the most similar images to the final folder
            copied_count = 0
            for img_path, similarity in similarities:
                if similarity >= SIMILARITY_THRESHOLD:
                    try:
                        img_filename = os.path.basename(img_path)
                        dest_path = os.path.join(final_folder, img_filename)
                        
                        # Make sure source file is valid before copying
                        if os.path.exists(img_path) and os.path.getsize(img_path) > 0:
                            # Copy with error handling
                            try:
                                shutil.copy2(img_path, dest_path)
                                copied_count += 1
                                log_message(f"  ✅ Copied to final selection: {img_filename} (similarity: {similarity:.4f})")
                                
                                # Add to final images JSON data with similarity score
                                if img_filename in all_image_data:
                                    final_image_data[img_filename] = {
                                        **all_image_data[img_filename],  # Include all original metadata
                                        "similarity": float(similarity)
                                    }
                            except Exception as e:
                                log_message(f"  ❌ Error copying {img_path} to final folder: {e}")
                    except Exception as e:
                        log_message(f"  ❌ Error processing similarity match: {e}")
            
            # Always include at least one image in final selection (the most similar) if we found any
            if copied_count == 0 and similarities:
                try:
                    best_img, best_sim = similarities[0]
                    if os.path.exists(best_img) and os.path.getsize(best_img) > 0:
                        img_filename = os.path.basename(best_img)
                        dest_path = os.path.join(final_folder, img_filename)
                        shutil.copy2(best_img, dest_path)
                        log_message(f"  ✅ Copied best match to final selection: {img_filename} (similarity: {best_sim:.4f})")
                        copied_count = 1
                        
                        # Add to final images JSON data
                        if img_filename in all_image_data:
                            final_image_data[img_filename] = {
                                **all_image_data[img_filename],  # Include all original metadata
                                "similarity": float(best_sim)
                            }
                except Exception as e:
                    log_message(f"  ❌ Error copying best match: {e}")
            
            # Save the URLs of final selected images to JSON
            if final_image_data:
                save_image_data_to_json(final_image_data, final_images_json)
            
            log_message(f"\n🎉 Process complete. {copied_count} images selected in final folder.")
            
            # Print URL tracking information
            log_message("\n📋 URL Tracking Summary:")
            log_message(f"  Total image attempts: {url_tracking['total_attempts']}")
            log_message(f"  Successful downloads: {url_tracking['successful_downloads']}")
            log_message(f"  Blacklisted URLs skipped: {len(url_tracking['blacklisted_urls'])}")
            log_message(f"  Failed downloads: {len(url_tracking['failed_downloads'])}")
            
        return downloaded_images
        
    except Exception as e:
        log_message(f"❌ Error during reverse image search: {e}")
        return downloaded_images

######################################## download function ###################################################

def download_google_image(url, save_dir, filename=None):
    """Download an image from URL and save it to disk"""
    try:
        # Check if the URL is from a blacklisted domain
        if is_blacklisted_url(url):
            log_message(f"  ⚠️ Skipping blacklisted URL: {url}")
            return None, url  # Return the URL to log it
        
        # Set headers to mimic a browser request
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.9',
            'Referer': 'https://www.google.com/'
        }
        
        response = requests.get(url, headers=headers, timeout=15)
        response.raise_for_status()
        
        # Check if response content is an image
        content_type = response.headers.get('Content-Type', '')
        if not content_type.startswith('image/'):
            log_message(f"  ⚠️ Warning: URL did not return an image. Content-Type: {content_type}")
        
        # Verify content length
        if len(response.content) < 1000:  # Extremely small files are likely not valid images
            log_message(f"  ⚠️ Warning: Downloaded content is too small ({len(response.content)} bytes)")
            return None, url
        
        # Try to open the image to verify it's valid
        try:
            img = Image.open(BytesIO(response.content))
            # Verify it's an image
            img.verify()
            format_name = img.format
            
            # Generate a filename if none provided, use correct extension
            if not filename:
                if format_name == 'JPEG':
                    ext = '.jpg'
                elif format_name:
                    ext = f'.{format_name.lower()}'
                else:
                    ext = '.jpg'  # Default
                filename = f"image_{uuid.uuid4().hex[:8]}{ext}"
            
            file_path = os.path.join(save_dir, f"serpapi_{filename}")
            
            # Save the image using PIL for better format control
            img = Image.open(BytesIO(response.content))
            if img.mode == 'RGBA' and filename.lower().endswith(('.jpg', '.jpeg')):
                img = img.convert('RGB')  # Convert RGBA to RGB for JPEG
                
            img.save(file_path)
            
            log_message(f"  ✅ Successfully downloaded: {file_path}")
            
            # Remove white background only if image is saved successfully
            if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                remove_white_background(file_path)
            
            return file_path, url
            
        except Exception as e:
            log_message(f"  ❌ Downloaded file is not a valid image: {e}")
            # Try alternative direct saving method
            try:
                if not filename:
                    filename = f"image_{uuid.uuid4().hex[:8]}.jpg"
                
                file_path = os.path.join(save_dir, filename)
                
                # Direct save without verification
                with open(file_path, 'wb') as f:
                    f.write(response.content)
                
                # Check if file is empty
                if os.path.getsize(file_path) == 0:
                    log_message("  ❌ Downloaded file is empty, removing it")
                    os.remove(file_path)
                    return None, url
                
                log_message(f"  ✅ Directly saved image (unverified): {file_path}")
                return file_path, url
                
            except Exception as e:
                log_message(f"  ❌ Failed to save image directly: {e}")
                return None, url
            
    except Exception as e:
        log_message(f"  ❌ Failed to download image: {e}")
        return None, url

######################################## for running script seperately ###################################################

def process_images_in_folder(input_folder, max_images=10, threshold=0.8):
    global SIMILARITY_THRESHOLD
    SIMILARITY_THRESHOLD = threshold
    
    # Define shared output and final folders inside input folder
    output_folder = os.path.join(input_folder, "similar_images")
    final_folder = os.path.join(input_folder, "final_selection")
    os.makedirs(output_folder, exist_ok=True)
    os.makedirs(final_folder, exist_ok=True)
    
    # Define JSON files for all images in the run
    all_run_json = os.path.join(input_folder, "all_image_urls.json")
    all_run_data = {}
    
    # Collect image files
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.webp', '*.bmp', '*.tiff']
    image_files = []
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(input_folder, ext)))
        image_files.extend(glob.glob(os.path.join(input_folder, ext.upper())))
    
    if not image_files:
        log_message(f"  ℹ️ No images found in {input_folder}")
        return
    
    log_message(f"  🔍 Found {len(image_files)} images. Processing...")
    
    # Process each image
    for i, image_path in enumerate(image_files):
        log_message(f"  [{i+1}/{len(image_files)}] Processing: {os.path.basename(image_path)}")
        
        # Create a sub-entry for this source image
        source_name = os.path.basename(image_path)
        all_run_data[source_name] = {"similar_images": {}}
        
        # Run reverse search (no image_url parameter for folder processing)
        downloaded_imgs = reverse_search_and_download(
            image_path, 
            image_url=None,  # Let it extract from metadata or upload
            output_folder=output_folder, 
            final_folder=final_folder, 
            max_images=max_images
        )
        
        # Collect all data for this source image
        if os.path.exists(os.path.join(output_folder, "image_urls.json")):
            with open(os.path.join(output_folder, "image_urls.json"), 'r') as f:
                img_data = json.load(f)
                all_run_data[source_name]["similar_images"] = img_data
        
        if os.path.exists(os.path.join(final_folder, "final_image_urls.json")):
            with open(os.path.join(final_folder, "final_image_urls.json"), 'r') as f:
                final_data = json.load(f)
                all_run_data[source_name]["final_selection"] = final_data
        
        # Save the combined data after each image
        with open(all_run_json, 'w') as f:
            json.dump(all_run_data, f, indent=4)
        
        if i < len(image_files) - 1:
            log_message("  ⏳ Waiting to avoid rate limiting...")
            time.sleep(5)
    
    log_message(f"🎉 Processing complete. All image URLs saved to {all_run_json}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Batch Google Reverse Image Search with MobileNetV2 Similarity Filtering")
    parser.add_argument("input_path", help="Path to image file or folder containing images")
    parser.add_argument("--max", type=int, default=5, help="Maximum number of images to download per input image")
    parser.add_argument("--threshold", type=float, default=0.8, help="Similarity threshold (0.0-1.0)")
    parser.add_argument("--url", type=str, default=None, help="Image URL to use for reverse search (for single image only)")
    args = parser.parse_args()
    
    if os.path.isfile(args.input_path):
        # Single image case
        input_folder = os.path.dirname(args.input_path)
        output_folder = os.path.join(input_folder, "similar_images")
        final_folder = os.path.join(input_folder, "final_selection")
        os.makedirs(output_folder, exist_ok=True)
        os.makedirs(final_folder, exist_ok=True)
        
        SIMILARITY_THRESHOLD = args.threshold
        
        # Use provided URL if available, otherwise let function handle it
        image_url = args.url if args.url else None
        if image_url:
            log_message(f"🔗 Using provided image URL: {image_url}")
        else:
            log_message("📋 No URL provided, will attempt to extract from metadata or upload")
        
        reverse_search_and_download(
            args.input_path, 
            image_url=image_url,
            output_folder=output_folder, 
            final_folder=final_folder, 
            max_images=args.max
        )
    elif os.path.isdir(args.input_path):
        # Folder case
        if args.url:
            log_message("⚠️ Warning: --url parameter is ignored when processing folders")
        process_images_in_folder(args.input_path, max_images=args.max, threshold=args.threshold)
    else:
        log_message(f"❌ Invalid path: {args.input_path}")

# Example usage:
# python script.py /path/to/image.jpg --url "https://example.com/image.jpg" --max 10 --threshold 0.8
# python script.py /path/to/image.jpg --max 10 --threshold 0.8
# python script.py /path/to/folder --max 10 --threshold 0.8