from langchain_google_genai import Chat<PERSON>oogleGener<PERSON><PERSON><PERSON>
from browser_use import Agent, Browser
from browser_use.browser.context import Browser<PERSON>ontext
from pydantic import SecretStr
import os
from dotenv import load_dotenv
import Common.Constants as Constants

load_dotenv()

# Documentation: https://docs.browser-use.com/customize/custom-functions

async def get_original_picture():
    # Create agent with the model
    browser = Browser() # Reuse existing browser

    initial_actions = [
        {'open_tab': {'url': 'https://maijiazhichi.com/archives/55915'}},
        # {'scroll_down': {'amount': 1000}},
    ]

    # agent = Agent(
    #     task="Save all the copyright pictures related to case 25-cv-00097 to the current working director.",
    #     initial_actions=initial_actions,    # Otpional
    #     llm=ChatGoogleGenerativeAI(model='gemini-2.0-flash-exp', api_key=SecretStr(os.getenv('GEMINI_API_KEY'))),   # can try ChatVertexAI from langchain. Need to read how to set it up

    agent = Agent(
        task="Save all the copyright pictures related to case 25-cv-00097 to the current working director.",
        initial_actions=initial_actions,    # Otpional
        llm=ChatGoogleGenerativeAI(model='gemini-2.0-flash-exp', api_key=SecretStr(os.getenv('GEMINI_API_KEY'))),   # can try ChatVertexAI from langchain. Need to read how to set it up
        planner_llm = ChatGoogleGenerativeAI(model=Constants.SMART_MODEL_FREE, api_key=SecretStr(os.getenv('GEMINI_API_KEY'))),  # Optional
        use_vision_for_planner=False,       # Disable vision for planner
        planner_interval=4,                  # Plan every 4 steps
        browser=browser                     # Browser instance will be reused // Optional
    )

    await agent.run()

    # Manually close the browser
    await browser.close()

async def get_original_picture2():
    print(os.getenv('GEMINI_API_KEY'))

    initial_actions = [{'open_tab': {'url': 'https://maijiazhichi.com/archives/55915'}}]

    agent = Agent(
        task="Save all the copyright pictures related to case 25-cv-00097 to the current working director.",
        initial_actions=initial_actions,    # Otpional
        llm=ChatGoogleGenerativeAI(model='gemini-2.0-flash-exp', api_key=SecretStr(os.getenv('GEMINI_API_KEY'))),   # can try ChatVertexAI from langchain. Need to read how to set it up
    )

    await agent.run()

if __name__ == "__main__":
    import asyncio
    asyncio.run(get_original_picture2())