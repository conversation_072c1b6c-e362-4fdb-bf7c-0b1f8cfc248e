import os
import asyncio
import shutil
import pandas as pd
from copyright_image_helpers import (
    EnhancedApproachA, 
    clean_claimant, 
    clean_title, 
    select_best_copyright_image_from_search_results,
    check_image_size,
    sanitize_name,
    display_image_and_ask_user_server
)


async def process_single_copyright_record(claimant: str, title: str, basis_of_claim: str = None, 
                                        nation_of_publication: str = None, all_images_folder_path: str = "all_images",
                                        best_images_folder_path: str = "best_images", num_results: int = 5):
    """
    Process a single copyright record to find the best matching image.
    
    Args:
        claimant (str): Copyright claimant name
        title (str): Title of the work
        basis_of_claim (str, optional): Basis of claim
        nation_of_publication (str, optional): Nation of first publication
        all_images_folder_path (str): Path to store all downloaded images
        best_images_folder_path (str): Path to store best matched images
        num_results (int): Number of search results to process
    
    Returns:
        dict: Result containing best image URL, score, and metadata
    """
    os.makedirs(all_images_folder_path, exist_ok=True)
    os.makedirs(best_images_folder_path, exist_ok=True)
    
    approach_a = EnhancedApproachA()
    
    # Clean inputs
    clean_claimant_name = clean_claimant(claimant)
    clean_title_name = clean_title(title)
    
    print(f"\n{'='*60}")
    print(f"Processing Single Record")
    print(f"Claimant: {clean_claimant_name}")
    print(f"Title: {clean_title_name}")
    print(f"{'='*60}")
    
    result = {
        'claimant': clean_claimant_name,
        'title': clean_title_name,
        'best_image_url': None,
        'best_score': -1.0,
        'best_category': None,
        'explanation': None,
        'image_metadata': None,
        'success': False
    }
    
    try:
        # Generate search queries
        queries = approach_a.generate_simplified_search_queries(clean_title_name, clean_claimant_name, basis_of_claim)
        print(f" Generated {len(queries)} search queries")
        
        # Execute search
        all_results = await approach_a.execute_simplified_search(queries, all_images_folder_path)
        
        if not all_results:
            print(f"\033[91m No images found for: {clean_claimant_name} - {clean_title_name}\033[0m")
            return result
        
        # Remove duplicates and rank results
        final_results = approach_a.remove_duplicates_and_rank(all_results)
        
        # Count preferred site results
        preferred_count = sum(1 for _, metadata in final_results if metadata.get('is_preferred_site', False))
        print(f"Final results: {len(final_results)} unique images ({preferred_count} from preferred sites)")
        
        # Analyze with Gemini
        search_query = f"{clean_claimant_name} {clean_title_name}"
        gemini_results, best_category, explanation_summary = await select_best_copyright_image_from_search_results(
            search_query, final_results, clean_claimant_name, clean_title_name, basis_of_claim, nation_of_publication
        )
        
        if not gemini_results:
            print(" No analysis results returned from Gemini")
            return result
        
        # Process results and find best match
        best_match_score = -1.0
        best_match_path = None
        best_metadata = None
        best_result_key = None
        
        # Create mapping from result keys to actual data
        result_key_to_data = {}
        for i, (image_path, metadata) in enumerate(final_results):
            result_key = f"Result {i + 1}"
            result_key_to_data[result_key] = (image_path, metadata)
        
        # Find best match from Gemini results
        if isinstance(gemini_results, dict):
            for result_key, score_data in gemini_results.items():
                try:
                    gemini_score = float(score_data.get('match', -1.0))
                    if gemini_score > best_match_score:
                        best_match_score = gemini_score
                        best_result_key = result_key
                        if result_key in result_key_to_data:
                            best_match_path, best_metadata = result_key_to_data[result_key]
                except (ValueError, TypeError) as e:
                    print(f"[ERROR] Error processing result {result_key}: {e}")
        
        # Display results
        print(f"\n Analysis Results:")
        print(f"Best Match Score: {best_match_score:.3f}")
        print(f"Best Category: {best_category}")
        print(f"Explanation: {explanation_summary}")
        
        # Check if we found a valid match above threshold
        if best_match_path and best_match_score > 0.3:  # Minimum threshold
            # Check image size one more time
            meets_req, width, height = check_image_size(best_match_path)
            if meets_req:
                # Prepare file paths for potential copying
                original_filename = os.path.basename(best_match_path)
                base_filename, file_extension = os.path.splitext(original_filename)
                safe_base_filename = sanitize_name(search_query.replace(" ", "_"))
                safe_result_key = sanitize_name(best_result_key.replace(" ", ""))
                
                best_filename_with_suffix = f"{safe_base_filename}_{safe_result_key}{file_extension}"
                best_image_path = os.path.join(best_images_folder_path, best_filename_with_suffix)
                
                # Display image and ask user for confirmation
                should_save = display_image_and_ask_user_server(
                    image_path=best_match_path,
                    score=best_match_score,
                    category=best_category,
                    explanation=explanation_summary,
                    claimant=clean_claimant_name,
                    title=clean_title_name,
                    image_url=best_metadata.get('link', ''),
                    size_info=f"{width}x{height}"
                )
                
                if should_save:
                    try:
                        shutil.copy(best_match_path, best_image_path)
                        print(f" Image saved to: {best_image_path}")
                    except Exception as e:
                        print(f"Error copying image: {e}")
                        best_image_path = best_match_path  # Use original path if copy fails
                else:
                    best_image_path = best_match_path
                    print(f"ℹ Image not saved to best folder (user declined)")
                
                # Update result with success
                result.update({
                    'best_image_url': best_metadata.get('link', ''),
                    'best_score': best_match_score,
                    'best_category': best_category,
                    'explanation': explanation_summary,
                    'image_metadata': best_metadata,
                    'success': True,
                    'image_size': f"{width}x{height}",
                    'local_image_path': best_image_path
                })
                
                print(f"Best match found: {best_metadata.get('displayLink', '')} (Score: {best_match_score:.3f})")
                print(f"   Image size: {width}x{height} pixels")
                print(f"   Image URL: {best_metadata.get('link', '')}")
            else:
                print(f"Best match image too small: {width}x{height} pixels")
        else:
            print(f" No suitable match found (score: {best_match_score:.3f})")
            
    except Exception as e:
        print(f" Error processing {clean_claimant_name} - {clean_title_name}: {e}")
        result['error'] = str(e)
    
    return result

# ========== ALTERNATIVE USAGE EXAMPLES ==========

def example_single_record():
    """Example for processing a single copyright record."""
    print("Running single record example...")
    
    # Single record data
    claimant = "Atlantic Recording Corporation"
    title = "Lost brotherhood GOWAN"
    basis_of_claim = "Visual Arts Work"
    nation_of_publication = "United Kingdom"
    
    # Setup paths
    base_folder = "single_record_test"
    all_images_path = os.path.join(base_folder, "all_images_test")
    best_images_path = os.path.join(base_folder, "best_images_test")
    
    # Run processing
    result = asyncio.run(
        process_single_copyright_record(
            claimant=claimant,
            title=title,
            basis_of_claim=basis_of_claim,
            nation_of_publication=nation_of_publication,
            all_images_folder_path=all_images_path,
            best_images_folder_path=best_images_path,
            num_results=5
        )
    )
    
    print("\n" + "="*60)
    print("SINGLE RECORD PROCESSING COMPLETE!")
    print("="*60)
    
    if result['success']:
        print(f" Success! Found best match:")
        print(f"   Claimant: {result['claimant']}")
        print(f"   Title: {result['title']}")
        print(f"   Score: {result['best_score']:.3f}")
        print(f"   Category: {result['best_category']}")
        print(f"   Image URL: {result['best_image_url']}")
        print(f"   Image Size: {result.get('image_size', 'Unknown')}")
        print(f"   Local Path: {result.get('local_image_path', 'Unknown')}")
        print(f"   Explanation: {result['explanation']}")
    else:
        print(f"No suitable match found for {result['claimant']} - {result['title']}")
        if 'error' in result:
            print(f"   Error: {result['error']}")
    
    return result


# Run the example
if __name__ == "__main__":
    # You can run either version:
    
    # Option 1: Direct single record processing
    example_single_record()
