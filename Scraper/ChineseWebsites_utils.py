#nofloqa
import asyncio, aiohttp
from logdata import log_message

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗


# --- Website Configuration ---
WEBSITE_CONFIG = [
    {
        "name": "10100",
        "search_url_template": "https://www.10100.com/search/xxxx",
        "exclude_words": [], # Add specific words if needed for this site during URL finding
        "watermark_description": "blue and white diagonal retangle watermark", # To be used later if needed
        "image_download_pattern": None, # Pattern for images to download from this site
        "content_selector": "div[sensor='ContentRead']" # Selects the div with the 'sensor' attribute equal to 'ContentRead'
    },
    {
        "name": "SellerDefense",
        "search_url_template": "https://sellerdefense.cn/?s=xxxx",
        "exclude_words": ["allcase"], # Used in find_target_urls and find_target_urls_using_markdown
        "watermark_description": "yellow diagonal SellerDefense watermark",
        "image_download_pattern": None, # No specific pattern for this site
        "content_selector": ".content"
    },
    {
        "name": "<PERSON>jiaz<PERSON><PERSON>",
        "search_url_template": "https://maijiazhichi.com/?s=xxxx",
        "exclude_words": ["tro"], # Used in find_target_urls and find_target_urls_using_markdown
        "watermark_description": "grey-blue diagonal watermark",
        "image_download_pattern": None, # No specific pattern for this site
        "content_selector": ".entry-main"
    },
    # Add more site configurations as needed
]
# --------------------------


async def fetch_url(session: aiohttp.ClientSession, url: str) -> str | None:
    """Fetches content from a given URL."""
    try:
        async with session.get(url, timeout=40) as response:
            response.raise_for_status() # Raise an exception for bad status codes
            return await response.text()
    except aiohttp.ClientError as e:
        log_message(f"      Error fetching {url}: {e}")
        return None
    except asyncio.TimeoutError:
        log_message(f"      Timeout fetching {url}")
        return None