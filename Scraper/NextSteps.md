1. The way that the AI identifies the reg number and images in B is not perfect
2. 
3. The output of B should be checked to ensure the identifier is not a URL (http)
4. The prompt in B is still not great at avoiding tables with the registration numbers but no pictures.
5. We need a D module that takes all the final pictures and try to map the pictures to the reg numbers



* The output of all B should be used to create a master list of reg number and art description (from USCO?)
* The output of B should be cleaned up if multiple reg have the same identifier (i.e. image): is it multi or is it a table with reg number and no pictures (ask flash 2.0 exp)
* Reverse check should be done for parts as well
