import os
import shutil
import argparse
from PIL import Image
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import cv2
from sklearn.cluster import DBSCAN
from logdata import log_message
import re
from typing import List, Dict, Any, Callable, Optional, Tuple
from langfuse import observe
import langfuse
import traceback

# Parameters
SIMILARITY_THRESHOLD = 0.60
TEXT_THRESHOLD = 100  # character count

model = None
preprocess = None

def ensure_folder(folder):
    if not os.path.exists(folder):
        os.makedirs(folder)

def get_all_images(folder):
    return [os.path.join(folder, f) for f in os.listdir(folder)
            if f.lower().endswith((".jpg", ".jpeg", ".png"))]

def extract_feature_vector(image_path):
    import torch
    from torchvision import models, transforms
    
    global model
    if model is None:
        # Load MobileNetV2 (feature extractor only)
        model = models.mobilenet_v2(weights='MobileNet_V2_Weights.IMAGENET1K_V1').features.eval()
        
    global preprocess
    if preprocess is None:
        # Image preprocessing for CNN
        preprocess = transforms.Compose([
            transforms.Resize((224, 224)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                std=[0.229, 0.224, 0.225])
        ])

    image = Image.open(image_path).convert('RGB')
    tensor = preprocess(image).unsqueeze(0)
    with torch.no_grad():
        output_tensor = model(tensor)
        squeezed_tensor = output_tensor.squeeze()
        numpy_features = squeezed_tensor.numpy()
    return numpy_features.flatten()

def text_heavy(image_path):
    try:
        image = cv2.imread(image_path)
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Binarize image
        _, thresh = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY_INV)

        # Dilate to merge text lines
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (5, 5))
        dilated = cv2.dilate(thresh, kernel, iterations=1)

        # Find contours
        contours, _ = cv2.findContours(dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Count text-like regions
        text_like_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > 100]

        # Heuristic: If too many text-like areas, mark as text-heavy
        if len(text_like_contours) > 15:
            return True

        return False
    except Exception as e:
        print(f"Text-detection failed for {image_path}: {e}")
        return False


def is_collage_or_screenshot(image_path, edge_threshold=100, grid_sensitivity=0.6):
    """
    Detect if an image is a collage based on:
    1. Strong edge detection (collages have many strong edges between sections)
    2. Grid-like structures (many collages use grid layouts)
    3. Color diversity (collages often have distinctly different color regions)
    
    Args:
        image_path: Path to the image file
        edge_threshold: Threshold for edge detection sensitivity
        grid_sensitivity: Higher values make the function more likely to classify as collage
        
    Returns:
        Boolean indicating if the image is likely a collage
    """
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        return False
    
    # Resize for consistency and performance
    max_dimension = 800
    h, w = image.shape[:2]
    if max(h, w) > max_dimension:
        scale = max_dimension / max(h, w)
        image = cv2.resize(image, (int(w * scale), int(h * scale)))
    
    # 1. Edge detection
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    edges = cv2.Canny(gray, edge_threshold, edge_threshold * 2)
    edge_density = np.sum(edges > 0) / (edges.shape[0] * edges.shape[1])
    
    # 2. Grid detection
    # Look for horizontal and vertical lines
    h_lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100, minLineLength=image.shape[1]//5, maxLineGap=20)
    v_lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100, minLineLength=image.shape[0]//5, maxLineGap=20)
    
    line_count = 0
    if h_lines is not None:
        line_count += len(h_lines)
    if v_lines is not None:
        line_count += len(v_lines)
    
    # Normalize line count by image size
    normalized_line_count = line_count / ((image.shape[0] + image.shape[1]) / 2)
    
    # 3. Color diversity analysis
    # Downsample image for faster processing
    small = cv2.resize(image, (50, 50))
    pixels = small.reshape(-1, 3).astype(np.float32)
    
    # Cluster colors using DBSCAN
    dbscan = DBSCAN(eps=25, min_samples=5)
    dbscan.fit(pixels)
    labels = dbscan.labels_
    n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
    
    # Normalize cluster count
    max_expected_clusters = 15  # Expected max for a complex image
    normalized_clusters = min(n_clusters / max_expected_clusters, 1.0)
    
    # Combined score
    collage_score = (edge_density * 2 + normalized_line_count + normalized_clusters) / 4
    
    # Detect multi-panel grid pattern
    is_collage = collage_score > grid_sensitivity
    
    return is_collage



def deduplicate_images(input_folder, duplicate_folder):
    ensure_folder(duplicate_folder)
    image_paths = get_all_images(input_folder)
    feature_vectors = {}
    to_move = set()
    duplicate_results = {} # Dictionary to store original_moved_path -> similar_image_path

    log_message(f"Total images before filtering: {len(image_paths)}")

    # Step 1: Remove text-heavy or collage/screenshot images
    # print("Filtering out non-art images...")
    for path in image_paths:
        if is_collage_or_screenshot(path):  #text_heavy(path) or 
            print(f"Flagged as non-art: {path}")
            to_move.add(path)

    # Step 2: Extract features for images not flagged above
    # print("Extracting features...")
    filtered_paths = [p for p in image_paths if p not in to_move]
    for path in filtered_paths:
        try:
            feature_vectors[path] = extract_feature_vector(path)
        except Exception as e:
            print(f"[ERROR] Feature extraction failed for {path}: {e}")
            to_move.add(path)

    # Step 3: Compare feature vectors to find duplicates
    # print("Comparing images for duplicates...")
    image_list = list(feature_vectors.keys())
    for i in range(len(image_list)):
        for j in range(i + 1, len(image_list)):
            if image_list[j] in to_move:
                continue
            sim = cosine_similarity([feature_vectors[image_list[i]]],
                                    [feature_vectors[image_list[j]]])[0][0]
            if sim >= SIMILARITY_THRESHOLD:
                print(f"Duplicate found (score={sim:.3f}): {os.path.basename(image_list[i])} <-> {os.path.basename(image_list[j])}")
                if bool(re.match(r"^[a-zA-Z]{2}\d{10}", os.path.basename(image_list[j]))) and bool(re.match(r"^[a-zA-Z]{2}\d{10}", os.path.basename(image_list[i]))):
                    continue
                elif bool(re.match(r"^[a-zA-Z]{2}\d{10}", image_list[j])):
                    to_move.add(image_list[i])
                else:
                    to_move.add(image_list[j])
                
                # Store the relationship: duplicate_path -> original_similar_path
                duplicate_results[image_list[j]] = image_list[i]

    # Step 4: Safely move flagged images
    log_message(f"Total duplicates/non-art to remove: {len(to_move)}")
    for path in to_move:
        dest = os.path.join(duplicate_folder, os.path.basename(path))
        if os.path.exists(path):
            try:
                # print(f"Moving: {path} -> {dest}")
                shutil.move(path, dest)
                # Update the key in duplicate_results to the new destination path
                if path in duplicate_results:
                    similar_image_path = duplicate_results[path]
                    del duplicate_results[path]
                    duplicate_results[dest] = similar_image_path
            except Exception as e:
                print(f"[ERROR] Could not move {path}: {e}")
        # else:
        #     print(f"[SKIP] File already missing or moved: {path}")
    
    return duplicate_results


@observe(capture_input=False, capture_output=False)
def deduplicate_selected_images(selected_items, duplicate_folder):
    langfuse.get_client().update_current_span(
        input={
            "SelectedItemsCount": len(selected_items),
            "SelectedItems": selected_items,
            "DuplicateFolder": duplicate_folder
        }
    )
    
    
    try:
        ensure_folder(duplicate_folder)
        feature_vectors = {}

        log_message(f"Total selected items before filtering: {len(selected_items)}")

        # Step 1: Remove text-heavy or collage/screenshot images
        log_message("Filtering out non-art images...")
        # Create sets of paths (strings) instead of dicts
        paths_to_remove_non_art = set()
        for item in selected_items:
            path = item["download_path"]
            if is_collage_or_screenshot(path):  #text_heavy(path) or
                print(f"Flagged as non-art: {path}")
                paths_to_remove_non_art.add(path)
                shutil.copy(path, os.path.join(duplicate_folder, "non_art_" + os.path.basename(path)))
        # Filter by path
        selected_items = [item for item in selected_items if item["download_path"] not in paths_to_remove_non_art]


        # Step 2: Extract features for images not flagged above
        log_message("Extracting features...")
        paths_to_remove_no_feature = set()
        for item in selected_items:
            path = item["download_path"]
            try:
                feature_vectors[path] = extract_feature_vector(path)
            except Exception as e:
                print(f"[ERROR] Feature extraction failed for {path}: {e}")
                paths_to_remove_no_feature.add(path)
                shutil.copy(path, os.path.join(duplicate_folder, "feature_extraction_failed_" + os.path.basename(path)))
        # Filter by path
        selected_items = [item for item in selected_items if item["download_path"] not in paths_to_remove_no_feature]
                
                
        # Step 3: Compare feature vectors to find duplicates
        log_message("Comparing images for duplicates...")
        paths_to_remove_dupe = set()
        for i in range(len(selected_items)):
            for j in range(i+1, len(selected_items)):
                item = selected_items[i]
                other_item = selected_items[j]
                if j == i:
                    continue
                
                path = item["download_path"]
                other_path = other_item["download_path"]
                if path not in feature_vectors or other_path not in feature_vectors:
                    continue
                
                sim = cosine_similarity([feature_vectors[path]],
                                        [feature_vectors[other_path]])[0][0]
                if sim >= SIMILARITY_THRESHOLD:
                    log_message(f"Duplicate found (score={sim:.3f}): {path} <-> {other_path}")
                    if bool(re.match(r"^[a-zA-Z]{2}\d{10}", item["reg_no"])) and bool(re.match(r"^[a-zA-Z]{2}\d{10}", other_item["reg_no"])):
                        continue
                    elif bool(re.match(r"^[a-zA-Z]{2}\d{10}", other_item["reg_no"])):
                        shutil.copy(path, os.path.join(duplicate_folder, "duplicate_" + os.path.basename(path)))
                        paths_to_remove_dupe.add(path)
                    else:
                        shutil.copy(other_path, os.path.join(duplicate_folder, "duplicate_" + os.path.basename(other_path)))
                        paths_to_remove_dupe.add(other_path)
        # Filter by path
        selected_items = [item for item in selected_items if item["download_path"] not in paths_to_remove_dupe]
    except Exception as e:
        log_message(f"Error during deduplication: {e}, {traceback.format_exc()}")
        langfuse.get_client().update_current_span(
            metadata={
                "Error": str(e),
                "Traceback": traceback.format_exc()
            }
        )

    langfuse.get_client().update_current_span(
        output={
            # Updated to use paths instead of dicts
            "ItemsRemoved_NonArt": list(paths_to_remove_non_art),
            "ItemsRemoved_NoFeature": list(paths_to_remove_no_feature),
            "ItemsRemoved_Duplicate": list(paths_to_remove_dupe),
            "FinalSelectedItemsCount": len(selected_items),
            "FinalSelectedItems": selected_items
        }
    )
    
    return selected_items



def calculate_colorfulness(image_path):
    """
    Calculates the colorfulness of an image.

    This function implements the metric proposed by Hasler and Suesstrunk (2003).
    It splits the image into Red-Green (rg) and Yellow-Blue (yb) opponent color
    channels and computes the mean and standard deviation of their pixel values.
    The final score is a combination of these statistics.

    A higher score indicates a more colorful image. Grayscale images will have a
    score near 0.

    Args:
        image_path (str): Path to the input image file.

    Returns:
        float: The colorfulness score.
    """
    # Load the image
    image = cv2.imread(image_path)
    if image is None:
        print(f"[WARNING] Could not read image from {image_path}")
        return 0.0

    # 1. Split the image into B, G, R channels
    # astype("float") is important for the subtraction to prevent wrap-around
    (B, G, R) = cv2.split(image.astype("float"))

    # 2. Compute the Red-Green (rg) opponent channel
    rg = R - G

    # 3. Compute the Yellow-Blue (yb) opponent channel
    yb = 0.5 * (R + G) - B

    # 4. Compute the mean and standard deviation of both channels
    rg_mean, rg_std = np.mean(rg), np.std(rg)
    yb_mean, yb_std = np.mean(yb), np.std(yb)

    # 5. Combine the standard deviations and means
    std_root = np.sqrt((rg_std ** 2) + (yb_std ** 2))
    mean_root = np.sqrt((rg_mean ** 2) + (yb_mean ** 2))

    # 6. The final colorfulness metric is a weighted sum of the two
    colorfulness = std_root + (0.3 * mean_root)

    return colorfulness



# Example usage (you would need a real find_duplicate_sets_func):
if __name__ == '__main__':
    # This is a MOCK find_duplicate_sets_func for demonstration.
    # A real one would use image hashing (e.g., pHash, dHash) or ML model embeddings.
    def mock_find_duplicate_sets(image_paths: List[str]) -> List[List[str]]:
        print(f"Mock find_duplicate_sets called with {len(image_paths)} images.")
        # Naive grouping: images with "copy" in name are duplicates of non-"copy"
        groups: Dict[str, List[str]] = {}
        for path in image_paths:
            name = os.path.basename(path).lower()
            key = name.replace("_copy", "").replace("(1)", "").replace("(2)", "").split('.')[0]
            if key not in groups:
                groups[key] = []
            groups[key].append(path)
        return [group for group in groups.values() if len(group) > 1]

    # Create dummy files and directories for testing
    # ... (setup code for dummy files would go here for a runnable example) ...
    # print("Please set up dummy files and directories to run a full example.")
    pass










# def remove_text_duplicates(input_folder, duplicate_folder):
#     ensure_folder(duplicate_folder)
#     image_paths = get_all_images(input_folder)
#     feature_vectors = {}
#     to_move = set()

#     print(f"Total images before filtering: {len(image_paths)}")

#     # Step 1: Remove text-heavy or collage/screenshot images
#     print("Filtering out non-art images...")
#     for path in image_paths:
#         if text_heavy(path) or is_collage_or_screenshot(path):  
#             print(f"Flagged as non-art: {path}")
#             to_move.add(path)

#     # Step 2: Extract features for images not flagged above
#     print("Extracting features...")
#     filtered_paths = [p for p in image_paths if p not in to_move]
#     for path in filtered_paths:
#         try:
#             feature_vectors[path] = extract_feature_vector(path)
#         except Exception as e:
#             print(f"[ERROR] Feature extraction failed for {path}: {e}")
#             to_move.add(path)

#     # Step 3: Compare feature vectors to find duplicates
#     print("Comparing images for duplicates...")
#     image_list = list(feature_vectors.keys())
#     for i in range(len(image_list)):
#         for j in range(i + 1, len(image_list)):
#             if image_list[j] in to_move:
#                 continue
#             sim = cosine_similarity([feature_vectors[image_list[i]]],
#                                     [feature_vectors[image_list[j]]])[0][0]
#             if sim >= SIMILARITY_THRESHOLD:
#                 print(f"Duplicate found (score={sim:.3f}): {image_list[i]} <-> {image_list[j]}")
#                 to_move.add(image_list[j])

#     # Step 4: Safely move flagged images
#     print(f"Total duplicates/non-art to move: {len(to_move)}")
#     for path in to_move:
#         dest = os.path.join(duplicate_folder, os.path.basename(path))
#         if os.path.exists(path):
#             try:
#                 print(f"Moving: {path} -> {dest}")
#                 shutil.move(path, dest)
#             except Exception as e:
#                 print(f"[ERROR] Could not move {path}: {e}")
#         else:
#             print(f"[SKIP] File already missing or moved: {path}")




#For running standalone script
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Detect and remove duplicate/text-heavy images.")
    parser.add_argument("-i", "--input", required=True, help="Input folder of images")
    parser.add_argument("-o", "--output", default="removed", help="Output folder for duplicates")
    args = parser.parse_args()

    deduplicate_images(args.input, args.output)
