import os
import time
import base64
# Assuming Chrome_Driver.py is in the same directory or accessible via PYTHONPATH
from Chrome_Driver import get_driver # type: ignore 

def take_amiunique_fingerprint_screenshot(
    url="https://amiunique.org/fingerprint",
    output_folder="fingerprint",
    output_filename=None):
    """
    Navigates to the specified URL, scrolls to the bottom of the page,
    and takes a viewport screenshot after scrolling, saving it to the specified folder.
    Note: This will capture what's visible after scrolling, not necessarily the entire scrollable page.

    Args:
        url (str): The URL to navigate to.
        output_folder (str): The name of the subfolder to save the screenshot in.
                             This folder will be created in the current working directory.
        output_filename (str, optional): The desired filename for the screenshot.
                                         If None, a timestamped filename will be generated.
    """
    print(f"Attempting to navigate to: {url} for viewport screenshot after scrolling.")

    # Ensure the output folder exists
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"Created output folder: ./{output_folder}")

    driver = None

    try:
        print("Initializing WebDriver for AmiUnique.org...")
        driver = get_driver()
        print("WebDriver initialized for AmiUnique.org.")

        driver.get(url)
        print(f"Successfully navigated to {url}")
        print("Waiting for page to load initial content (5 seconds)...")
        time.sleep(5)  # Allow time for initial JavaScript execution and content rendering

        print("Scrolling down the page to ensure all content is loaded...")
        last_height = driver.execute_script("return document.body.scrollHeight")
        scroll_attempts = 0
        max_scroll_attempts = 15 # Prevent infinite loops on very dynamic pages

        while scroll_attempts < max_scroll_attempts:
            timestamp = time.strftime("%Y%m%d-%H%M%S")
            output_filename = f"amiunique_{timestamp}.png"
            full_output_path = os.path.join(output_folder, output_filename)
            print(f"Taking viewport screenshot of {url} after scrolling...")
            driver.save_screenshot(full_output_path)
            print(f"Viewport screenshot (after scroll) successfully saved to: {os.path.abspath(full_output_path)}")
            time.sleep(1)
            
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            print(f"Scrolled down. Waiting for new content (2 seconds)...")
            time.sleep(2)  # Wait for lazy-loaded content
            new_height = driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                print("Reached the bottom of the page.")
                break
            last_height = new_height
            scroll_attempts += 1
            print(f"Current page height: {new_height}")
            time.sleep(1)
        
        
    except Exception as e:
        print(f"An error occurred while processing {url}: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

    finally:
        if driver:
            print("Closing WebDriver for AmiUnique.org...")
            driver.quit()
            print("WebDriver for AmiUnique.org closed.")

def take_browserleaks_canvas_screenshot(
    url="https://browserleaks.com/canvas",
    output_folder="fingerprint",
    output_filename="browserleaks_canvas_screenshot.png"
):
    """
    Navigates to the specified URL and takes a standard viewport screenshot,
    saving it to the specified folder.

    Args:
        url (str): The URL to navigate to.
        output_folder (str): The name of the subfolder to save the screenshot in.
                             This folder will be created in the current working directory.
        output_filename (str): The desired filename for the screenshot.
    """
    print(f"Attempting to navigate to: {url} for viewport screenshot.")

    # Ensure the output folder exists
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"Created output folder: ./{output_folder}")

    driver = None

    try:
        print("Initializing WebDriver for BrowserLeaks.com...")
        driver = get_driver()
        print("WebDriver initialized for BrowserLeaks.com.")

        driver.get(url)
        print(f"Successfully navigated to {url}")
        
        # The canvas test might take a few seconds to generate its signature
        print("Waiting for page content and canvas rendering (10 seconds)...")
        time.sleep(10)

        print(f"Taking viewport screenshot of {url}...")
        timestamp = time.strftime("%Y%m%d-%H%M%S")
        output_filename = f"browserleaks_canvas_{timestamp}.png"
        driver.save_screenshot(os.path.join(output_folder, output_filename))
        print(f"Viewport screenshot successfully saved to: {os.path.abspath(os.path.join(output_folder, output_filename))}")

    except Exception as e:
        print(f"An error occurred while processing {url}: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
    finally:
        if driver:
            print("Closing WebDriver for BrowserLeaks.com...")
            driver.quit()
            print("WebDriver for BrowserLeaks.com closed.")


if __name__ == "__main__":
    # Ensure the main output folder exists before any function tries to create it
    # This is a small redundancy but can be helpful if functions are called in parallel later
    # or if one function fails before creating the directory.
    main_output_folder = "fingerprint"
    if not os.path.exists(main_output_folder):
        os.makedirs(main_output_folder)
        print(f"Ensured main output folder exists: ./{main_output_folder}")

    take_amiunique_fingerprint_screenshot()
    print("-" * 50) # Separator for clarity in logs
    take_browserleaks_canvas_screenshot()
