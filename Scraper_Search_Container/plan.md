1. user folder: see if logging into google is persistent
2. more pluging/mine types
3. canvas and webgl image
4. selenium-driverless: **This is an excellent alternative. It works by connecting to an** **already running** **Chrome instance (which you could launch manually or with more "natural" flags, potentially outside of direct WebDriver control for the launch phase). This can bypass many ChromeDriver-specific detections. It's worth trying if the above doesn't work.**
5. consider Xvfb
