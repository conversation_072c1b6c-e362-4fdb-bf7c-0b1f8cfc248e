{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9959bbd8", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "pd.set_option('display.max_columns', None)"]}, {"cell_type": "code", "execution_count": 25, "id": "d1a8deb4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Retrieved connection from pool lazy_pool_175.178.236.180_javaapi_trodatadb_3306 in 0.4 seconds\n", "Fetching 'tb_case' from GZ:  | 1k (11.7s) | 2k (7.8s) | 3k (3.8s) | 4k (1.9s) | 5k (1.4s) | 6k (5.3s) | 7k (4.0s) | 8k (3.3s) | 8k (0.4s) | DONE (40.91s)\n"]}], "source": ["from DatabaseManagement.ImportExport import get_table_from_GZ\n", "\n", "\n", "all_cases_df = get_table_from_GZ(\"tb_case\", force_refresh=True)"]}, {"cell_type": "code", "execution_count": 26, "id": "694a7d3f", "metadata": {}, "outputs": [{"data": {"text/plain": ["Index(['id', 'plaintiff_id', 'plaintiff_names', 'plaintiff_lawyers',\n", "       'defendant_names', 'defendant_lawyers', 'docket', 'court',\n", "       'nature_of_suit', 'nos_description', 'class_code', 'date_filed',\n", "       'title', 'assigned_to', 'cause', 'statute', 'images', 'images_status',\n", "       'validation_status', 'aisummary', 'file_status', 'demand_amount',\n", "       'date_checked', 'date_updated', 'closed', 'status', 'creator',\n", "       'create_time', 'updater', 'update_time', 'deleted', 'tenant_id',\n", "       'ln_url'],\n", "      dtype='object')"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["all_cases_df.columns"]}, {"cell_type": "code", "execution_count": 30, "id": "46584fcb", "metadata": {}, "outputs": [{"data": {"application/vnd.microsoft.datawrangler.viewer.v0+json": {"columns": [{"name": "index", "rawType": "int64", "type": "integer"}, {"name": "id", "rawType": "int64", "type": "integer"}, {"name": "plaintiff_id", "rawType": "float64", "type": "float"}, {"name": "plaintiff_names", "rawType": "object", "type": "string"}, {"name": "plaintiff_lawyers", "rawType": "object", "type": "string"}, {"name": "defendant_names", "rawType": "object", "type": "string"}, {"name": "defendant_lawyers", "rawType": "object", "type": "string"}, {"name": "docket", "rawType": "object", "type": "string"}, {"name": "court", "rawType": "object", "type": "string"}, {"name": "nature_of_suit", "rawType": "object", "type": "string"}, {"name": "nos_description", "rawType": "object", "type": "string"}, {"name": "class_code", "rawType": "object", "type": "string"}, {"name": "date_filed", "rawType": "object", "type": "string"}, {"name": "title", "rawType": "object", "type": "string"}, {"name": "assigned_to", "rawType": "object", "type": "string"}, {"name": "cause", "rawType": "object", "type": "string"}, {"name": "statute", "rawType": "object", "type": "string"}, {"name": "images", "rawType": "object", "type": "string"}, {"name": "images_status", "rawType": "object", "type": "string"}, {"name": "validation_status", "rawType": "object", "type": "string"}, {"name": "aisummar<PERSON>", "rawType": "object", "type": "string"}, {"name": "file_status", "rawType": "object", "type": "string"}, {"name": "demand_amount", "rawType": "object", "type": "string"}, {"name": "date_checked", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "date_updated", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "closed", "rawType": "object", "type": "string"}, {"name": "status", "rawType": "int64", "type": "integer"}, {"name": "creator", "rawType": "object", "type": "string"}, {"name": "create_time", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "updater", "rawType": "object", "type": "string"}, {"name": "update_time", "rawType": "datetime64[ns]", "type": "datetime"}, {"name": "deleted", "rawType": "int64", "type": "integer"}, {"name": "tenant_id", "rawType": "int64", "type": "integer"}, {"name": "ln_url", "rawType": "object", "type": "string"}], "ref": "896da0cb-f617-4f44-b67b-8a776363c1b1", "rows": [], "shape": {"columns": 33, "rows": 0}}, "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>plaintiff_id</th>\n", "      <th>plaintiff_names</th>\n", "      <th>plaintiff_lawyers</th>\n", "      <th>defendant_names</th>\n", "      <th>defendant_lawyers</th>\n", "      <th>docket</th>\n", "      <th>court</th>\n", "      <th>nature_of_suit</th>\n", "      <th>nos_description</th>\n", "      <th>class_code</th>\n", "      <th>date_filed</th>\n", "      <th>title</th>\n", "      <th>assigned_to</th>\n", "      <th>cause</th>\n", "      <th>statute</th>\n", "      <th>images</th>\n", "      <th>images_status</th>\n", "      <th>validation_status</th>\n", "      <th>aisummary</th>\n", "      <th>file_status</th>\n", "      <th>demand_amount</th>\n", "      <th>date_checked</th>\n", "      <th>date_updated</th>\n", "      <th>closed</th>\n", "      <th>status</th>\n", "      <th>creator</th>\n", "      <th>create_time</th>\n", "      <th>updater</th>\n", "      <th>update_time</th>\n", "      <th>deleted</th>\n", "      <th>tenant_id</th>\n", "      <th>ln_url</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [id, plaintiff_id, plaintiff_names, plaintiff_lawyers, defendant_names, defendant_lawyers, docket, court, nature_of_suit, nos_description, class_code, date_filed, title, assigned_to, cause, statute, images, images_status, validation_status, aisummary, file_status, demand_amount, date_checked, date_updated, closed, status, creator, create_time, updater, update_time, deleted, tenant_id, ln_url]\n", "Index: []"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["all_cases_df[all_cases_df['id'].isin([14415])]"]}, {"cell_type": "code", "execution_count": 24, "id": "77668070", "metadata": {}, "outputs": [{"data": {"text/plain": ["8113"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["len(all_cases_df)"]}, {"cell_type": "code", "execution_count": 19, "id": "bea4a113", "metadata": {}, "outputs": [], "source": ["ids_to_remove = [14416, 14425, 14422, 14420, 14419, 14415, 14421, 14423, 14418, 14417, 14424, 14426]\n", "all_cases_df = all_cases_df[~all_cases_df['id'].isin(ids_to_remove)]"]}, {"cell_type": "code", "execution_count": 20, "id": "9c30dc81", "metadata": {}, "outputs": [{"data": {"text/plain": ["8101"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["len(all_cases_df)"]}, {"cell_type": "code", "execution_count": 4, "id": "f83fe640", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>plaintiff_id</th>\n", "      <th>plaintiff_names</th>\n", "      <th>plaintiff_lawyers</th>\n", "      <th>defendant_names</th>\n", "      <th>defendant_lawyers</th>\n", "      <th>docket</th>\n", "      <th>court</th>\n", "      <th>nature_of_suit</th>\n", "      <th>nos_description</th>\n", "      <th>class_code</th>\n", "      <th>date_filed</th>\n", "      <th>title</th>\n", "      <th>assigned_to</th>\n", "      <th>cause</th>\n", "      <th>statute</th>\n", "      <th>images</th>\n", "      <th>images_status</th>\n", "      <th>validation_status</th>\n", "      <th>aisummary</th>\n", "      <th>file_status</th>\n", "      <th>demand_amount</th>\n", "      <th>date_checked</th>\n", "      <th>date_updated</th>\n", "      <th>closed</th>\n", "      <th>status</th>\n", "      <th>creator</th>\n", "      <th>create_time</th>\n", "      <th>updater</th>\n", "      <th>update_time</th>\n", "      <th>deleted</th>\n", "      <th>tenant_id</th>\n", "      <th>ln_url</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>33.0</td>\n", "      <td>[\"<PERSON>\"]</td>\n", "      <td>[\"Jiangip Llc\"]</td>\n", "      <td>[\"The Partnerships and Unincorporated Associat...</td>\n", "      <td>[\"\", \"\", \"\"]</td>\n", "      <td>1:24-cv-08824</td>\n", "      <td>Illinois Northern District Court</td>\n", "      <td>Copyrights (820)</td>\n", "      <td>Copyrights</td>\n", "      <td>Closed</td>\n", "      <td>2024-09-24</td>\n", "      <td><PERSON>. The Partnerships And Uninco...</td>\n", "      <td>Honorable <PERSON></td>\n", "      <td>Copyright Infringement</td>\n", "      <td>17:101</td>\n", "      <td>{'trademarks': {}, 'patents': {}, 'copyrights'...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>{\\n\"English\": \"The complaint is a copyright in...</td>\n", "      <td>1+free</td>\n", "      <td>$0</td>\n", "      <td>2024-11-19 15:02:52</td>\n", "      <td>2024-11-05</td>\n", "      <td>2024-10-25</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>2024-10-19 01:03:24</td>\n", "      <td></td>\n", "      <td>2025-03-28 07:22:13</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   id  plaintiff_id      plaintiff_names plaintiff_lawyers  \\\n", "0   1          33.0  [\"<PERSON>\"]   [\"Jiangip Llc\"]   \n", "\n", "                                     defendant_names defendant_lawyers  \\\n", "0  [\"The Partnerships and Unincorporated Associat...      [\"\", \"\", \"\"]   \n", "\n", "          docket                             court    nature_of_suit  \\\n", "0  1:24-cv-08824  Illinois Northern District Court  Copyrights (820)   \n", "\n", "  nos_description class_code  date_filed  \\\n", "0      Copyrights     Closed  2024-09-24   \n", "\n", "                                               title  \\\n", "0  <PERSON> V. The Partnerships And Uninco...   \n", "\n", "                 assigned_to                   cause statute  \\\n", "0  Honorable <PERSON>  Copyright Infringement  17:101   \n", "\n", "                                              images images_status  \\\n", "0  {'trademarks': {}, 'patents': {}, 'copyrights'...          None   \n", "\n", "  validation_status                                          aisummary  \\\n", "0              None  {\\n\"English\": \"The complaint is a copyright in...   \n", "\n", "  file_status demand_amount        date_checked date_updated      closed  \\\n", "0      1+free            $0 2024-11-19 15:02:52   2024-11-05  2024-10-25   \n", "\n", "   status creator         create_time updater         update_time  deleted  \\\n", "0       0         2024-10-19 01:03:24         2025-03-28 07:22:13        0   \n", "\n", "   tenant_id ln_url  \n", "0          0   None  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["all_cases_df.head(1)"]}, {"cell_type": "code", "execution_count": 5, "id": "4fe94a43", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>plaintiff_id</th>\n", "      <th>plaintiff_names</th>\n", "      <th>plaintiff_lawyers</th>\n", "      <th>defendant_names</th>\n", "      <th>defendant_lawyers</th>\n", "      <th>docket</th>\n", "      <th>court</th>\n", "      <th>nature_of_suit</th>\n", "      <th>nos_description</th>\n", "      <th>class_code</th>\n", "      <th>date_filed</th>\n", "      <th>title</th>\n", "      <th>assigned_to</th>\n", "      <th>cause</th>\n", "      <th>statute</th>\n", "      <th>images</th>\n", "      <th>images_status</th>\n", "      <th>validation_status</th>\n", "      <th>aisummary</th>\n", "      <th>file_status</th>\n", "      <th>demand_amount</th>\n", "      <th>date_checked</th>\n", "      <th>date_updated</th>\n", "      <th>closed</th>\n", "      <th>status</th>\n", "      <th>creator</th>\n", "      <th>create_time</th>\n", "      <th>updater</th>\n", "      <th>update_time</th>\n", "      <th>deleted</th>\n", "      <th>tenant_id</th>\n", "      <th>ln_url</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>8075</th>\n", "      <td>14389</td>\n", "      <td>2251.0</td>\n", "      <td>[\"PR Trading Comercio &amp; Exportacao LTDA\", \"Joa...</td>\n", "      <td>[\"<PERSON><PERSON><PERSON> <PERSON>\", \"Lippes...</td>\n", "      <td>[\"Vitoria Meat Market, Inc.\", \"Commodity Conne...</td>\n", "      <td>[\"\", \"\", \"\", \"\", \"\"]</td>\n", "      <td>1:25-cv-11899</td>\n", "      <td>Massachusetts District Court</td>\n", "      <td>Defend Trade Secrets Act (of 2016) (880)</td>\n", "      <td>Defend Trade Secrets Act (of 2016)</td>\n", "      <td>Open</td>\n", "      <td>2025-07-03</td>\n", "      <td>Pr Trading Comercio &amp; Exportacao Ltda Et Al V....</td>\n", "      <td>None</td>\n", "      <td>Civil Action to Protect Trade Secrets</td>\n", "      <td>18:1836(b)</td>\n", "      <td>{'trademarks': {}, 'patents': {}, 'copyrights'...</td>\n", "      <td>{\"ip_manager_state\": {}, \"steps_processed\": []...</td>\n", "      <td>None</td>\n", "      <td>{\\n  \"English\": \"This is a federal lawsuit fil...</td>\n", "      <td>IP Goals Met</td>\n", "      <td>$0</td>\n", "      <td>2025-07-04 15:04:36</td>\n", "      <td>2025-07-03</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>2025-07-04 12:33:01</td>\n", "      <td></td>\n", "      <td>2025-07-04 23:04:44</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>https://advance.lexis.com/api/permalink/09a5f0...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         id  plaintiff_id                                    plaintiff_names  \\\n", "8075  14389        2251.0  [\"PR Trading Comercio & Exportacao LTDA\", \"Joa...   \n", "\n", "                                      plaintiff_lawyers  \\\n", "8075  [\"<PERSON><PERSON><PERSON>\", \"Lippes...   \n", "\n", "                                        defendant_names     defendant_lawyers  \\\n", "8075  [\"Vitoria Meat Market, Inc.\", \"Commodity Conne...  [\"\", \"\", \"\", \"\", \"\"]   \n", "\n", "             docket                         court  \\\n", "8075  1:25-cv-11899  Massachusetts District Court   \n", "\n", "                                nature_of_suit  \\\n", "8075  Defend Trade Secrets Act (of 2016) (880)   \n", "\n", "                         nos_description class_code  date_filed  \\\n", "8075  Defend Trade Secrets Act (of 2016)       Open  2025-07-03   \n", "\n", "                                                  title assigned_to  \\\n", "8075  Pr Trading Comercio & Exportacao Ltda Et Al V....        None   \n", "\n", "                                      cause     statute  \\\n", "8075  Civil Action to Protect Trade Secrets  18:1836(b)   \n", "\n", "                                                 images  \\\n", "8075  {'trademarks': {}, 'patents': {}, 'copyrights'...   \n", "\n", "                                          images_status validation_status  \\\n", "8075  {\"ip_manager_state\": {}, \"steps_processed\": []...              None   \n", "\n", "                                              aisummary   file_status  \\\n", "8075  {\\n  \"English\": \"This is a federal lawsuit fil...  IP Goals Met   \n", "\n", "     demand_amount        date_checked date_updated closed  status creator  \\\n", "8075            $0 2025-07-04 15:04:36   2025-07-03   None       0           \n", "\n", "             create_time updater         update_time  deleted  tenant_id  \\\n", "8075 2025-07-04 12:33:01         2025-07-04 23:04:44        0          0   \n", "\n", "                                                 ln_url  \n", "8075  https://advance.lexis.com/api/permalink/09a5f0...  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["all_cases_df[all_cases_df['docket'] == '1:25-cv-11899']"]}, {"cell_type": "code", "execution_count": 31, "id": "a9bc4370", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>plaintiff_id</th>\n", "      <th>plaintiff_names</th>\n", "      <th>plaintiff_lawyers</th>\n", "      <th>defendant_names</th>\n", "      <th>defendant_lawyers</th>\n", "      <th>docket</th>\n", "      <th>court</th>\n", "      <th>nature_of_suit</th>\n", "      <th>nos_description</th>\n", "      <th>class_code</th>\n", "      <th>date_filed</th>\n", "      <th>title</th>\n", "      <th>assigned_to</th>\n", "      <th>cause</th>\n", "      <th>statute</th>\n", "      <th>images</th>\n", "      <th>images_status</th>\n", "      <th>validation_status</th>\n", "      <th>aisummary</th>\n", "      <th>file_status</th>\n", "      <th>demand_amount</th>\n", "      <th>date_checked</th>\n", "      <th>date_updated</th>\n", "      <th>closed</th>\n", "      <th>status</th>\n", "      <th>creator</th>\n", "      <th>create_time</th>\n", "      <th>updater</th>\n", "      <th>update_time</th>\n", "      <th>deleted</th>\n", "      <th>tenant_id</th>\n", "      <th>ln_url</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>8075</th>\n", "      <td>14389</td>\n", "      <td>2251.0</td>\n", "      <td>[\"PR Trading Comercio &amp; Exportacao LTDA\", \"Joa...</td>\n", "      <td>[\"<PERSON><PERSON><PERSON> <PERSON>\", \"Lippes...</td>\n", "      <td>[\"Vitoria Meat Market, Inc.\", \"Commodity Conne...</td>\n", "      <td>[\"\", \"\", \"\", \"\", \"\"]</td>\n", "      <td>1:25-cv-11899</td>\n", "      <td>Massachusetts District Court</td>\n", "      <td>Defend Trade Secrets Act (of 2016) (880)</td>\n", "      <td>Defend Trade Secrets Act (of 2016)</td>\n", "      <td>Open</td>\n", "      <td>2025-07-03</td>\n", "      <td>Pr Trading Comercio &amp; Exportacao Ltda Et Al V....</td>\n", "      <td>None</td>\n", "      <td>Civil Action to Protect Trade Secrets</td>\n", "      <td>18:1836(b)</td>\n", "      <td>{'trademarks': {}, 'patents': {}, 'copyrights'...</td>\n", "      <td>{\"ip_manager_state\": {}, \"steps_processed\": []...</td>\n", "      <td>None</td>\n", "      <td>{\\n  \"English\": \"This is a federal lawsuit fil...</td>\n", "      <td>IP Goals Met</td>\n", "      <td>$0</td>\n", "      <td>2025-07-04 04:59:39</td>\n", "      <td>2025-07-03</td>\n", "      <td>None</td>\n", "      <td>0</td>\n", "      <td></td>\n", "      <td>2025-07-04 12:33:01</td>\n", "      <td></td>\n", "      <td>2025-07-04 12:59:51</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>https://advance.lexis.com/api/permalink/09a5f0...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         id  plaintiff_id                                    plaintiff_names  \\\n", "8075  14389        2251.0  [\"PR Trading Comercio & Exportacao LTDA\", \"Joa...   \n", "\n", "                                      plaintiff_lawyers  \\\n", "8075  [\"<PERSON><PERSON><PERSON>\", \"Lippes...   \n", "\n", "                                        defendant_names     defendant_lawyers  \\\n", "8075  [\"Vitoria Meat Market, Inc.\", \"Commodity Conne...  [\"\", \"\", \"\", \"\", \"\"]   \n", "\n", "             docket                         court  \\\n", "8075  1:25-cv-11899  Massachusetts District Court   \n", "\n", "                                nature_of_suit  \\\n", "8075  Defend Trade Secrets Act (of 2016) (880)   \n", "\n", "                         nos_description class_code  date_filed  \\\n", "8075  Defend Trade Secrets Act (of 2016)       Open  2025-07-03   \n", "\n", "                                                  title assigned_to  \\\n", "8075  Pr Trading Comercio & Exportacao Ltda Et Al V....        None   \n", "\n", "                                      cause     statute  \\\n", "8075  Civil Action to Protect Trade Secrets  18:1836(b)   \n", "\n", "                                                 images  \\\n", "8075  {'trademarks': {}, 'patents': {}, 'copyrights'...   \n", "\n", "                                          images_status validation_status  \\\n", "8075  {\"ip_manager_state\": {}, \"steps_processed\": []...              None   \n", "\n", "                                              aisummary   file_status  \\\n", "8075  {\\n  \"English\": \"This is a federal lawsuit fil...  IP Goals Met   \n", "\n", "     demand_amount        date_checked date_updated closed  status creator  \\\n", "8075            $0 2025-07-04 04:59:39   2025-07-03   None       0           \n", "\n", "             create_time updater         update_time  deleted  tenant_id  \\\n", "8075 2025-07-04 12:33:01         2025-07-04 12:59:51        0          0   \n", "\n", "                                                 ln_url  \n", "8075  https://advance.lexis.com/api/permalink/09a5f0...  "]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["fault_case = all_cases_df[all_cases_df['docket'] == '1:25-cv-11899']\n", "fault_case"]}, {"cell_type": "code", "execution_count": 33, "id": "afe2c4b6", "metadata": {}, "outputs": [{"data": {"text/plain": ["'{\"ip_manager_state\": {}, \"steps_processed\": [], \"number_of_pdfs\": 0, \"trace_url\": \"https://langfuse.sergedc.com/project/cm65gk8n600073r6amjcsjqu4/traces/233492ffe903b6a72cb009d228bdb042\", \"copyright_status\": {\"exhibit\": {\"count\": 0, \"steps\": []}, \"byregno\": {\"ai_reg_nos\": [], \"count\": 0}, \"cn_website\": {\"cn_reg_nos\": [], \"count\": 0, \"sources\": {}}, \"bygoogle\": {\"count\": 0, \"search_term\": \"\"}, \"manual\": {\"count\": 0, \"comment\": \"\"}}, \"trademark_status\": {\"exhibit\": {\"count\": 0, \"steps\": [], \"sources\": {}}, \"byregno\": {\"ai_reg_nos\": [], \"count\": 0, \"steps\": [], \"sources\": {}}, \"cn_website\": {\"cn_reg_nos\": [], \"count\": 0, \"sources\": {}}, \"byname\": {\"count\": 0, \"search_term_used\": \"\", \"sources\": {}}, \"manual\": {\"count\": 0, \"comment\": \"\"}}, \"patent_status\": {\"exhibit\": {\"count\": 0, \"steps\": []}, \"byregno\": {\"ai_reg_nos\": [], \"count\": 0, \"steps\": []}, \"cn_website\": {\"cn_reg_nos\": [], \"count\": 0}, \"byname\": {\"count\": 0, \"search_term_used\": \"\", \"ai_search_term\": \"\"}, \"manual\": {\"count\": 0, \"comment\": \"\"}}}'"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["(all_cases_df.at[8075, 'images_status'] or {})"]}, {"cell_type": "code", "execution_count": 23, "id": "249bd48d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{}\n"]}], "source": ["import json\n", "\n", "\n", "print(json.loads((all_cases_df.at[8075, 'images_status'] or {})).get('ip_manager_state', {}))\n"]}, {"cell_type": "code", "execution_count": 30, "id": "da52aa30", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'ip_manager_state': {'trademark': {'is_relevant': True,\n", "   'goal_met': True,\n", "   'reg_nos_status': 'WholeSet',\n", "   'target_reg_nos': ['1073671',\n", "    '1075059',\n", "    '1100741',\n", "    '1312269',\n", "    '1314299',\n", "    '1346436',\n", "    '1390935',\n", "    '2276097',\n", "    '2316903',\n", "    '2772224',\n", "    '2854465',\n", "    '4354512',\n", "    '4419214',\n", "    '4515131',\n", "    '4522437',\n", "    '4522438',\n", "    '4592051',\n", "    '4592052',\n", "    '4592054',\n", "    '4596147',\n", "    '4605902',\n", "    '4846779',\n", "    '4918817',\n", "    '5055079',\n", "    '5059956',\n", "    '5129446',\n", "    '5129447',\n", "    '5166771',\n", "    '5176741',\n", "    '5276717',\n", "    '5428676',\n", "    '5428677',\n", "    '5546145',\n", "    '5697522',\n", "    '6069963',\n", "    '6169993',\n", "    '6294707',\n", "    '6294711',\n", "    '6456114',\n", "    '6731983',\n", "    '6731984',\n", "    '6731985',\n", "    '6791161',\n", "    '7139772',\n", "    '7426341',\n", "    '7762741'],\n", "   'found_reg_nos': ['1073671',\n", "    '1075059',\n", "    '1100741',\n", "    '1312269',\n", "    '1314299',\n", "    '1346436',\n", "    '1390935',\n", "    '2276097',\n", "    '2316903',\n", "    '2772224',\n", "    '2854465',\n", "    '4354512',\n", "    '4419214',\n", "    '4515131',\n", "    '4522437',\n", "    '4522438',\n", "    '4592051',\n", "    '4592052',\n", "    '4592054',\n", "    '4596147',\n", "    '4605902',\n", "    '4846779',\n", "    '4918817',\n", "    '5055079',\n", "    '5059956',\n", "    '5129446',\n", "    '5129447',\n", "    '5166771',\n", "    '5176741',\n", "    '5276717',\n", "    '5428676',\n", "    '5428677',\n", "    '5546145',\n", "    '5697522',\n", "    '6069963',\n", "    '6169993',\n", "    '6294707',\n", "    '6294711',\n", "    '6456114',\n", "    '6731983',\n", "    '6731984',\n", "    '6731985',\n", "    '6791161',\n", "    '7139772',\n", "    '7426341',\n", "    '7762741']},\n", "  'copyright': {'is_relevant': True,\n", "   'goal_met': True,\n", "   'reg_nos_status': 'WholeSet',\n", "   'non_target_reg_nos': ['PA0001685728',\n", "    'PA0001859348',\n", "    'PA0001952355',\n", "    'PA0002043544',\n", "    'PA0002369070',\n", "    'PA0002478457']}},\n", " 'steps_processed': ['1.00'],\n", " 'number_of_pdfs': 1,\n", " 'trace_url': 'https://langfuse.sergedc.com/project/cm65gk8n600073r6amjcsjqu4/traces/11c843158eb3c86b5824b4d66355e8e5',\n", " 'copyright_status': {'exhibit': {'count': 0, 'steps': []},\n", "  'byregno': {'ai_reg_nos': [], 'count': 0},\n", "  'cn_website': {'cn_reg_nos': [], 'count': 0, 'sources': {}},\n", "  'bygoogle': {'count': 0, 'search_term': ''},\n", "  'manual': {'count': 0, 'comment': ''}},\n", " 'trademark_status': {'exhibit': {'count': 0, 'steps': [], 'sources': {}},\n", "  'byregno': {'ai_reg_nos': ['5129447',\n", "    '5129446',\n", "    '6731984',\n", "    '4592052',\n", "    '1314299',\n", "    '4515131',\n", "    '1073671',\n", "    '1100741',\n", "    '5166771',\n", "    '2854465',\n", "    '4846779',\n", "    '5697522',\n", "    '6791161',\n", "    '5176741',\n", "    '4605902',\n", "    '6294707',\n", "    '4522438',\n", "    '6456114',\n", "    '6731985',\n", "    '4592054',\n", "    '5428677',\n", "    '5428676',\n", "    '5276717',\n", "    '5059956',\n", "    '1075059',\n", "    '4354512',\n", "    '7139772',\n", "    '7426341',\n", "    '1312269',\n", "    '1346436',\n", "    '5055079',\n", "    '4419214',\n", "    '4522437',\n", "    '4918817',\n", "    '5546145',\n", "    '6294711',\n", "    '7762741',\n", "    '6169993',\n", "    '4596147',\n", "    '2772224',\n", "    '6069963',\n", "    '4592051',\n", "    '6731983',\n", "    '1390935',\n", "    '2276097',\n", "    '2316903'],\n", "   'count': 46,\n", "   'steps': [],\n", "   'sources': {'USPTO_URL': 46}},\n", "  'cn_website': {'cn_reg_nos': [], 'count': 0, 'sources': {}},\n", "  'byname': {'count': 0, 'search_term_used': '', 'sources': {}},\n", "  'manual': {'count': 0, 'comment': ''}},\n", " 'patent_status': {'exhibit': {'count': 0, 'steps': []},\n", "  'byregno': {'ai_reg_nos': [], 'count': 0, 'steps': []},\n", "  'cn_website': {'cn_reg_nos': [], 'count': 0},\n", "  'byname': {'count': 0, 'search_term_used': '', 'ai_search_term': ''},\n", "  'manual': {'count': 0, 'comment': ''}}}"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["json.loads((all_cases_df.at[8073, 'images_status'] or {}))"]}, {"cell_type": "code", "execution_count": 2, "id": "98885f78", "metadata": {}, "outputs": [], "source": ["from datetime import date, timedelta\n", "import calendar"]}, {"cell_type": "code", "execution_count": 3, "id": "dee2713f", "metadata": {}, "outputs": [], "source": ["def get_date_range_for_period(period_type):\n", "    \"\"\"\n", "    Calculate date ranges for different periodic fetch types.\n", "    \n", "    Args:\n", "        period_type: 'weekly', 'monthly_1', 'monthly_2', 'monthly_3'\n", "    \n", "    Returns:\n", "        tuple: (start_date, end_date, description)\n", "    \"\"\"\n", "    today = date.today()\n", "    \n", "    if period_type == 'weekly':\n", "        # Last week (7 days ago to today)\n", "        start_date = today - <PERSON><PERSON><PERSON>(days=7)\n", "        end_date = today\n", "        description = \"last week\"\n", "        \n", "    elif period_type == 'monthly_1':\n", "        # Last month (previous month)\n", "        if today.month == 1:\n", "            # If current month is January, last month is December of previous year\n", "            last_month = 12\n", "            year = today.year - 1\n", "        else:\n", "            last_month = today.month - 1\n", "            year = today.year\n", "        \n", "        # First day of last month\n", "        start_date = date(year, last_month, 1)\n", "        # Last day of last month\n", "        last_day = calendar.monthrange(year, last_month)[1]\n", "        end_date = date(year, last_month, last_day)\n", "        description = f\"{calendar.month_name[last_month]} {year}\"\n", "        \n", "    elif period_type == 'monthly_2':\n", "        # 2 months ago\n", "        if today.month <= 2:\n", "            target_month = today.month + 10  # 12 - (2 - month)\n", "            year = today.year - 1\n", "        else:\n", "            target_month = today.month - 2\n", "            year = today.year\n", "        \n", "        start_date = date(year, target_month, 1)\n", "        last_day = calendar.monthrange(year, target_month)[1]\n", "        end_date = date(year, target_month, last_day)\n", "        description = f\"{calendar.month_name[target_month]} {year}\"\n", "        \n", "    elif period_type == 'monthly_3':\n", "        # 3 months ago\n", "        if today.month <= 3:\n", "            target_month = today.month + 9  # 12 - (3 - month)\n", "            year = today.year - 1\n", "        else:\n", "            target_month = today.month - 3\n", "            year = today.year\n", "        \n", "        start_date = date(year, target_month, 1)\n", "        last_day = calendar.monthrange(year, target_month)[1]\n", "        end_date = date(year, target_month, last_day)\n", "        description = f\"{calendar.month_name[target_month]} {year}\"\n", "        \n", "    else:\n", "        raise ValueError(f\"Unknown period_type: {period_type}\")\n", "    \n", "    return start_date, end_date, description"]}, {"cell_type": "code", "execution_count": 8, "id": "3cdb349e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Today: 2025-07-05\n"]}], "source": ["today = date.today()\n", "print(f\"Today: {date.today()}\")"]}, {"cell_type": "code", "execution_count": 12, "id": "51713ec4", "metadata": {}, "outputs": [], "source": ["last_month = today.month - 1\n", "year = today.year\n", "start_date = date(year, last_month, 1)"]}, {"cell_type": "code", "execution_count": 19, "id": "f7ff962c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'June'"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["calendar.month_name[last_month]"]}, {"cell_type": "code", "execution_count": 5, "id": "e49a25a1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["weekly      : last week                 (2025-06-28 to 2025-07-05)\n", "monthly_1   : June 2025                 (2025-06-01 to 2025-06-30)\n", "monthly_2   : May 2025                  (2025-05-01 to 2025-05-31)\n", "monthly_3   : April 2025                (2025-04-01 to 2025-04-30)\n"]}], "source": ["periods = ['weekly', 'monthly_1', 'monthly_2', 'monthly_3']\n", "\n", "for period in periods:\n", "    try:\n", "        start_date, end_date, description = get_date_range_for_period(period)\n", "        print(f\"{period:12}: {description:25} ({start_date} to {end_date})\")\n", "    except Exception as e:\n", "        print(f\"{period:12}: ERROR - {e}\")"]}, {"cell_type": "code", "execution_count": 1, "id": "5b0a8f28", "metadata": {}, "outputs": [], "source": ["from datetime import date, timedelta\n", "from dateutil.relativedelta import relativedelta\n", "\n", "def get_month_range(months_ago):\n", "    today = date.today()\n", "    target_date = today - relativedelta(months=months_ago)\n", "    start_date = target_date.replace(day=1)\n", "    end_date = (start_date + relativedelta(months=1)) - <PERSON><PERSON><PERSON>(days=1)\n", "    description = f\"{start_date.strftime('%B %Y')}\"\n", "    return start_date, end_date, description\n", "\n", "def get_date_range_for_period(period_type):\n", "    today = date.today()\n", "\n", "    if period_type == 'weekly':\n", "        start_date = today - <PERSON><PERSON><PERSON>(days=7)\n", "        end_date = today\n", "        description = \"last week\"\n", "\n", "    elif period_type == 'monthly_1':\n", "        start_date, end_date, description = get_month_range(1)\n", "\n", "    elif period_type == 'monthly_2':\n", "        start_date, end_date, description = get_month_range(2)\n", "\n", "    elif period_type == 'monthly_3':\n", "        start_date, end_date, description = get_month_range(3)\n", "\n", "    elif period_type == 'monthly_4':\n", "        start_date = today - relativedel<PERSON>(months=18)\n", "        end_date = today - relativedel<PERSON>(months=3)\n", "        description = \"open cases 3-18 months old\"\n", "\n", "    else:\n", "        raise ValueError(f\"Unknown period_type: {period_type}\")\n", "\n", "    return start_date, end_date, description\n"]}, {"cell_type": "code", "execution_count": 13, "id": "9e2dc06d", "metadata": {}, "outputs": [], "source": ["def get_month_range(months_ago):\n", "    today = date.today()\n", "    target_date = today - relativedelta(months=months_ago)\n", "    start_date = target_date.replace(day=1)\n", "    end_date = (start_date + relativedelta(months=1)) - <PERSON><PERSON><PERSON>(days=1)\n", "    description = f\"{start_date.strftime('%B %Y')}\"\n", "    return start_date, end_date, description"]}, {"cell_type": "code", "execution_count": 16, "id": "8de03dae", "metadata": {}, "outputs": [{"data": {"text/plain": ["(datetime.date(2025, 4, 1), datetime.date(2025, 4, 30), 'April 2025')"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["get_month_range(3)"]}, {"cell_type": "code", "execution_count": null, "id": "4b6c25ff", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}