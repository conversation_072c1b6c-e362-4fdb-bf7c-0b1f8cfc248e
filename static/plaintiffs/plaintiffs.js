// Menu activation is handled by common.js activateMenuItem('menu-plaintiffs')

// Main functionality
document.addEventListener('DOMContentLoaded', function() {
    // Set default sort order
    document.getElementById('sort_by').value = 'id';
    document.getElementById('sort_order').value = 'asc';

    // Initial data fetch
    fetchPlaintiffs();

    // Add event listeners for filter actions
    document.getElementById('apply-filters').addEventListener('click', function() {
        fetchPlaintiffs();
    });

    document.getElementById('refresh-data').addEventListener('click', function() {
        fetchPlaintiffs(true);
    });

    // Add event listeners for cleanup actions
    document.getElementById('delete-without-cases').addEventListener('click', function() {
        runAction('delete_without_cases');
    });

    document.getElementById('fix-auto').addEventListener('click', function() {
        runAction('fix_auto');
    });

    document.getElementById('fix-manual').addEventListener('click', function() {
        runAction('fix_manual');
    });

    // Add search on enter key
    document.getElementById('search_term').addEventListener('keyup', function(event) {
        if (event.key === 'Enter') {
            fetchPlaintiffs();
        }
    });
});

function fetchPlaintiffs(forceRefresh = false) {
    // Get filter values
    const searchTerm = document.getElementById('search_term').value;
    const sortBy = document.getElementById('sort_by').value;
    const sortOrder = document.getElementById('sort_order').value;

    // Show loading
    document.querySelector('.table-container .loading').style.display = 'block';
    document.getElementById('plaintiffs-table').style.display = 'none';

    // Build query parameters
    const params = new URLSearchParams({
        search_term: searchTerm,
        sort_by: sortBy,
        sort_order: sortOrder,
        force_refresh: forceRefresh
    });

    // Fetch data
    fetch(`/get_plaintiffs?${params}`)
        .then(response => response.json())
        .then(data => {
            renderPlaintiffs(data.plaintiffs);
            document.getElementById('total-results').textContent = `${data.plaintiffs.length} plaintiffs`;
            document.getElementById('last-refresh').textContent = `Last refresh: ${new Date().toLocaleTimeString()}`;
        })
        .catch(error => {
            console.error('Error fetching plaintiffs:', error);
            document.querySelector('.table-container .loading').textContent = 'Error loading plaintiffs data. Please try again.';
        });
}

function renderPlaintiffs(plaintiffs) {
    const tableBody = document.querySelector('#plaintiffs-table tbody');
    tableBody.innerHTML = ''; // Clear previous rows

    if (!plaintiffs || plaintiffs.length === 0) {
        document.querySelector('.table-container .loading').textContent = 'No plaintiffs found';
        document.querySelector('.table-container .loading').style.display = 'block';
        document.getElementById('plaintiffs-table').style.display = 'none';
        return;
    }

    plaintiffs.forEach(plaintiff => {
        const row = document.createElement('tr');
        row.setAttribute('data-plaintiff-id', plaintiff.id); // Add ID for easy row selection

        // --- Data Extraction ---
        let overviewEn = 'N/A';
        let overviewZh = '';
        if (plaintiff.plaintiff_overview_data && typeof plaintiff.plaintiff_overview_data === 'object') {
            overviewEn = plaintiff.plaintiff_overview_data.English || 'N/A';
            overviewZh = (plaintiff.plaintiff_overview_data.Chinese || '').substring(0, 5);
        } else if (plaintiff.plaintiff_overview_data) { // Handle case where it might be a string
            overviewEn = String(plaintiff.plaintiff_overview_data);
        }


        // --- Cell Creation ---
        // ID
        const idCell = document.createElement('td');
        idCell.textContent = plaintiff.id;
        row.appendChild(idCell);

        // Name
        const nameCell = document.createElement('td');
        nameCell.className = 'col-plaintiff-name'; // Add class for styling
        nameCell.textContent = plaintiff.plaintiff_name;
        row.appendChild(nameCell);

        // Overview (English)
        const overviewCell = document.createElement('td');
        overviewCell.className = 'col-overview truncate'; // Add class for styling
        overviewCell.textContent = overviewEn;
        overviewCell.setAttribute('data-field', 'overview-en'); // For potential dynamic update
        row.appendChild(overviewCell);

        // Chinese Overview Snippet
        const chineseCell = document.createElement('td');
        chineseCell.className = 'col-chinese-overview'; // Add class for styling
        chineseCell.textContent = overviewZh;
        chineseCell.setAttribute('data-field', 'overview-zh'); // For potential dynamic update
        row.appendChild(chineseCell);

        // Cases Count
        const casesCell = document.createElement('td');
        casesCell.className = 'col-cases-count'; // Add class for styling
        casesCell.textContent = plaintiff.cases_count;
        row.appendChild(casesCell);

        // Actions
        const actionsCell = document.createElement('td');
        actionsCell.className = 'col-actions'; // Add class for styling
        const updateButton = document.createElement('button');
        updateButton.className = 'btn btn-sm btn-info'; // Use smaller button style
        updateButton.textContent = 'Update Overview';
        updateButton.onclick = () => updatePlaintiffOverview(plaintiff.id); // Attach click handler
        actionsCell.appendChild(updateButton);
        row.appendChild(actionsCell);

        tableBody.appendChild(row);
    });

    // Hide loading, show table
    document.querySelector('.table-container .loading').style.display = 'none';
    document.getElementById('plaintiffs-table').style.display = 'table';
}

function runAction(action) {
    // Confirm before running
    if (!confirm(`Are you sure you want to run the "${action}" action? This operation cannot be undone.`)) {
        return;
    }

    // Show loading status
    const actionResult = document.getElementById('action-result');
    actionResult.className = 'action-result info';
    actionResult.textContent = 'Running action, please wait...';

    // Disable all buttons during operation
    const buttons = document.querySelectorAll('.cleanup-actions button');
    buttons.forEach(button => {
        button.disabled = true;
    });

    // Run action
    fetch('/run_plaintiff_action', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: action })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            actionResult.className = 'action-result success';
            actionResult.textContent = data.message;
            // Refresh data to show changes
            fetchPlaintiffs(true);
        } else if (data.status === 'redirect') {
            // Handle redirect
            window.location.href = data.url;
        } else {
            actionResult.className = 'action-result error';
            actionResult.textContent = data.message;
        }
    })
    .catch(error => {
        actionResult.className = 'action-result error';
        actionResult.textContent = `Error running action: ${error}`;
    })
    .finally(() => {
        // Re-enable buttons
        buttons.forEach(button => {
            button.disabled = false;
        });
    });
}

function updatePlaintiffOverview(plaintiffId) {
    const actionResult = document.getElementById('action-result');
    const row = document.querySelector(`tr[data-plaintiff-id="${plaintiffId}"]`);
    const updateButton = row ? row.querySelector('.col-actions button') : null;

    // Show loading status on button and general message area
    actionResult.className = 'action-result info';
    actionResult.textContent = `Updating overview for plaintiff ${plaintiffId}...`;
    if (updateButton) {
        updateButton.disabled = true;
        updateButton.textContent = 'Updating...';
    }

    fetch('/update_plaintiff_overview', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ plaintiff_id: plaintiffId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            actionResult.className = 'action-result success';
            actionResult.textContent = data.message;
            // Update the table row dynamically
            if (row && data.new_overview) {
                const overviewEnCell = row.querySelector('td[data-field="overview-en"]');
                const overviewZhCell = row.querySelector('td[data-field="overview-zh"]');
                if (overviewEnCell) overviewEnCell.textContent = data.new_overview.English || 'N/A';
                if (overviewZhCell) overviewZhCell.textContent = (data.new_overview.Chinese || '').substring(0, 5);
            }
        } else {
            actionResult.className = 'action-result error';
            actionResult.textContent = data.message || 'An unknown error occurred.';
        }
    })
    .catch(error => {
        console.error('Error updating plaintiff overview:', error);
        actionResult.className = 'action-result error';
        actionResult.textContent = `Error updating overview: ${error}`;
    })
    .finally(() => {
        // Re-enable button
        if (updateButton) {
            updateButton.disabled = false;
            updateButton.textContent = 'Update Overview';
        }
    });
}