/* Extracted from templates/plaintiff_duplicates.html */
/* Use .card for the header */
.duplicates-header {
    /* Inherits from .card */
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.duplicates-header h2 {
    margin: 0;
    font-size: 1.1rem; /* Match h3 */
}

.duplicates-header p {
    color: var(--text-secondary);
    margin: 0;
    flex-grow: 1;
}

.duplicates-container {
    margin-top: var(--spacing-lg);
}

/* Use .card for duplicate cards */
.duplicate-card {
    /* Inherits from .card */
    margin-bottom: var(--spacing-md); /* Override default card margin if needed */
}

.duplicate-card.completed {
    opacity: 0.7;
    position: relative;
}

.duplicate-card.completed::after {
    content: 'Completed';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--success-color);
    color: white;
    padding: 5px 10px;
    border-radius: var(--border-radius-sm); /* Use common var */
    font-weight: bold;
    z-index: 10;
}

.duplicate-header {
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color); /* Use common var */
}

.duplicate-header h3 {
    margin: 0;
    font-size: 1rem; /* Adjust size */
}

/* Use .common-table */
.duplicate-table {
    /* Inherits from .common-table */
    margin-bottom: var(--spacing-md); /* Override default table margin if needed */
}

/* Specific column width */
.duplicate-table th:nth-child(1) {
    width: 100px;
}

/* Use common message classes: .info-message, .success-message, .error-message */
.merge-result {
    margin-top: var(--spacing-md);
    /* Base styles handled by common message classes */
    display: none; /* Hide by default */
}
.merge-result.show { /* Add class to show */
    display: block;
}