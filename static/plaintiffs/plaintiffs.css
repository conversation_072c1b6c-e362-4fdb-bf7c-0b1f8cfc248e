.cleanup-actions-container h3 {
    margin-bottom: var(--spacing-md);
    font-size: 1rem; /* Adjust size if needed */
}


/* Use common message classes: .info-message, .success-message, .error-message */
.action-result {
    margin-top: var(--spacing-md);
    /* Base styles handled by common message classes */
    display: none; /* Keep hidden by default */
}
.action-result.show { /* Add a class to show the message */
    display: block;
}


/* --- Column Widths & Styling (Keep Specifics) --- */
.col-plaintiff-name {
    max-width: 250px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.col-overview {
     max-width: 350px;
}
.col-chinese-overview {
    max-width: 80px;
    text-align: center;
    font-size: 0.9em;
}
.col-cases-count {
     width: 150px;
     text-align: center;
}
.col-actions {
    width: 150px;
    text-align: center;
}

/* Truncate styling for overview - Use common .truncate */
/* Keep hover effect as it's specific to this table context */
.truncate:hover {
    white-space: normal;
    overflow: visible;
    background-color: var(--card-background);
    position: absolute; /* Position relative to the table cell */
    z-index: 10;
    box-shadow: var(--shadow);
    padding: 5px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    max-width: 500px; /* Limit expanded width */
    cursor: text; /* Allow text selection */
}