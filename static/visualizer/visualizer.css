/* Specific styles for case visualizer */

/* Header */
header {
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

header h1 {
    font-size: 1.5rem;
    font-weight: 500;
}

/* Results Info - Use common styles */
.results-info {
    color: var(--text-secondary);
    font-size: var(--text-size);
}

.case-title-container {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-grow: 1;
}

.case-title {
    font-size: var(--heading-size);
    font-weight: 600;
    margin-bottom: 0;
    flex-grow: 1;
}

.proposed-name {
    color: var(--accent-color);
    font-style: italic;
    font-weight: normal;
    font-size: 12px;
}

.case-title-container .improve-plaintiff-btn {
    width: 24px;
    height: 24px;
    min-width: 24px;
    font-size: 10px;
    padding: 0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--accent-color);
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    margin-left: 4px;
}

.case-title-container .improve-plaintiff-btn:hover {
    background-color: var(--accent-hover);
    transform: scale(1.1);
}

.case-title-container .improve-plaintiff-btn.success {
    background-color: var(--success-color);
}

.case-title-container .improve-plaintiff-btn.error {
    background-color: var(--error-color);
}

.validation-icons {
    display: flex;
    gap: var(--spacing-xs);
    align-items: center;
}

.validation-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background-color: #444;
    color: #777;
    font-size: 12px;
    transition: all 0.2s ease;
}

.validation-icon.active.validated {
    background-color: var(--success-color);
    color: white;
}

.validation-icon.active.review {
    background-color: var(--warning-color);
    color: white;
}

.validation-icon.active.failed {
    background-color: var(--error-color);
    color: white;
}

.validation-icon:hover {
    transform: scale(1.1);
}

.validation-icon i {
    font-size: 12px;
}

/* Dropdown checkbox menu - Keep */
.dropdown-checkbox {
    position: relative;
    display: inline-block;
    width: 100%;
}

.dropdown-checkbox-header {
    background-color: var(--input-background); /* Use common var */
    border: 1px solid var(--border-color); /* Use common var */
    border-radius: var(--border-radius-sm); /* Use common var */
    padding: var(--spacing-sm);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dropdown-checkbox-content {
    display: none;
    position: absolute;
    background-color: var(--input-background); /* Use common var */
    min-width: 100%;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.5);
    padding: var(--spacing-sm);
    z-index: 1;
    border-radius: var(--border-radius-sm); /* Use common var */
    border: 1px solid var(--border-color); /* Use common var */
    max-height: 200px;
    overflow-y: auto;
}

.dropdown-checkbox-content.show {
    display: block;
}

.dropdown-checkbox-item {
    padding: var(--spacing-xs) 0;
}

/* Case Details Row - Keep */
.case-details-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 10px;
    border-bottom: 1px solid var(--border-color); /* Use common var */
    padding-bottom: 10px;
}

.case-info-left {
    display: flex;
    flex-wrap: wrap;
    gap: 5px 15px;
    flex-grow: 1;
}

.case-info-right {
    display: flex;
    gap: 15px;
    flex-shrink: 0;
    white-space: nowrap;
    margin-left: 20px;
}

.action-link {
    color: var(--accent-color); /* Use common var */
    text-decoration: none;
    font-weight: 500;
}

.action-link:hover {
    text-decoration: underline;
    color: var(--accent-hover); /* Use common var */
}

.action-link.disabled {
    color: #6c757d; /* Keep specific disabled color? */
    pointer-events: none;
    cursor: default;
    opacity: 0.7;
}

.case-description {
    margin-top: 10px;
    clear: both;
}

/* Update Case Button - Keep */
.update-case-btn {
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    transition: background-color 0.2s;
}

.update-case-btn:hover {
    background-color: var(--accent-hover);
}


/* Logs (case processing and steps) */
.log-section {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius-sm); /* Use common var */
    padding: var(--spacing-sm);
    margin: var(--spacing-md) 0;
    height: 300px; /* Initial height */
    max-height: 80vh; /* Maximum height when expanded */
    overflow-y: auto;
    resize: vertical;
    position: relative;
}

.log-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 4px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 8px;
}

.log-section-title {
    font-weight: 600;
    font-size: var(--heading-size); /* Use common var */
}

.log-section-content {
    font-family: monospace;
    font-size: var(--text-size); /* Use common var */
    white-space: pre-wrap;
}

.close-log {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 14px;
    padding: 0;
}

.close-log:hover {
    color: var(--text-color);
}


/* Steps Section Styles*/
.steps-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

.steps-table th,
.steps-table td {
    text-align: left;
    padding: 6px 8px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.steps-table th {
    font-weight: 600;
    color: var(--text-color);
    background-color: rgba(0, 0, 0, 0.2);
}

.steps-table tr:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.02);
}

.steps-table tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Adjust table columns width */
.steps-table th:nth-child(1), /* Date Filed */
.steps-table td:nth-child(1) {
    width: 100px; /* 80% wider than default */
}

.steps-table th:nth-child(7), /* Last Updated */
.steps-table td:nth-child(7) {
    width: 90px; /* 20% wider than default */
}

/* Make proceeding text columns take available space */
.steps-table th:nth-child(3),
.steps-table td:nth-child(3),
.steps-table th:nth-child(4),
.steps-table td:nth-child(4) {
    min-width: 200px;
}

.steps-count {
    color: var(--text-color);
}

.steps-count.duplicate {
    color: #ff5555; /* Red color for duplicate step numbers */
}

.steps-info-item {
    cursor: pointer;
    position: relative;
}

.steps-info-item:hover .info-label {
    text-decoration: underline;
}


/* Overlay to select reprocess options (on top of common overlay in common.css) */
.option-group {
    margin-bottom: var(--spacing-md);
}

.option-group-title {
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.radio-option, .checkbox-option {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-xs);
    padding: var(--spacing-xs);
    border-radius: 4px;
}

.radio-option:hover, .checkbox-option:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.radio-option input[type="radio"],
.checkbox-option input[type="checkbox"] {
    margin-right: var(--spacing-sm);
    width: auto;
    margin-top: 2px;
}

.option-label {
    display: flex;
    flex-direction: column;
}

.option-text {
    font-weight: 500;
}

.option-description {
    font-size: var(--detail-size);
    color: var(--text-secondary);
}

.file-type-options {
    margin-left: 20px;
    padding-left: var(--spacing-sm);
    border-left: 2px solid #444;
}

.disabled {
    opacity: 0.4;
    pointer-events: none;
    background-color: rgba(0, 0, 0, 0.1);
}