document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const applyFiltersBtn = document.getElementById('apply-filters');
    const refreshDataBtn = document.getElementById('refresh-data');
    const casesContainer = document.getElementById('cases-container'); // Renamed from plaintiffsContainer
    const totalResultsEl = document.getElementById('total-results');
    const lastRefreshEl = document.getElementById('last-refresh');
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');
    const pageInfoEl = document.getElementById('page-info');
    const pictureTypeSelect = document.getElementById('picture_type');

    // State
    let currentPage = 1;
    let totalPages = 1;
    let currentData = [];
    let currentFilters = {
        plaintiff_name: '',
        plaintiff_id: '',
        picture_type: '',
        ip_count_min: 0,
        ip_count_max: '',
        sort_by: 'ip_count',
        sort_order: 'desc',
        limit: 20,
        offset: 0
    };

    // Initialize
    fetchPlaintiffsIP(false);

    // Event Listeners
    applyFiltersBtn.addEventListener('click', function() {
        currentPage = 1;
        updateFilters();
        fetchPlaintiffsIP();
    });

    refreshDataBtn.addEventListener('click', function() {
        fetchPlaintiffsIP(true);
    });

    prevPageBtn.addEventListener('click', function() {
        if (currentPage > 1) {
            currentPage--;
            currentFilters.offset = (currentPage - 1) * currentFilters.limit;
            fetchPlaintiffsIP();
        }
    });

    nextPageBtn.addEventListener('click', function() {
        if (currentPage < totalPages) {
            currentPage++;
            currentFilters.offset = (currentPage - 1) * currentFilters.limit;
            fetchPlaintiffsIP();
        }
    });

    // Update filters from form inputs
    function updateFilters() {
        currentFilters.plaintiff_name = document.getElementById('plaintiff_name').value;
        currentFilters.plaintiff_id = document.getElementById('plaintiff_id').value;
        currentFilters.ip_count_min = document.getElementById('ip_count_min').value;
        currentFilters.ip_count_max = document.getElementById('ip_count_max').value;
        currentFilters.sort_by = document.getElementById('sort_by').value;
        currentFilters.sort_order = document.getElementById('sort_order').value;
        currentFilters.limit = parseInt(document.getElementById('limit').value);
        currentFilters.offset = (currentPage - 1) * currentFilters.limit;

        // Get selected picture type
        currentFilters.picture_type = pictureTypeSelect.value;
    }

    function fetchPlaintiffsIP(refresh = false) {
        // Show loading state
        casesContainer.innerHTML = '<div class="loading">Loading plaintiffs IP data...</div>'; // Renamed variable

        // Build query string
        const params = new URLSearchParams();

        // Add all simple params
        for (const [key, value] of Object.entries(currentFilters)) {
            if (value !== '' && !Array.isArray(value)) {
                params.append(key, value);
            }
        }

        // No need to handle arrays anymore

        if (refresh) {
            params.append('refresh', 'true');
        }

        // Fetch data
        fetch(`/api/plaintiffs/ip?${params.toString()}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                currentData = data.plaintiffs;
                totalResultsEl.textContent = `${data.total} plaintiffs`;
                lastRefreshEl.textContent = `Last refresh: ${new Date().toLocaleTimeString()}`;

                // Update pagination
                totalPages = Math.ceil(data.total / currentFilters.limit);
                pageInfoEl.textContent = `Page ${currentPage} of ${totalPages || 1}`;
                prevPageBtn.disabled = currentPage <= 1;
                nextPageBtn.disabled = currentPage >= totalPages || totalPages === 0;

                // Render plaintiffs
                renderPlaintiffs(currentData);
            })
            .catch(error => {
                console.error('Error fetching plaintiffs IP data:', error);
                casesContainer.innerHTML = `<div class="error">Error loading plaintiffs IP data: ${error.message}</div>`; // Renamed variable
            });
    }

    function renderPlaintiffs(plaintiffs) {
        if (plaintiffs.length === 0) {
            casesContainer.innerHTML = '<div class="no-results">No plaintiffs match your search criteria</div>'; // Renamed variable
            return;
        }

        casesContainer.innerHTML = ''; // Renamed variable

        plaintiffs.forEach(plaintiff => {
            const plaintiffCard = renderSinglePlaintiffCard(plaintiff);
            casesContainer.appendChild(plaintiffCard); // Renamed variable
        });
    }

    function renderSinglePlaintiffCard(plaintiff) {
        const plaintiffCard = document.createElement('div');
        plaintiffCard.className = 'card plaintiff-card';

        // Plaintiff header
        const plaintiffHeader = document.createElement('div');
        plaintiffHeader.className = 'plaintiff-header';

        const plaintiffName = document.createElement('h2');
        plaintiffName.className = 'plaintiff-name';
        plaintiffName.textContent = plaintiff.plaintiff_name;

        const plaintiffId = document.createElement('div');
        plaintiffId.className = 'plaintiff-id';
        plaintiffId.textContent = `(ID: ${plaintiff.id}`;

        const ipCount = document.createElement('div');
        ipCount.className = 'ip-count';
        ipCount.textContent = `- Total IP: ${plaintiff.total_ip_count})`;

        plaintiffHeader.appendChild(plaintiffName);
        plaintiffHeader.appendChild(plaintiffId);
        plaintiffHeader.appendChild(ipCount);

        plaintiffCard.appendChild(plaintiffHeader);

        // IP sections
        const ipTypes = [
            { key: 'trademarks', name: 'Trademarks', statusKey: 'trademark_status' },
            { key: 'patents', name: 'Patents', statusKey: 'patent_status' },
            { key: 'copyrights', name: 'Copyrights', statusKey: 'copyright_status' }
        ];

        ipTypes.forEach(type => {
            if (plaintiff[type.key] && plaintiff[type.key].length > 0) {
                const ipSection = document.createElement('div');
                ipSection.className = 'ip-section';

                const ipTitleContainer = document.createElement('div');
                ipTitleContainer.className = 'ip-title';

                const ipTitleText = document.createElement('div');
                ipTitleText.className = 'ip-title-text';
                ipTitleText.textContent = `${type.name} (${plaintiff[type.key].length})`;
                ipTitleContainer.appendChild(ipTitleText);

                ipSection.appendChild(ipTitleContainer);

                const ipItems = document.createElement('div');
                ipItems.className = 'ip-items';

                plaintiff[type.key].forEach(item => {
                    const ipItem = document.createElement('div');
                    ipItem.className = 'ip-item';

                    // Image
                    if (item.image) {
                        const imgUrl = getImagePath(plaintiff.id, item.image, type.key);
                        const img = document.createElement('img');
                        img.className = 'ip-item-image';
                        img.src = imgUrl;
                        img.alt = type.name;
                        img.onerror = function() {
                            console.log("Image not found for plaintiff " + plaintiff.id + ", image: " + item.image);
                            this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzMzMyIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjZmZmIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+Tm8gSW1hZ2U8L3RleHQ+PC9zdmc+';
                        };

                        // Add click handler for image preview (using common function)
                        // Always preview high quality for the item's own image
                        img.addEventListener('click', function() {
                            const highQualityItemUrl = `http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/${plaintiff.id}/high/${item.image}`;
                            previewImage(highQualityItemUrl);
                        });

                        ipItem.appendChild(img);
                    }

                    // IP info
                    const ipItemInfo = document.createElement('div');
                    ipItemInfo.className = 'ip-item-info';

                    // Add specific fields based on IP type
                    if (type.key === 'trademarks') {
                        const trademarkText = Array.isArray(item.trademark_text) ? item.trademark_text.join(', ') : item.trademark_text;
                        const regNo = Array.isArray(item.reg_no) ? item.reg_no.join(', ') : item.reg_no;
                        const intClsList = Array.isArray(item.int_cls) ? item.int_cls.join(', ') : item.int_cls;

                        addIpInfoItem(ipItemInfo, 'Keywords:', trademarkText || 'N/A');
                        // Always add the Reg # item, then make the value a link
                        const regNoInfoItem = addIpInfoItem(ipItemInfo, 'Reg #:', regNo || 'N/A');
                        makeInfoItemValueLinkable(regNoInfoItem, plaintiff.id, 'Reg #:', regNo, item.full_filename);
                        addIpInfoItem(ipItemInfo, 'Class #:', intClsList || 'N/A');
                    } else if (type.key === 'patents') {
                        addIpInfoItem(ipItemInfo, 'Title:', item.text || 'N/A');
                        addIpInfoItem(ipItemInfo, 'Applicant:', item.applicant || 'N/A');
                        addIpInfoItem(ipItemInfo, 'Assignee:', item.assignee || 'N/A');
                        // Always add the Patent # item, then potentially make the value a link
                        const patentNoInfoItem = addIpInfoItem(ipItemInfo, 'Patent #:', item.patent_number || 'N/A');
                        makeInfoItemValueLinkable(patentNoInfoItem, plaintiff.id, 'Patent #:', item.patent_number, item.full_filename);
                    } else if (type.key === 'copyrights') {
                        const regNo = Array.isArray(item.reg_no) ? item.reg_no.join(', ') : item.reg_no;
                        // Always add the Reg # item, then potentially make the value a link
                        const regNoInfoItem = addIpInfoItem(ipItemInfo, 'Reg #:', regNo || 'N/A');
                        addIpInfoItem(ipItemInfo, 'Title:', item.title || 'N/A');
                        makeInfoItemValueLinkable(regNoInfoItem, plaintiff.id, 'Reg #:', regNo, item.full_filename);
                    }

                    ipItem.appendChild(ipItemInfo);
                    ipItems.appendChild(ipItem);
                });

                ipSection.appendChild(ipItems);
                plaintiffCard.appendChild(ipSection);
            }
        });

        return plaintiffCard;
    }

    function addIpInfoItem(container, label, value) {
        const item = document.createElement('div');

        const labelEl = document.createElement('span');
        labelEl.className = 'ip-item-label';
        // Remove the colon from the label if it already has one
        const labelText = label.endsWith(':') ? label : label + ':';
        labelEl.textContent = labelText + ' ';

        const valueEl = document.createElement('span');
        valueEl.textContent = value;

        item.appendChild(labelEl);
        item.appendChild(valueEl);
        container.appendChild(item);

        return item;
    }

    // previewImage and getImagePath functions are available in common.js
});
