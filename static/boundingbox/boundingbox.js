document.addEventListener('DOMContentLoaded', () => {
    const imageUploadArea = document.getElementById('image-upload-area');
    const imageUploadInput = document.getElementById('image-upload-input');
    const imagePreview = document.getElementById('image-preview');
    const promptInput = document.getElementById('prompt-input');
    const modelSelect = document.getElementById('model-select');
    const modelOtherInput = document.getElementById('model-other-input');
    const resizeXInput = document.getElementById('resize-x-input');
    const resizeYInput = document.getElementById('resize-y-input');
    const detectButton = document.getElementById('detect-button');
    const loadingIndicator = document.getElementById('loading-indicator');
    const errorMessage = document.getElementById('error-message');
    const resultImageContainer = document.getElementById('result-image-container');
    const resultImage = document.getElementById('result-image');
    const noResultImage = document.getElementById('no-result-image');
    const jsonOutputContainer = document.getElementById('json-output-container');
    const jsonOutput = document.getElementById('json-output');
    const copyJsonButton = document.getElementById('copy-json-button');
    // New elements for segmentation masks
    const segmentationMasksContainer = document.getElementById('segmentation-masks-container');
    const segmentationMasksGrid = document.getElementById('segmentation-masks-grid');

    let selectedFile = null;

    // --- Image Upload Logic ---

    // Trigger file input click when upload area is clicked
    imageUploadArea.addEventListener('click', () => {
        imageUploadInput.click();
    });

    // Handle file selection via input
    imageUploadInput.addEventListener('change', (event) => {
        const files = event.target.files;
        if (files.length > 0) {
            handleFile(files[0]);
        }
    });

    // Handle drag and drop
    imageUploadArea.addEventListener('dragover', (event) => {
        event.preventDefault(); // Prevent default browser behavior
        imageUploadArea.classList.add('dragover');
    });

    imageUploadArea.addEventListener('dragleave', () => {
        imageUploadArea.classList.remove('dragover');
    });

    imageUploadArea.addEventListener('drop', (event) => {
        event.preventDefault(); // Prevent default browser behavior
        imageUploadArea.classList.remove('dragover');
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            handleFile(files[0]);
            imageUploadInput.files = files; // Sync input files for consistency
        }
    });

    function handleFile(file) {
        if (file && file.type.startsWith('image/')) {
            selectedFile = file;
            const reader = new FileReader();
            reader.onload = (e) => {
                imagePreview.src = e.target.result;
                imagePreview.style.display = 'block';
                // Optionally hide the placeholder text
                const p = imageUploadArea.querySelector('p');
                if (p) p.style.display = 'none';
            }
            reader.readAsDataURL(file);
            errorMessage.style.display = 'none'; // Hide error if a valid file is selected
        } else {
            selectedFile = null;
            imagePreview.style.display = 'none';
            const p = imageUploadArea.querySelector('p');
            if (p) p.style.display = 'block'; // Show placeholder text
            showError('Please select a valid image file (PNG, JPG, JPEG, WEBP).');
        }
    }

    // --- Detect Button Logic ---

    detectButton.addEventListener('click', async () => {
        if (!selectedFile) {
            showError('Please select an image file first.');
            return;
        }

        // Get selected model name (handle 'Other')
        let modelName = modelSelect.value;
        if (modelName === 'other') {
            modelName = modelOtherInput.value.trim();
            if (!modelName) {
                showError('Please specify the custom model name.');
                return;
            }
        }

        const prompt = promptInput.value.trim();
        const resizeX = resizeXInput.value;
        const resizeY = resizeYInput.value;

        // Basic validation
        if (!prompt) {
            showError('Please enter a prompt.');
            return;
        }
        if (!resizeX || !resizeY || parseInt(resizeX) < 1 || parseInt(resizeY) < 1) {
            showError('Please enter valid resize dimensions (minimum 1).');
            return;
        }


        // Prepare UI for loading
        showLoading(true);
        clearResults();

        // Create FormData
        const formData = new FormData();
        formData.append('image', selectedFile);
        formData.append('prompt', prompt);
        formData.append('model_name', modelName);
        formData.append('resize_x', resizeX);
        formData.append('resize_y', resizeY);

        // Get and append selected output type
        const outputType = document.querySelector('input[name="output_type"]:checked').value;
        formData.append('output_type', outputType);

        try {
            const response = await fetch('/api/boundingbox/process', {
                method: 'POST',
                body: formData,
            });

            const data = await response.json();

            if (response.ok && data.success) {
                // Success: Display results
                if (data.image_data) {
                    resultImage.src = data.image_data;
                    resultImage.style.display = 'block';
                    noResultImage.style.display = 'none';
                } else {
                     noResultImage.textContent = 'Processing successful, but no image data returned.';
                     noResultImage.style.display = 'block';
                     resultImage.style.display = 'none';
                }

                if (data.json_output) {
                    const formattedJson = JSON.stringify(data.json_output, null, 2); // Pretty print JSON
                    jsonOutput.textContent = formattedJson;
                    copyJsonButton.disabled = false;
                } else {
                    jsonOutput.textContent = 'No JSON output returned.';
                    copyJsonButton.disabled = true;
                }

                // --- Handle Segmentation Masks ---
                const selectedOutputType = document.querySelector('input[name="output_type"]:checked').value; // Get selected type again

                if (selectedOutputType === 'bbox_mask' && data.segmentation_masks && Array.isArray(data.segmentation_masks) && data.segmentation_masks.length > 0) {
                    segmentationMasksContainer.style.display = 'block'; // Show container
                    segmentationMasksGrid.innerHTML = ''; // Clear previous masks

                    data.segmentation_masks.forEach(mask => {
                        const maskItemDiv = document.createElement('div');
                        maskItemDiv.classList.add('mask-item'); // Add class for styling

                        const img = document.createElement('img');
                        img.src = mask.mask_base64; // Assumes base64 data URL
                        img.alt = `Mask for ${mask.label}`;

                        const label = document.createElement('p');
                        label.textContent = mask.label;

                        maskItemDiv.appendChild(img);
                        maskItemDiv.appendChild(label);
                        segmentationMasksGrid.appendChild(maskItemDiv);
                    });
                } else {
                    // Hide and clear if no masks or bbox_only selected
                    segmentationMasksContainer.style.display = 'none';
                    segmentationMasksGrid.innerHTML = '';
                }
                // --- End Handle Segmentation Masks ---

            } else {
                // Failure: Display error
                showError(data.error || 'An unknown error occurred.');
                // Ensure mask container is hidden on error too
                segmentationMasksContainer.style.display = 'none';
                segmentationMasksGrid.innerHTML = '';
            }
        } catch (error) {
            console.error('Fetch error:', error);
            showError('Failed to communicate with the server. Check the console for details.');
        } finally {
            // Hide loading indicator regardless of outcome
            showLoading(false);
        }
    });

    // --- JSON Copy Logic ---
    // TODO: Replace with common copy utility if created, or keep specific if needed.
    copyJsonButton.addEventListener('click', () => {
        if (jsonOutput.textContent && jsonOutput.textContent !== 'No JSON output yet.') {
            navigator.clipboard.writeText(jsonOutput.textContent)
                .then(() => {
                    const originalText = copyJsonButton.textContent;
                    copyJsonButton.textContent = 'Copied!';
                    setTimeout(() => { copyJsonButton.textContent = originalText; }, 1500);
                })
                .catch(err => {
                    console.error('Failed to copy JSON:', err);
                    // Use common error display
                    showErrorState(jsonOutputContainer, 'Failed to copy JSON to clipboard.');
                });
        }
    });

    // --- Helper Functions ---
    // NOTE: showLoading, showError, clearResults removed, use common.js equivalents

    function clearBoundingBoxResults() {
        // Specific clearing logic for this module
        errorMessage.style.display = 'none'; // Still hide specific error element if needed
        resultImage.style.display = 'none';
        resultImage.src = '#';
        noResultImage.style.display = 'block';
        noResultImage.textContent = 'No image processed yet.';
        jsonOutput.textContent = 'No JSON output yet.';
        copyJsonButton.disabled = true;
        if (segmentationMasksContainer) segmentationMasksContainer.style.display = 'none';
        if (segmentationMasksGrid) segmentationMasksGrid.innerHTML = '';
    }


    // Initial setup for model select 'Other' input visibility
    const modelOtherInputContainer = document.getElementById('model-other-input');
    modelSelect.addEventListener('change', function() {
        modelOtherInputContainer.style.display = this.value === 'other' ? 'block' : 'none';
        if (this.value === 'other') {
            modelOtherInputContainer.focus();
        }
    });
    // Ensure it's hidden initially if default is not 'other'
    if (modelSelect.value !== 'other') {
         modelOtherInputContainer.style.display = 'none';
    }

});