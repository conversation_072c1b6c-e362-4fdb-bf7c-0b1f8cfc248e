// Form validation functions

// Function to validate file uploads
function validateFileUpload(id, previewId, max, name) {
    const input = document.getElementById(id),
        preview = previewId ? document.getElementById(previewId) : null,
        submitButton = document.getElementById("submitButton");
    
    if (input) {
        input.addEventListener("change", e => {
            if (e.target.files.length > max) {
                alert(`You can only upload up to ${max} images for ${name}.`);
                e.target.value = "";
                if (preview) preview.innerHTML = "";
                // Disable the submit button when the limit is exceeded
                submitButton.disabled = true;
            } else {
                // Re-enable the submit button if the file count is within limits
                submitButton.disabled = false;
            }
        });
    }
}

// Function to validate URL inputs
function validateUrlInput(id, max, name) {
    const input = document.getElementById(id);
    const submitButton = document.getElementById("submitButton");

    if (input) {
        input.addEventListener("blur", () => {
            if (input.value.trim()) {
                const urls = input.value.split(",").map(u => u.trim()).filter(Boolean);
                if (urls.length > max) {
                    alert(`You can only specify up to ${max} URLs for ${name}.`);
                    input.value = urls.slice(0, max).join(", ");
                    submitButton.disabled = true;
                } else {
                    // If the number of URLs is within range, enable the submit button.
                    submitButton.disabled = false;
                }
            } else {
                // Clear input is fine, so enable the submit button.
                submitButton.disabled = false;
            }
        });
    }
}

// Export functions for global access
window.validateFileUpload = validateFileUpload;
window.validateUrlInput = validateUrlInput;
