// Reverse check functionality

// Initialize reverse check page
function initializeReverseCheckPage() {
    console.log('initializeReverseCheckPage called.');
    // Load saved API key
    const savedApiKey = getCookie('api_key');
    if (savedApiKey) {
        document.getElementById('reverse_check_api_key').value = savedApiKey;
    }

    // Set default date range (last 7 days)
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    document.getElementById('end_date').value = today.toISOString().split('T')[0];
    document.getElementById('start_date').value = lastWeek.toISOString().split('T')[0];

    // Add event listeners
    document.getElementById('fetchReverseCheckBtn').addEventListener('click', fetchReverseCheckResults);
    
    // Initialize report toggling functionality for this page
    initializeReportToggling();

    // Language switcher
    const languageSelect = document.getElementById('languageSelect');
    if (languageSelect) {
        languageSelect.addEventListener('change', function() {
            const lang = this.value;
            document.getElementById('language').value = lang;
            updateLanguage(lang);
        });
    }
}

// Fetch reverse check results
function fetchReverseCheckResults() {
    console.log('Fetch Reverse Check button clicked.');
    const apiKey = document.getElementById('reverse_check_api_key').value.trim();
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;

    if (!apiKey) {
        alert('Please enter your API key');
        return;
    }

    if (!startDate || !endDate) {
        alert('Please select both start and end dates');
        return;
    }

    // Validate date range (max 1 month)
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end - start);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays > 31) {
        alert('Date range cannot exceed 1 month (31 days)');
        return;
    }

    if (start > end) {
        alert('Start date must be before or equal to end date');
        return;
    }

    // Save API key
    setCookie('api_key', apiKey, 30);

    // Show loading state
    const reverseCheckTree = document.getElementById('reverseCheckTree');
    reverseCheckTree.innerHTML = '<div class="loading">Loading reverse checks</div>';

    // Make API call
    fetch('/reverse_check_status', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            api_key: apiKey,
            start_date: startDate,
            end_date: endDate
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Fetch data received:', data);
        if (data.error) {
            throw new Error(data.error);
        }
        displayReverseCheckList(data.results);
    })
    .catch(error => {
        console.error('Error fetching reverse checks:', error);
        reverseCheckTree.innerHTML = `<div class="error">Error: ${error.message}</div>`;
    });
}

// Display reverse check list
function displayReverseCheckList(results) {
    console.log('displayReverseCheckList called with results:', results);
    const reverseCheckTree = document.getElementById('reverseCheckTree');

    if (!results || Object.keys(results).length === 0) {
        reverseCheckTree.innerHTML = '<div class="no-results">No reverse checks found for the selected date range</div>';
        return;
    }

    let html = '';

    // Sort dates in descending order
    const sortedDates = Object.keys(results).sort((a, b) => new Date(b) - new Date(a));

    sortedDates.forEach(date => {
        html += `<div class="date-group">`;
        html += `<div class="date-header" onclick="toggleDateGroup('${date}')">${date} (${Object.keys(results[date]).length} checks)</div>`;
        html += `<div class="check-id-list" id="date-${date}">`;

        // Sort check IDs in descending order
        const sortedCheckIds = Object.keys(results[date]).sort((a, b) => b.localeCompare(a));

        sortedCheckIds.forEach(checkId => {
            html += `<div class="check-id-item" onclick="displayReverseCheckResult(event, '${date}', '${checkId}')">Check ID: ${checkId}</div>`;
        });

        html += `</div></div>`;
    });

    console.log('Generated HTML:', html);
    reverseCheckTree.innerHTML = html;

    // Store results for later use
    window.reverseCheckResults = results;
    console.log('Stored reverseCheckResults:', window.reverseCheckResults);
}

// Make sure this function is globally accessible
window.toggleDateGroup = function(date) {
    console.log('toggleDateGroup called with date:', date);
    const dateGroup = document.getElementById(`date-${date}`);
    if (dateGroup.style.display === 'none') {
        dateGroup.style.display = 'block';
    } else {
        dateGroup.style.display = 'none';
    }
}

// Make sure this function is globally accessible
window.displayReverseCheckResult = function(event, date, checkId) {
    console.log('displayReverseCheckResult called with:', { date, checkId });
    console.log('Event target:', event.target);
    console.log('Available reverseCheckResults:', window.reverseCheckResults);

    // Remove previous selection
    document.querySelectorAll('.check-id-item').forEach(item => {
        item.classList.remove('selected');
    });

    // Add selection to current item
    event.target.classList.add('selected');
    console.log('Added selected class to:', event.target);

    if (!window.reverseCheckResults) {
        console.error('No reverseCheckResults found in window');
        return;
    }

    if (!window.reverseCheckResults[date]) {
        console.error('No results found for date:', date);
        return;
    }

    const result = window.reverseCheckResults[date][checkId];
    if (!result) {
        console.error('Result not found for date:', date, 'checkId:', checkId);
        console.log('Available checkIds for date:', Object.keys(window.reverseCheckResults[date]));
        return;
    }

    console.log('Found result:', result);
    displayReverseCheckResultDetails(result);
}

// Display reverse check result details
function displayReverseCheckResultDetails(result) {
    console.log('displayReverseCheckResultDetails called with:', result);
    const outputDiv = document.getElementById('output');
    if (!outputDiv) {
        console.error('Output div not found!');
        return;
    }

    const isZh = document.getElementById('language').value === 'zh';

    // Clear previous content
    outputDiv.innerHTML = '';
    console.log('Cleared output div');

    // Create report container
    const reportContainer = document.createElement('div');
    reportContainer.className = 'reverse-check-result-container';

    // Check ID display
    const checkIdDisplay = document.createElement('p');
    checkIdDisplay.className = 'check-id';
    checkIdDisplay.innerHTML = `<strong data-i18n="check_id"></strong> ${result.check_id || 'N/A'}`;
    reportContainer.appendChild(checkIdDisplay);
    console.log('Added check ID display');

    // Parse result data - the actual results are in result.results (JSON string)
    let resultsData;
    console.log('Raw result object:', result);

    // Try to get results from result.results first (which is a JSON string)
    if (result.results && typeof result.results === 'string') {
        try {
            resultsData = JSON.parse(result.results);
            console.log('Parsed results from result.results:', resultsData);
        } catch (e) {
            console.error('Failed to parse result.results:', e);
            resultsData = result.results;
        }
    }
    // Fallback to result.result if available
    else if (result.result) {
        if (typeof result.result === 'string') {
            try {
                resultsData = JSON.parse(result.result);
                console.log('Parsed results from result.result:', resultsData);
            } catch (e) {
                console.error('Failed to parse result.result:', e);
                resultsData = result.result;
            }
        } else {
            resultsData = result.result;
        }
    }
    // If neither exists, use the whole result object
    else {
        console.log('Using whole result object as resultsData');
        resultsData = result;
    }

    // Display each result
    console.log('Final resultsData to display:', resultsData);
    console.log('Is resultsData an array?', Array.isArray(resultsData));

    if (Array.isArray(resultsData)) {
        console.log('Processing array of results, length:', resultsData.length);
        resultsData.forEach((item, index) => {
            console.log(`Processing result item ${index}:`, item);
            displaySingleReverseCheckResult(item, index, reportContainer, isZh);
        });
    } else if (resultsData) {
        console.log('Processing single result item:', resultsData);
        displaySingleReverseCheckResult(resultsData, 0, reportContainer, isZh);
    } else {
        console.error('No valid results data found');
        const errorDiv = document.createElement('div');
        errorDiv.innerHTML = '<p>No results data available</p>';
        reportContainer.appendChild(errorDiv);
    }

    outputDiv.appendChild(reportContainer);
    console.log('Added report container to output div');
    console.log('Final output div content:', outputDiv.innerHTML);
}

// Display single reverse check result
function displaySingleReverseCheckResult(item, index, container, isZh) {
    console.log('displaySingleReverseCheckResult called with item:', item);

    // Create result section
    const resultSection = document.createElement('div');
    resultSection.className = 'result-card';
    resultSection.style.marginBottom = '30px';
    resultSection.style.padding = '20px';
    resultSection.style.border = '1px solid #ddd';
    resultSection.style.borderRadius = '8px';

    // Result header
    const resultHeader = document.createElement('h3');
    resultHeader.textContent = `${isZh ? '结果' : 'Result'} ${index + 1}`;
    resultSection.appendChild(resultHeader);

    // Create formatted content based on the actual data structure
    const resultContent = document.createElement('div');

    // Basic information
    let contentHtml = '';

    if (item.ip_owner) {
        contentHtml += `<p><strong data-i18n="ip_owner">IP Owner:</strong> ${item.ip_owner}</p>`;
    }

    if (item.reg_no) {
        contentHtml += `<p><strong>Registration Number:</strong> ${item.reg_no}</p>`;
    }

    if (item.risk_level) {
        contentHtml += `<p><strong data-i18n="risk_level">Risk Level:</strong> ${item.risk_level}</p>`;
    }

    if (item.risk_description) {
        contentHtml += `<p><strong data-i18n="risk_description">Risk Description:</strong> ${item.risk_description}</p>`;
    }

    // Product image
    if (item.product_url) {
        contentHtml += `<p><strong data-i18n="product_image">Product Image:</strong><br>`;
        contentHtml += `<img src="${item.product_url}" class="uniform-img" style="max-width: 200px; margin: 10px 0;">`;
        contentHtml += `</p>`;
    }

    // IP images
    if (item.ip_asset_urls && Array.isArray(item.ip_asset_urls)) {
        contentHtml += `<p><strong data-i18n="ip_image">IP Image:</strong><br>`;
        item.ip_asset_urls.forEach(url => {
            contentHtml += `<img src="${url}" class="uniform-img" style="max-width: 200px; margin: 10px 5px;">`;
        });
        contentHtml += `</p>`;
    }

    // Report (if available)
    if (item.report) {
        const reportId = `reverse-report-${index}`;
        contentHtml += `
            <button type="button" class="show-report-button" data-report-id="${reportId}">
                ${isZh ? (i18n.zh.show_report || 'Show Report') : (i18n.en.show_report || 'Show Report')}
            </button>
            <div id="${reportId}" class="report-container" style="display: none;">
                <div class="report-text">
                    ${formatReportText(item.report)}
                </div>
            </div>
        `;
    }

    resultContent.innerHTML = contentHtml;
    resultSection.appendChild(resultContent);
    container.appendChild(resultSection);

    console.log('Added result section to container');
}

// Export functions for global access
window.initializeReverseCheckPage = initializeReverseCheckPage;
window.fetchReverseCheckResults = fetchReverseCheckResults;
window.displayReverseCheckList = displayReverseCheckList;
window.displayReverseCheckResultDetails = displayReverseCheckResultDetails;