/* Basic Reset & General Styles */
*, *:before, *:after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Roboto', sans-serif; /* Modern sans-serif font */
    color: #ffffff; /* White text */
    background-color: #77828d; /* Light grey background */
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    padding-top: 60px; /* Space for fixed header */
}

.container {
    /* max-width: 1800px; Increased from 1200px to 1400px */
    margin-left: 30px;
    margin-right: 60px; /* 20 + 30 for the gap between the 2 columns */
    margin-top: 30px;
    margin-bottom: 30px;
    padding: 20px;
}

h1, h2, h3 {
    font-weight: 700;
    margin-bottom: 15px;
    color: #ffffff; /* Darker header color */
}

h2 { font-size: 2.2em; }
h3 { font-size: 1.8em; }

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: #1E2442; /* White header background */
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    padding: 5px 20px;
    z-index: 100;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header .logo-title-container {
    display: flex;
    align-items: center;
}

.header .logo-title-container img {
    height: 40px;
    margin-right: 15px;
}

.header .studio-title {
    margin-bottom: 0;
}

.header nav ul {
    list-style: none;
    display: flex;
    align-items: center;
}

.header nav ul li {
    margin-left: 20px;
    display: inline-block;
}

.header nav a {
    text-decoration: none;
    color: #3498db; /* Accent color for links */
    font-weight: 500;
    transition: color 0.3s ease;
}

.header nav a:hover {
    color: #2980b9;
}


/* Main Content Layout (Flexbox) */
.main-content {
    display: flex;
    gap: 30px;
}


.left, .right {
    background-color: #1E2442;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

/* Input Form: narrower column */
.left {
    flex: 0 0 25%; /* Fixed basis: about 35% of the container */
}

/* Analysis Report: wider column */
.right {
    flex: 0 0 75%; /* Fixed basis: about 65% of the container */
}

/* Form Styles */
form {
    display: flex;
    flex-direction: column;
}

.form-group {
    margin-bottom: 30px;  /* Increased spacing between fields */
}

/* For fields not wrapped in .form-group */
#checkForm input[type="text"],
#checkForm input[type="file"],
#checkForm textarea,
#checkForm select {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: #bababa;
}

input[type="text"],
textarea,
select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1em;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

input[type="text"]:focus,
textarea:focus,
select:focus {
    border-color: #3498db;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
    outline: none;
}

input[type="file"] {
    background-color: #ffffff;
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1em;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

textarea {
    min-height: 150px;
}

/* Buttons */
button {
    padding: 12px 25px;
    border: none;
    border-radius: 4px;
    font-size: 1em;
    font-weight: 500;
    background-color: #3498db;
    color: white;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

button:hover {
    background-color: #2980b9;
    transform: scale(1.02);
}

button:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
}

button[type="button"] { /* Style for "Get Code" button if needed differently */
    background-color: #7f8c8d; /* Greyish button for secondary actions */
}

button[type="button"]:hover {
    background-color: #778384;
}

button:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}


/* Output Section */
#output {
    padding: 20px;
}

.result-summary {
    background-color: #ecf0f1; /* Light grey result summary background */
    color: #333;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.result-summary p {
    margin-bottom: 8px;
}

.result-card {
    background-color: #ffffff;
    color: #333;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.result-card h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #3498db;
}

.result-card p {
    margin-bottom: 8px;
    font-size: 0.95em;
}

.result-card strong {
    font-weight: 600;
}

.result-card img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin-top: 10px;
}


/* Spinner (adjust as needed) */
/* .spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: #3498db;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    display: inline-block;
    vertical-align: middle;
} */

.spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid #3498db;
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}


/* Modal styles */
.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto; /* 15% from the top and centered */
    padding: 20px;
    border: 1px solid #888;
    width: 80%; /* Could be more or less, depending on screen size */
    box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);
    animation-name: animatetop;
    animation-duration: 0.4s
}

/* Add Animation */
@keyframes animatetop {
    from {top:-300px; opacity:0}
    to {top:0; opacity:1}
}

.close-button {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close-button:hover,
.close-button:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

.modal-header {
    padding: 2px 16px;
    background-color: #ffffff;
    color: black;
    border-bottom: 1px solid #ccc;
}

.modal-body {padding: 20px 16px;}

.modal-footer {
    padding: 10px 16px;
    background-color: #ffffff;
    color: black;
    border-top: 1px solid #ccc;
    text-align: right;
}

/* Code block styling (basic) */
pre code.language-python {
    background-color: #282c34; /* Dark background for code */
    color: #f8f8f2; /* Light text color */
    padding: 15px;
    border-radius: 5px;
    overflow-x: auto; /* Horizontal scroll if code is too wide */
    display: block; /* Ensure block display for scroll */
    font-family: monospace;
    font-size: 14px;
}

/* --- Report Styles --- */
.show-report-button {
    padding: 8px 15px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #f9f9f9;
    cursor: pointer;
    font-size: 0.9em;
}

.show-report-button:hover {
    background-color: #eee;
}

.report-container {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    background-color: #fefefe;
    font-size: 1em; /* Ensure consistent base font size */
}

.report-text {
    white-space: pre-wrap;
    word-wrap: break-word;
    font-family: 'Roboto', sans-serif;
    font-size: 1em; /* Base font size */
    color: #333;
    background-color: #fefefe;
    padding: 0;
    margin: 0;
    border: none;
}

/* Ensure all text elements in report inherit the base size */
.report-container strong,
.report-container p,
.report-container li,
.report-container code {
    font-size: 1em !important; /* Force consistent font size */
    line-height: 1.6; /* Consistent line height */
}

.report-container strong {
    font-weight: bold;
    display: block;
    margin-top: 10px;
    margin-bottom: 5px;
    padding-left: 25px;
}

.report-container ul {
    list-style: none; /* Remove default bullets */
    padding-left: 25px; /* Indent list items */
}

.report-container li {
    list-style-type: none; /* Remove default bullet */
    position: relative;   /* Needed for ::before */
    padding-left: 25px; /* Add space between bullet and text */
}

.report-container li::before {
    content: "•"; /* Custom bullet point */
    position: absolute;
    left: 5px;
    color: #555; /* Bullet color */
    padding-right: 5px; /* Add space after bullet */
}

/* GetCodeButton styles */
#getCodeButton {
    background-color: #6ec1e4 !important;
    font-weight: 500 !important;
    color: #000000 !important;
    border-style: solid !important;
    border-width: 0 !important;
    border-radius: 30px !important;
    padding: 10px 20px !important;
    box-shadow: none !important;
    text-decoration: none !important;
    display: inline-block !important;
    transition: color 0.3s ease !important;
}

#getCodeButton:hover {
    color: white !important;
}

/* Responsive Design (Media Queries) */
@media screen and (max-width: 768px) {
    .main-content {
        flex-direction: column; /* Stack left and right columns on smaller screens */
    }

    .left, .right {
        width: 100%; /* Full width for columns on smaller screens */
        margin-bottom: 20px;
    }

    .header {
        flex-direction: column;
        align-items: flex-start; /* Align header items to the start on small screens */
    }

    .header nav ul {
        flex-direction: column;
        margin-top: 10px;
    }

    .header nav li {
        margin-left: 0;
        margin-bottom: 10px;
    }
}

.check-id {
    display: inline-block;
    background-color: #f7f7f7; /* Light grey background */
    color: #333;
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: 600;
    font-size: 1em;
    margin: 10px 0;
    border: 1px solid #ddd;
}

/* Reverse Check Page Specific Styles */
.reverse-check-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #3a4a6b;
    border-radius: 4px;
    padding: 10px;
    background-color: #2a3a5b;
}

.reverse-check-tree {
    font-size: 0.9em;
}

.date-group {
    margin-bottom: 15px;
}

.date-header {
    font-weight: bold;
    color: #3498db;
    cursor: pointer;
    padding: 5px 0;
    border-bottom: 1px solid #3a4a6b;
    margin-bottom: 8px;
}

.date-header:hover {
    color: #2980b9;
}

.check-id-list {
    margin-left: 15px;
}

.check-id-item {
    padding: 3px 8px;
    margin: 2px 0;
    cursor: pointer;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.check-id-item:hover {
    background-color: #3a4a6b;
}

.check-id-item.selected {
    background-color: #3498db;
    color: white;
}

.output-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.controls {
    display: flex;
    align-items: center;
}

.toggle-btn {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
    transition: background-color 0.3s ease;
}

.toggle-btn:hover {
    background-color: #2980b9;
}

/* Date input styling */
input[type="date"] {
    background-color: #2a3a5b;
    border: 1px solid #3a4a6b;
    color: #ffffff;
    padding: 10px;
    border-radius: 4px;
    font-size: 1em;
    width: 100%;
}

input[type="date"]:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
}

/* Loading state */
.loading {
    text-align: center;
    padding: 20px;
    color: #888;
}

.loading::after {
    content: "...";
    animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
    0%, 20% {
        color: rgba(255,255,255,0);
        text-shadow:
            .25em 0 0 rgba(255,255,255,0),
            .5em 0 0 rgba(255,255,255,0);
    }
    40% {
        color: white;
        text-shadow:
            .25em 0 0 rgba(255,255,255,0),
            .5em 0 0 rgba(255,255,255,0);
    }
    60% {
        text-shadow:
            .25em 0 0 white,
            .5em 0 0 rgba(255,255,255,0);
    }
    80%, 100% {
        text-shadow:
            .25em 0 0 white,
            .5em 0 0 white;
    }
}

/* Image Overlay */
.image-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.9);
    z-index: 1000;
    cursor: pointer;
    padding: 20px;
    animation: fadeIn 0.3s ease;
}

.image-overlay-content {
    position: relative;
    max-width: 75vw;
    max-height: 75vh;
    margin: auto;
    pointer-events: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.image-overlay img {
    width: 100%;
    height: 100%;
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0,0,0,0.3);
}

.close-overlay {
    position: absolute;
    top: 20px;
    right: 30px;
    color: white;
    font-size: 40px;
    font-weight: bold;
    transition: 0.3s;
    cursor: pointer;
    text-shadow: 0 2px 5px rgba(0,0,0,0.5);
    z-index: 1001;
}

.close-overlay:hover {
    color: #ccc;
    transform: scale(1.1);
}

@keyframes fadeIn {
    from {opacity: 0;}
    to {opacity: 1;}
}

html {
    scroll-padding-top: 80px; /* Match header height */
}

.uniform-img {
    width: 200px;
    height: 200px;
    object-fit: cover;
    margin-top: 5px;
    margin-right: 10px;
}

/* Add CSS for error messages */
.error-message {
    color: #721c24;
    background-color: #f8d7da;
    border-left: 4px solid #f5c6cb;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.error-message strong {
    display: block;
    margin-bottom: 8px;
}

.error-message .error-code {
    font-family: monospace;
    background-color: rgba(0,0,0,0.05);
    padding: 2px 4px;
    border-radius: 3px;
    margin-top: 8px;
    display: inline-block;
}

/* Add CSS for success/info messages */
.info-message {
    color: #0c5460;
    background-color: #d1ecf1;
    border-left: 4px solid #bee5eb;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
}

.required-field {
    color: red;
    margin-left: 4px;
    font-weight: bold;
}