// Utility functions

// Cookie management functions
function setCookie(name, value, days) {
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
}

function getCookie(name) {
    const nameEQ = name + "=";
    const ca = document.cookie.split(';');
    for (let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) === ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
    }
    return null;
}

// Report text formatting function
function formatReportText(originalText) {
    if (!originalText) return '';

    // 1) Remove double asterisks (but keep the text inside).
    originalText = originalText.replace(/\*\*(.*?)\*\*/g, '$1');

    // 2) Split into lines by actual newlines (\n).
    const lines = originalText.split('\n');

    let html = '';
    let currentIndent = 0; // how many <ul> we are "in"
    
    // Helper: count how many leading spaces are on a line
    function getIndentLevel(line) {
        const match = line.match(/^(s*)/);  // capture leading whitespace
        const spaceCount = match ? match[1].length : 0;
        // each 4 spaces = next nesting level
        return Math.floor(spaceCount / 4);
    }

    // Loop over each line and decide what to do:
    for (let i = 0; i < lines.length; i++) {
        let line = lines[i];
        // If the line is completely blank, just skip it
        if (!line.trim()) {
            continue;
        }

        // figure out if it's a bullet line: starts with some indentation, then '*'
        const bulletMatch = line.match(/^(s*)\*s*(.*)$/);
        const indentLevel = getIndentLevel(line);

        // close or open <ul> tags as needed
        if (indentLevel > currentIndent) {
            // we're going deeper into nested lists
            for (let n = currentIndent; n < indentLevel; n++) {
                html += '<ul>';
            }
        } else if (indentLevel < currentIndent) {
            // we're coming back up
            for (let n = currentIndent; n > indentLevel; n--) {
                html += '</ul>';
            }
        }
        currentIndent = indentLevel;

        if (bulletMatch) {
            // It's a bullet line
            const bulletText = bulletMatch[2].trim();
            
            // Check for colon in bullet text
            const colonIndex = bulletText.indexOf(':');
            if (colonIndex !== -1) {
                const label = bulletText.substring(0, colonIndex + 1);
                const value = bulletText.substring(colonIndex + 1).trim();
                html += `<li><strong>${label}</strong> ${value}</li>`;
            } else {
                html += `<li>${bulletText}</li>`;
            }
        } else {
            // Handle section headers and colon-separated labels
            const sectionHeaderMatch = line.match(/^(\d+\.\s+.*)/);
            if (sectionHeaderMatch) {
                // Left-align section headers with no indentation
                html += `<p style="margin-left: 0; padding-left: 0;"><strong>${sectionHeaderMatch[1].trim()}</strong></p>`;
            } else {
                const colonIndex = line.indexOf(':');
                if (colonIndex !== -1) {
                    const label = line.substring(0, colonIndex + 1).trim();
                    const value = line.substring(colonIndex + 1).trim();
                    html += `<p><strong>${label}</strong> ${value}</p>`;
                } else {
                    html += `<p>${line.trim()}</p>`;
                }
            }
        }
    }

    // if we still have open <ul>, close them
    while (currentIndent > 0) {
        html += '</ul>';
        currentIndent--;
    }

    return html;
}

// Close overlay function
function closeImageOverlay() {
    const imageOverlay = document.getElementById('imageOverlay');
    if (imageOverlay) {
        imageOverlay.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

// --- Shared Report Toggling Functionality ---

// Toggles the visibility of all report containers on the page
function toggleAllReports() {
    const reports = document.querySelectorAll('.report-container');
    const isZh = document.getElementById('language').value === 'zh';
    const toggleBtn = document.getElementById('toggleReportBtn');

    // Check if any report is currently visible to decide the action
    const shouldHide = Array.from(reports).some(report => report.style.display !== 'none');

    // Show or hide all reports
    reports.forEach(report => {
        report.style.display = shouldHide ? 'none' : 'block';
        const reportId = report.id;
        const button = document.querySelector(`[data-report-id="${reportId}"]`);
        if (button) {
            button.textContent = shouldHide 
                ? (isZh ? (i18n.zh.show_report || 'Show Report') : (i18n.en.show_report || 'Show Report'))
                : (isZh ? (i18n.zh.hide_report || 'Hide Report') : (i18n.en.hide_report || 'Hide Report'));
        }
    });

    // Update the main toggle button's text
    if (toggleBtn) {
        toggleBtn.textContent = shouldHide
            ? (isZh ? (i18n.zh.show_report || 'Show Report') : (i18n.en.show_report || 'Show Report'))
            : (isZh ? (i18n.zh.hide_report || 'Hide Report') : (i18n.en.hide_report || 'Hide Report'));
    }
}

// Initializes all event listeners related to report toggling
function initializeReportToggling() {
    // Event listener for the global "Toggle Report" button
    const toggleReportBtn = document.getElementById('toggleReportBtn');
    if (toggleReportBtn) {
        toggleReportBtn.addEventListener('click', toggleAllReports);
    }

    // Delegated event listener for individual "Show/Hide Report" buttons
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('show-report-button')) {
            const reportId = e.target.dataset.reportId;
            const reportContainer = document.getElementById(reportId);
            const isZh = document.getElementById('language').value === 'zh';
            
            if (reportContainer) {
                const isHidden = reportContainer.style.display === 'none';
                reportContainer.style.display = isHidden ? 'block' : 'none';
                e.target.textContent = isHidden
                    ? (isZh ? (i18n.zh.hide_report || 'Hide Report') : (i18n.en.hide_report || 'Hide Report'))
                    : (isZh ? (i18n.zh.show_report || 'Show Report') : (i18n.en.show_report || 'Show Report'));
            }
        }
    });
}


// Export functions for global access
window.setCookie = setCookie;
window.getCookie = getCookie;
window.formatReportText = formatReportText;
window.closeImageOverlay = closeImageOverlay;
window.initializeReportToggling = initializeReportToggling; // Export the new initializer
