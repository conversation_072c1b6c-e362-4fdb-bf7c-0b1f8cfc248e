#!/usr/bin/env python
import requests
import time
import json
import os
import base64

# API endpoint info
BASE_URL = "https://api.maidalv.com" 
CHECK_API_ENDPOINT = f"{BASE_URL}/check_api"
CHECK_STATUS_ENDPOINT = f"{BASE_URL}/check_status"

# Sample data
API_KEY = "your_api_key_here"  # Replace with your actual API key
LOCAL_IMAGE_PATH = "d:\\9547696832542.webp"
IP_IMAGE_URLS = ["https://www.chanel.com/images///f_auto,q_auto:good,dpr_1.1/w_3200/-9547696832542.jpg"]
DESCRIPTION = "a chanel hand bag"
IP_KEYWORDS = ["chanel", "handbag", "luxury", "fashion"]

"""
API Parameter Reference:
- api_key (REQUIRED): String - Your API authentication key
- main_product_image (REQUIRED): String - Either a URL or base64-encoded image data
- other_product_images (OPTIONAL): List - Array of URLs or base64-encoded image data
- ip_images (OPTIONAL): List - Array of URLs or base64-encoded image data
- ip_keywords (OPTIONAL): List - Array of keyword strings for IP search
- description (REQUIRED): String - Text description of the product
- reference_text (OPTIONAL): String - Additional reference text
- reference_images (OPTIONAL): List - Array of URLs or base64-encoded reference images
"""

def submit_check_request():
    """Submit a check request to the API with local main image and URL IP image"""
    print("Preparing to submit check request...")
    
    with open(LOCAL_IMAGE_PATH, "rb") as image_file:
        main_image_base64 = base64.b64encode(image_file.read()).decode('utf-8')

    payload = {
        "api_key": API_KEY,              # REQUIRED
        "main_product_image": main_image_base64,  # REQUIRED - Base64 encoded local image
        "ip_images": IP_IMAGE_URLS,     # OPTIONAL - Must be a list, even for a single URL
        "ip_keywords": IP_KEYWORDS,      # OPTIONAL - List of keyword strings
        "description": DESCRIPTION,      # REQUIRED
        # Optional parameters (not included in this example)
        # "other_product_images": [],    # OPTIONAL
        # "reference_text": "",          # OPTIONAL
        # "reference_images": [],        # OPTIONAL
    }
    
    # Submit the request
    try:
        print("Submitting request to API...")
        response = requests.post(CHECK_API_ENDPOINT, json=payload)
        response.raise_for_status()  # Raise exception for 4XX/5XX responses
        
        result = response.json()
        check_id = result.get('check_id')
        
        print(f"Request submitted successfully! Check ID: {check_id}")
        return check_id

    except requests.exceptions.RequestException as e:
        print(f"Error submitting request: {str(e)}")
        if hasattr(response, 'text'):
            print(f"Response text: {response.text}")
        return None

def poll_check_status(check_id):
    """Poll for check status every 15 seconds until complete"""
    print(f"Starting to poll for status of check_id: {check_id}")
    
    while True:
        try:
            status_url = f"{CHECK_STATUS_ENDPOINT}/{check_id}"
            response = requests.get(status_url)
            response.raise_for_status()
            
            result = response.json()
            status = result.get('status')
            
            # Display time remaining and message if available
            if status == 'processing':
                # Extract estimated completion time if available
                estimated_time = result.get('estimated_completion_time', 'unknown')
                queue_position = result.get('queue_position', 'unknown')
                print(f"Status: {status} | Estimated time: {estimated_time} | Queue position: {queue_position}")
            elif status == 'completed':
                print("Check completed successfully!")
                # Print the analysis results in a formatted way
                print_results(result)
                return result
            elif status == 'error':
                print(f"Error in processing: {result.get('message')}")
                return result
            else:
                print(f"Unknown status: {status} | Full response: {result}")
            
            # Wait 15 seconds before next poll
            print("Waiting 15 seconds before next check...")
            time.sleep(15)
            
        except requests.exceptions.RequestException as e:
            print(f"Error checking status: {str(e)}")
            print("Retrying in 15 seconds...")
            time.sleep(15)

def print_results(answer):
    """
    Print the API response results in a well-formatted, readable structure
    similar to how the results are displayed in the HTML interface.
    """
    print("\n" + "="*50)
    print(f"🔍 STATUS: {answer.get('status', 'N/A')}")
    result = answer.get('result', {})
    print(f"🔍 CHECK ID: {result.get('check_id', 'N/A')}")
    print("="*50)
    
    # Print overall risk level
    print(f"\n📊 OVERALL RISK LEVEL: {result.get('risk_level', 'N/A')}")
    
    # Print detailed results
    if 'results' in result and isinstance(result['results'], list):
        for i, res in enumerate(result['results']):
            print(f"\n{'='*25} RESULT {i+1} {'='*25}")
            print(f"🏷️  Type: {res.get('type', 'N/A')}")
            print(f"👤 IP Owner: {res.get('ip_owner', 'N/A')}")
            
            # Use result-specific risk level if available, otherwise use overall
            risk_level = res.get('risk_level', result.get('risk_level', 'N/A'))
            print(f"⚠️  Risk Level: {risk_level}")
            
            print(f"📝 Risk Description: {res.get('risk_description', 'N/A')}")
            
            # Print litigation information if available
            if 'number_of_cases' in res:
                print(f"⚖️  Number of Cases: {res.get('number_of_cases')}")
            
            if 'last_case_date_filed' in res:
                print(f"📅 Last Case Date: {res.get('last_case_date_filed')}")
                
            if 'last_case_docket' in res:
                print(f"📂 Last Case Docket: {res.get('last_case_docket')}")
            
            # Print IP images info
            if 'ip_image' in res:
                ip_images = res['ip_image']
                # Handle different formats
                if isinstance(ip_images, str):
                    if ip_images.startswith('['):
                        try:
                            ip_images = json.loads(ip_images)
                        except:
                            ip_images = [ip_images]
                    else:
                        ip_images = [ip_images]
                
                if isinstance(ip_images, list):
                    print(f"🖼️  IP Images: {len(ip_images)} image(s)")
                    for img in ip_images:
                        print(f"   - {img}")
            
            # Print product images info
            if 'product_url' in res:
                product_images = res['product_url']
                # Handle different formats
                if isinstance(product_images, str):
                    if product_images.startswith('['):
                        try:
                            product_images = json.loads(product_images)
                        except:
                            product_images = [product_images]
                    else:
                        product_images = [product_images]
                
                if isinstance(product_images, list):
                    print(f"📷 Product Images: {len(product_images)} image(s)")
                    for img in product_images:
                        # Truncate data URLs to prevent overflow
                        if isinstance(img, str) and img.startswith('data:image'):
                            print(f"   - [Base64 encoded image]")
                        else:
                            print(f"   - {img}")
            
            # Print report summary (not the full report as it can be very long)
            if 'report' in res:
                report = res.get('report_en', res.get('report', ''))
                if report:
                    print(f"\n📃 REPORT SUMMARY: ")
                    # Show first 100 chars of report as preview
                    print(f"   {report[:100]}... [truncated]")
                    print("   (Full report available in the complete response)")
    else:
        # If no results array, print whatever is available
        print("\nRESPONSE DETAILS:")
        for key, value in result.items():
            if key != 'check_id' and key != 'risk_level':
                print(f"- {key}: {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}")
    
    print("\n" + "="*50)
    print("For full details, use the JSON output or build your own interface")
    print("="*50)

if __name__ == "__main__":
    print("=== API DEMO SCRIPT ===")
    
    # Submit the check request
    check_id = submit_check_request()
    
    if check_id:
        # Poll for results
        result = poll_check_status(check_id)
        print("\n=== DEMO COMPLETED ===")
    else:
        print("Failed to submit check request. Demo terminated.")
