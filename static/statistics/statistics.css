/* Statistics Page Styles */

.statistics-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-lg);
}

.statistics-container h1 {
    margin-bottom: var(--spacing-lg);
    color: var(--text-color);
}

/* Controls Section */
.controls-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
}

.time-filter {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.time-filter label {
    margin-bottom: 0;
    color: var(--text-secondary);
}

.time-filter select {
    min-width: 120px;
}

/* Statistics Grid */
.statistics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

/* Statistic Card */
.statistic-card {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow);
    transition: transform 0.2s, box-shadow 0.2s;
    cursor: pointer;
}

.statistic-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

.statistic-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.statistic-title {
    font-size: var(--heading-size);
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
    flex: 1;
}

.statistic-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--accent-color);
    margin-left: var(--spacing-md);
}

.statistic-value.warning {
    color: var(--warning-color);
}

.statistic-value.error {
    color: var(--error-color);
}

.statistic-description {
    font-size: var(--text-size);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.4;
}

.statistic-chart {
    height: 120px;
    margin-top: var(--spacing-md);
    background-color: var(--input-background);
    border-radius: var(--border-radius-sm);
    position: relative;
}

.chart-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--text-secondary);
    font-size: var(--detail-size);
}

.statistic-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-sm);
    border-top: 1px solid var(--border-color);
}

.statistic-timestamp {
    font-size: var(--detail-size);
    color: var(--text-secondary);
}

.details-btn {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--detail-size);
    background-color: var(--accent-color);
    color: white;
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: background-color 0.2s;
}

.details-btn:hover {
    background-color: var(--accent-hover);
}

/* Chart Styles */
.chart-line {
    fill: none;
    stroke: var(--accent-color);
    stroke-width: 2px;
}

.chart-area {
    fill: var(--accent-color);
    opacity: 0.1;
}

.chart-dot {
    fill: var(--accent-color);
    stroke: var(--card-background);
    stroke-width: 2px;
    r: 3px;
}

.chart-dot:hover {
    r: 5px;
    fill: var(--accent-hover);
}

.chart-axis {
    stroke: var(--border-color);
    stroke-width: 1px;
}

.chart-axis text {
    fill: var(--text-secondary);
    font-size: 10px;
}

.chart-grid {
    stroke: var(--border-color);
    stroke-width: 0.5px;
    opacity: 0.3;
}

/* Details Modal Styles */
.overlay-content {
    max-width: 90vw;
    max-height: 90vh;
    width: 800px;
}

.details-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: var(--spacing-md);
}

.details-table th,
.details-table td {
    padding: var(--spacing-sm);
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: var(--text-size);
}

.details-table th {
    background-color: rgba(0, 0, 0, 0.2);
    font-weight: 600;
    color: var(--text-color);
}

.details-table tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.details-table a {
    color: var(--accent-color);
    text-decoration: none;
}

.details-table a:hover {
    color: var(--accent-hover);
    text-decoration: underline;
}

.details-summary {
    background-color: var(--input-background);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-sm);
    margin-bottom: var(--spacing-md);
}

.details-summary h4 {
    margin: 0 0 var(--spacing-sm) 0;
    color: var(--text-color);
}

.details-summary p {
    margin: 0;
    color: var(--text-secondary);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--text-secondary);
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    margin: var(--spacing-lg) 0;
}

.empty-state h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

/* Responsive Design */
@media (max-width: 768px) {
    .statistics-grid {
        grid-template-columns: 1fr;
    }
    
    .controls-section {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .statistic-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .statistic-value {
        margin-left: 0;
        margin-top: var(--spacing-xs);
    }
    
    .overlay-content {
        width: 95vw;
        margin: var(--spacing-sm);
    }
}

@media (max-width: 480px) {
    .statistics-container {
        padding: var(--spacing-md);
    }
    
    .statistic-card {
        padding: var(--spacing-md);
    }
    
    .statistic-chart {
        height: 80px;
    }
    
    .details-table {
        font-size: var(--detail-size);
    }
    
    .details-table th,
    .details-table td {
        padding: var(--spacing-xs);
    }
}

/* Animation for loading states */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.loading-card {
    animation: pulse 1.5s ease-in-out infinite;
}

/* Metric-specific styling */
.metric-cases_without_cause .statistic-value { color: var(--warning-color); }
.metric-cases_without_images .statistic-value { color: var(--warning-color); }
.metric-cases_with_corrupted_image_data .statistic-value { color: var(--error-color); }
.metric-duplicate_cases .statistic-value { color: var(--error-color); }
.metric-duplicate_plaintiffs .statistic-value { color: var(--error-color); }
.metric-pictures_in_cos_not_in_dataframe .statistic-value { color: var(--warning-color); }
.metric-cases_with_pictures_missing_from_cos .statistic-value { color: var(--error-color); }
