/* === Base Styles (Derived from visualizer/boundingbox) === */
:root {
    --background-color: #242424;
    --card-background: #2e2e2e;
    --input-background: #1a1a1a;
    --image-box-background: #383838;
    --text-color: #f0f0f0;
    --text-secondary: #b0b0b0;
    --accent-color: #3f6dc2;
    --accent-hover: #4f7ed2;
    --error-color: #e74c3c;
    --error-background: rgba(231, 76, 60, 0.1);
    --success-color: #2ecc71;
    --success-background: rgba(46, 204, 113, 0.2);
    --warning-color: #f39c12;
    --warning-background: rgba(243, 156, 18, 0.1);
    --info-color: #3498db; /* Existing button color */
    --info-background: rgba(52, 152, 219, 0.2);
    --border-color: #444;
    --border-radius: 8px;
    --border-radius-sm: 4px;
    --shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --heading-size: 14px;
    --text-size: 12px;
    --detail-size: 10px;
    --overlay-background: rgba(0, 0, 0, 0.75);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.4;
    font-size: var(--text-size);
    margin: 0; /* Override previous margin */
    padding: var(--spacing-md); /* Add some padding */
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-color);
}
h1 { font-size: 1.5rem; }
h2 { font-size: 1.25rem; } /* Adjusted */
h3 { font-size: 1.1rem; } /* Adjusted */

a {
    color: var(--accent-color);
    text-decoration: none;
    transition: color 0.2s;
}
a:hover {
    color: var(--accent-hover);
    text-decoration: underline;
}

/* === Layout Helpers === */
.container, /* For api_studio */
.app-container { /* For visualizer/boundingbox */
    max-width: 100%;
    margin: 0 auto;
    padding: 0; /* Remove padding, handled by body or specific containers */
}

.button-row,
.actions-row { /* Common pattern */
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    gap: var(--spacing-md);
    flex-wrap: wrap; /* Allow wrapping */
}

.button-group,
.action-buttons { /* Common pattern */
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap; /* Allow wrapping */
}

/* === Buttons === */
button,
.button, /* From original common.css */
.btn { /* From visualizer/boundingbox */
    display: inline-block; /* Ensure consistent behavior */
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--text-size); /* Standardized */
    font-weight: 500;
    border: none;
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s, color 0.2s;
    background-color: var(--accent-color); /* Default to primary */
    color: white;
    text-decoration: none; /* For .button links */
    text-align: center;
    vertical-align: middle;
}

button:hover,
.button:hover,
.btn:hover {
    background-color: var(--accent-hover);
    color: white; /* Ensure hover maintains text color */
    text-decoration: none; /* Remove underline on hover for button-like links */
}

button:active,
.button:active,
.btn:active {
    transform: translateY(1px);
}

button:disabled,
.button:disabled,
.btn:disabled {
    background-color: #333;
    color: #777;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Button Variants */
.btn.primary { /* Explicit primary */
    background-color: var(--accent-color);
    color: white;
}
.btn.primary:hover {
    background-color: var(--accent-hover);
}

.btn.secondary {
    background-color: #444;
    color: white;
}
.btn.secondary:hover {
    background-color: #555;
}

.btn.success {
    background-color: var(--success-color);
    color: white;
}
.btn.success:hover {
    background-color: #27ae60; /* Darker success */
}

.btn.error {
    background-color: var(--error-color);
    color: white;
}
.btn.error:hover {
    background-color: #c0392b; /* Darker error */
}

.btn.warning {
    background-color: var(--warning-color);
    color: #333; /* Darker text for better contrast */
}
.btn.warning:hover {
    background-color: #e67e22; /* Darker warning */
}

.btn.info { /* For plaintiffs.css btn-info */
    background-color: #17a2b8; /* Specific info color */
    color: white;
}
.btn.info:hover {
    background-color: #138496; /* Darker info */
}

.btn.small, /* From boundingbox */
.btn-sm { /* From plaintiffs */
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: var(--detail-size);
}

/* === Forms === */
.form-group, /* From api_studio/boundingbox */
.filter-group { /* From visualizer/boundingbox */
    display: flex;
    flex-direction: column;
    margin-bottom: var(--spacing-md); /* Consistent spacing */
}

label {
    display: block; /* Ensure label is on its own line */
    margin-bottom: var(--spacing-xs);
    font-size: var(--heading-size);
    font-weight: 500;
    color: var(--text-secondary); /* Consistent label color */
}

input[type="text"],
input[type="date"], /* From original common.css */
input[type="number"],
input[type="email"],
input[type="password"],
textarea,
select {
    background-color: var(--input-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm);
    color: var(--text-color);
    font-size: var(--text-size);
    width: 100%;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

input[type="text"]:focus,
input[type="date"]:focus,
input[type="number"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(63, 109, 194, 0.3); /* Focus ring */
}

textarea {
    min-height: 100px; /* Reasonable default */
    line-height: 1.4;
}

select[multiple] {
    height: 100px; /* Increased default height */
    overflow-y: auto;
}

select[multiple] option {
    padding: var(--spacing-xs) var(--spacing-sm);
}

select[multiple] option:checked {
    background-color: rgba(63, 109, 194, 0.5);
    color: white;
}

input[type="file"] {
    background-color: var(--input-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm);
    color: var(--text-color);
    font-size: var(--text-size);
    width: 100%;
    /* Basic file input styling, browser defaults vary */
}

/* Checkboxes & Radios */
.checkbox-wrapper, /* From original common.css */
.checkbox-label, /* From visualizer/boundingbox */
.checkbox-option, /* From visualizer/boundingbox overlay */
.radio-option { /* From visualizer/boundingbox overlay */
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-xs);
    cursor: pointer;
    font-size: var(--text-size);
}

.checkbox-wrapper input[type="checkbox"],
.checkbox-label input[type="checkbox"],
.checkbox-option input[type="checkbox"],
.radio-option input[type="radio"] {
    width: auto; /* Don't force 100% width */
    margin: 0; /* Reset margin */
    flex-shrink: 0; /* Prevent shrinking */
    cursor: pointer;
}

.checkbox-wrapper label,
.checkbox-label label,
.checkbox-option .option-label, /* Target nested label if exists */
.radio-option .option-label {
    margin-bottom: 0; /* Remove margin for inline labels */
    font-weight: normal; /* Normal weight for checkbox/radio text */
    color: var(--text-color); /* Ensure text color */
}

.required-field { /* From api_studio */
    color: var(--error-color);
    margin-left: 4px;
    font-weight: bold;
}

/* === Cards === */
.card {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    box-shadow: var(--shadow);
    /* margin-bottom: var(--spacing-lg); Add default margin */
    transform: translateZ(0); /* Optimization hint */
    transition: transform 0.2s, box-shadow 0.2s; /* Added box-shadow transition */
}

.case:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Fallback shadow */
}

/* === Tables === */

.table-container {
    margin-top: var(--spacing-lg);
}

table.common-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--card-background); /* Often tables are in cards */
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden; /* For border-radius */
    font-size: var(--text-size);
    margin-bottom: var(--spacing-lg);
}

table.common-table th,
table.common-table td {
    text-align: left;
    padding: var(--spacing-sm); /* Consistent padding */
    border-bottom: 1px solid var(--border-color);
    vertical-align: top; /* Align content to top */
}

table.common-table th {
    background-color: rgba(0, 0, 0, 0.2);
    font-weight: 600;
    color: var(--text-color);
    position: sticky; /* Make headers sticky */
    top: 0;
    z-index: 10;
}

table.common-table tr:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.02);
}

table.common-table tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* === Overlays / Modals === */
.overlay {
    display: none; /* Hidden by default */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--overlay-background);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-lg); /* Padding around content */
    overflow-y: auto; /* Allow scrolling if content overflows */
}


.overlay.show { /* Class to show the overlay */
    display: flex;
}

.overlay-content {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    width: auto; /* Let content define width */
    max-width: 90%; /* Limit max width */
    max-height: 90vh; /* Limit max height */
    overflow-y: auto; /* Scroll inside content if needed */
    box-shadow: var(--shadow);
    position: relative; /* For close button positioning */
}

.overlay-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color);
}

.overlay-title {
    font-size: 1.1rem; /* Consistent with h3 */
    font-weight: 600;
    margin: 0; /* Remove default margin */
}

.close-overlay, /* Generic close button */
.close-button { /* From api_studio modal */
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 24px; /* Larger click target */
    line-height: 1;
    padding: 0;
}
.close-overlay:hover,
.close-button:hover {
    color: var(--text-color);
}

.overlay-body {
    margin-bottom: var(--spacing-lg);
}

.overlay-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
    padding-top: var(--spacing-sm);
    border-top: 1px solid var(--border-color);
}


/* === Utility Classes === */
.loading,
.no-results {
    text-align: center;
    padding: var(--spacing-lg);
    color: var(--text-secondary);
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
}

.truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%; /* Ensure it doesn't overflow container */
    display: inline-block; /* Or block depending on context */
    vertical-align: middle; /* Align nicely with text */
}
/* Hover effect for truncate needs JS or specific parent context */

.error-message,
.action-result.error, /* plaintiffs */
.merge-result.error { /* plaintiff_duplicates */
    color: var(--error-color);
    background-color: var(--error-background);
    border-left: 4px solid var(--error-color);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    border-radius: var(--border-radius-sm);
}

.success-message,
.action-result.success, /* plaintiffs */
.merge-result.success { /* plaintiff_duplicates */
    color: var(--success-color);
    background-color: var(--success-background);
    border-left: 4px solid var(--success-color);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    border-radius: var(--border-radius-sm);
}

.info-message,
.action-result.info, /* plaintiffs */
.merge-result.loading { /* plaintiff_duplicates - treat loading as info */
    color: var(--info-color);
    background-color: var(--info-background);
    border-left: 4px solid var(--info-color);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    border-radius: var(--border-radius-sm);
}

.warning-message {
    color: var(--warning-color);
    background-color: var(--warning-background);
    border-left: 4px solid var(--warning-color);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
    border-radius: var(--border-radius-sm);
}

/* === Original common.css Components (Adapted) === */

/* Progress Bar (Steps) */
#progressBar {
    display: flex;
    margin: var(--spacing-lg) 0;
    border-radius: var(--border-radius-sm);
    overflow: hidden; /* Ensure rounded corners */
    border: 1px solid var(--border-color);
}

.step {
    flex: 1;
    padding: var(--spacing-sm);
    text-align: center;
    border-left: 1px solid var(--border-color);
    cursor: pointer;
    font-size: var(--text-size);
    transition: background-color 0.3s;
}
.step:first-child {
    border-left: none;
}

.step.pending { background-color: #444; }
.step.running { background-color: var(--warning-color); color: #333; }
.step.completed { background-color: var(--success-color); color: white; }
.step.failed { background-color: var(--error-color); color: white; }
.step:hover { background-color: #555; }

/* Logs Container */
#logsContainer {
    height: 60vh; /* Adjusted height */
    overflow-y: auto;
    border: 1px solid var(--border-color);
    padding: var(--spacing-sm);
    margin-top: var(--spacing-lg);
    background-color: var(--input-background);
    border-radius: var(--border-radius-sm);
}

#logs {
    font-family: monospace;
    white-space: pre-wrap;
    word-wrap: break-word;
    font-size: var(--text-size);
    color: var(--text-color);
}

/* Progress Bar (General) */
.progress {
    height: 20px; /* Slimmer progress bar */
    margin-bottom: var(--spacing-lg);
    overflow: hidden;
    background-color: #333; /* Darker background */
    border-radius: var(--border-radius-sm);
    box-shadow: inset 0 1px 2px rgba(0,0,0,.2);
}

.progress-bar {
    float: left;
    width: 0%;
    height: 100%;
    font-size: var(--text-size);
    line-height: 20px; /* Match height */
    color: white;
    text-align: center;
    background-color: var(--accent-color);
    box-shadow: inset 0 -1px 0 rgba(0,0,0,.15);
    transition: width .6s ease;
}

/* Spinner (from api_studio) */
.spinner {
    border: 4px solid rgba(255, 255, 255, 0.1); /* Lighter border */
    border-radius: 50%;
    border-top: 4px solid var(--accent-color);
    width: 24px;
    height: 24px;
    animation: spin 1s linear infinite;
    display: inline-block; /* Allow inline placement */
    vertical-align: middle; /* Align with text */
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* === Main Menu === */
.main-menu {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    padding: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow);
}

.menu-items {
    display: flex;
    list-style-type: none;
    margin-bottom: var(--spacing-md);
}

.menu-item {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    transition: background-color 0.2s;
}

.menu-item a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    font-size: 14px; /* Specific font size for menu */
}

.menu-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.menu-item.active {
    background-color: var(--accent-color);
}

.menu-item.active a {
    color: white;
}

/* Filters */
.filters-container {
    background-color: var(--card-background);
    border-radius: var(--border-radius);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--shadow);
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(135px, 1fr));
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    margin-bottom: var(--spacing-xs);
    font-size: var(--heading-size);
    font-weight: 500;
}

input, select {
    background-color: #1a1a1a;
    border: 1px solid #444;
    border-radius: 4px;
    padding: var(--spacing-sm);
    color: var(--text-color);
    font-size: var(--text-size);
    width: 100%;
}

input:focus, select:focus {
    outline: none;
    border-color: var(--accent-color);
}

/* Multi-select styling */
select[multiple] {
    height: 80px;
    overflow-y: auto;
}

select[multiple] option {
    padding: var(--spacing-xs) var(--spacing-sm);
}

select[multiple] option:checked {
    background-color: rgba(63, 109, 194, 0.5);
    color: white;
}

.actions-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.action-buttons {
    display: flex;
    gap: var(--spacing-md);
}

.results-info {
    color: var(--text-secondary);
    font-size: var(--text-size);
}

.btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s, transform 0.1s;
}

.btn:active {
    transform: translateY(1px);
}

.primary {
    background-color: var(--accent-color);
    color: white;
}

.primary:hover {
    background-color: var(--accent-hover);
}

.secondary {
    background-color: #444;
    color: white;
}

.secondary:hover {
    background-color: #555;
}

.btn:disabled {
    background-color: #333;
    color: #777;
    cursor: not-allowed;
}

/* === Common Styles from Visualizer/TRO_IP === */

/* Container for case/plaintiff cards */
.cases-container { /* Renamed from plaintiffs-ip-container */
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

/* Common Card Header Layout */
.case-header, /* From visualizer */
.plaintiff-header { /* From tro_ip */
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

/* Common Info Item Layout */
.info-item {
  margin-bottom: 0; /* Override potential card margins */
  display: flex;
  align-items: center;
  gap: var(--spacing-xs); /* Add gap */
}

.info-label {
  font-weight: 500;
  color: var(--text-secondary);
  /* margin-right removed, using gap */
}

.info-value {
  text-align: left;
  word-break: break-word;
}

/* Common IP Section Layout */
.ip-section {
    margin-top: var(--spacing-md);
}

.ip-title {
    font-size: var(--heading-size);
    margin-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: var(--spacing-xs);
    display: flex;
    justify-content: space-between;
    align-items: center; /* Align items vertically */
}

.ip-title-text {
    margin-right: var(--spacing-sm);
    font-weight: 600; /* Make title text bold */
}

.ip-status {
    font-size: var(--detail-size);
    color: var(--text-secondary);
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.ip-status-item {
    background-color: rgba(255, 255, 255, 0.1);
    padding: 2px 6px;
    border-radius: var(--border-radius-sm);
    white-space: nowrap;
}

.ip-status-item-count {
    font-weight: bold;
}

.ip-items {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: var(--spacing-md);
}

.ip-item {
    background-color: var(--image-box-background);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-sm);
    height: 100%; /* Ensure items fill height if needed */
    display: flex; /* Use flex for better internal alignment */
    flex-direction: column;
}

.ip-item-image {
    width: 100%;
    height: 140px;
    object-fit: contain;
    margin-bottom: var(--spacing-sm);
    background-color: #222; /* Consider making this a variable if needed */
    border-radius: var(--border-radius-sm);
    cursor: pointer; /* Add cursor for clickable images */
}

.ip-item-info {
    font-size: var(--detail-size);
    flex-grow: 1; /* Allow info to take remaining space */
}

.ip-item-info div { /* Style for label/value pairs inside info */
    margin-bottom: var(--spacing-xs);
    line-height: 1.3; /* Improve readability */
}

.ip-item-label {
    color: var(--text-secondary);
    font-weight: 500;
    margin-right: 4px;
}

/* Common Pagination Controls */
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

/* Common Responsive Adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
    .ip-items {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

/* Image Preview Overlay (Ensure this matches common.js implementation) */
#image-preview-overlay {
    display: none; /* Hidden by default */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--overlay-background);
    z-index: 1100; /* Higher than other overlays */
    justify-content: center;
    align-items: center;
    cursor: pointer; /* Close on click outside image */
}

#image-preview-overlay.show {
    display: flex;
}

#image-preview-content {
    max-width: 90%;
    max-height: 90%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

#image-preview-img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: var(--border-radius-sm);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    cursor: default; /* Don't close when clicking image itself */
}

#image-preview-close {
    position: absolute;
    top: -10px;
    right: -10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    font-size: 18px;
    line-height: 30px;
    text-align: center;
    cursor: pointer;
    transition: background-color 0.2s;
}

#image-preview-close:hover {
    background-color: rgba(0, 0, 0, 0.9);
}
