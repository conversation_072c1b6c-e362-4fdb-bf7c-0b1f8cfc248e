import os
import psycopg2
import psycopg2.extras
import pandas as pd
from datetime import datetime, timedelta
import json
from logdata import log_message

def get_postgres_connection():
    """
    Get a connection to the PostgreSQL database using environment variables.
    
    Returns:
        A connection to the PostgreSQL database.
    """
    try:
        conn = psycopg2.connect(
            host=os.getenv("POSTGRES_HOST"),
            port=os.getenv("POSTGRES_PORT"),
            user=os.getenv("POSTGRES_USER"),
            password=os.getenv("POSTGRES_PASSWORD"),
            dbname=os.getenv("POSTGRES_DB")
        )
        conn.autocommit = True
        return conn
    except Exception as e:
        log_message(f"Error connecting to PostgreSQL: {str(e)}", level='ERROR')
        raise

def init_statistics_table():
    """
    Initialize the statistics_log table if it doesn't exist.
    """
    try:
        conn = get_postgres_connection()
        cursor = conn.cursor()
        
        # Create the statistics_log table
        create_table_query = """
        CREATE TABLE IF NOT EXISTS statistics_log (
            id SERIAL PRIMARY KEY,
            report_timestamp TIMESTAMPTZ NOT NULL,
            metric_name VARCHAR(255) NOT NULL,
            metric_value INTEGER NOT NULL,
            details_preview JSONB NULL,
            created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        cursor.execute(create_table_query)
        
        # Create indexes for better performance
        create_indexes_query = """
        CREATE INDEX IF NOT EXISTS idx_statistics_log_timestamp ON statistics_log(report_timestamp);
        CREATE INDEX IF NOT EXISTS idx_statistics_log_metric_name ON statistics_log(metric_name);
        CREATE INDEX IF NOT EXISTS idx_statistics_log_timestamp_metric ON statistics_log(report_timestamp, metric_name);
        """
        
        cursor.execute(create_indexes_query)
        
        cursor.close()
        conn.close()
        
        log_message("Statistics table initialized successfully", level='INFO')
        
    except Exception as e:
        log_message(f"Error initializing statistics table: {str(e)}", level='ERROR')
        raise

def store_statistics_batch(statistics_data, report_timestamp=None):
    """
    Store multiple statistics in the database.
    
    Args:
        statistics_data: List of dictionaries with keys: metric_name, metric_value, details_preview
        report_timestamp: Timestamp for the report (defaults to current time)
    """
    if report_timestamp is None:
        report_timestamp = datetime.now()
    
    try:
        conn = get_postgres_connection()
        cursor = conn.cursor()
        
        # Prepare the insert query
        insert_query = """
        INSERT INTO statistics_log (report_timestamp, metric_name, metric_value, details_preview)
        VALUES (%s, %s, %s, %s)
        """
        
        # Prepare the data for batch insert
        batch_data = []
        for stat in statistics_data:
            details_json = json.dumps(stat.get('details_preview')) if stat.get('details_preview') else None
            batch_data.append((
                report_timestamp,
                stat['metric_name'],
                stat['metric_value'],
                details_json
            ))
        
        # Execute batch insert
        cursor.executemany(insert_query, batch_data)
        
        cursor.close()
        conn.close()
        
        log_message(f"Successfully stored {len(statistics_data)} statistics", level='INFO')
        
    except Exception as e:
        log_message(f"Error storing statistics: {str(e)}", level='ERROR')
        raise

def get_statistics_history(metric_name=None, days_back=30):
    """
    Retrieve statistics history from the database.
    
    Args:
        metric_name: Specific metric to retrieve (None for all metrics)
        days_back: Number of days to look back
        
    Returns:
        pandas.DataFrame with statistics data
    """
    try:
        conn = get_postgres_connection()
        
        # Build the query
        base_query = """
        SELECT report_timestamp, metric_name, metric_value, details_preview
        FROM statistics_log
        WHERE report_timestamp >= %s
        """
        
        params = [datetime.now() - timedelta(days=days_back)]
        
        if metric_name:
            base_query += " AND metric_name = %s"
            params.append(metric_name)
        
        base_query += " ORDER BY report_timestamp DESC, metric_name"
        
        # Execute query and return as DataFrame
        df = pd.read_sql_query(base_query, conn, params=params)
        
        conn.close()
        
        return df
        
    except Exception as e:
        log_message(f"Error retrieving statistics history: {str(e)}", level='ERROR')
        raise

def get_latest_statistics():
    """
    Get the most recent statistics for all metrics.
    
    Returns:
        pandas.DataFrame with the latest value for each metric
    """
    try:
        conn = get_postgres_connection()
        
        query = """
        SELECT DISTINCT ON (metric_name) 
            metric_name, metric_value, report_timestamp, details_preview
        FROM statistics_log
        ORDER BY metric_name, report_timestamp DESC
        """
        
        df = pd.read_sql_query(query, conn)
        
        conn.close()
        
        return df
        
    except Exception as e:
        log_message(f"Error retrieving latest statistics: {str(e)}", level='ERROR')
        raise

def get_metric_trend(metric_name, days_back=30):
    """
    Get trend data for a specific metric.
    
    Args:
        metric_name: Name of the metric
        days_back: Number of days to look back
        
    Returns:
        pandas.DataFrame with timestamp and value columns
    """
    try:
        conn = get_postgres_connection()
        
        query = """
        SELECT report_timestamp, metric_value
        FROM statistics_log
        WHERE metric_name = %s 
        AND report_timestamp >= %s
        ORDER BY report_timestamp ASC
        """
        
        params = [metric_name, datetime.now() - timedelta(days=days_back)]
        
        df = pd.read_sql_query(query, conn, params=params)
        
        conn.close()
        
        return df
        
    except Exception as e:
        log_message(f"Error retrieving metric trend for {metric_name}: {str(e)}", level='ERROR')
        raise

def cleanup_old_statistics(days_to_keep=90):
    """
    Clean up old statistics data to prevent database bloat.
    
    Args:
        days_to_keep: Number of days of data to retain
    """
    try:
        conn = get_postgres_connection()
        cursor = conn.cursor()
        
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        delete_query = """
        DELETE FROM statistics_log
        WHERE report_timestamp < %s
        """
        
        cursor.execute(delete_query, (cutoff_date,))
        deleted_count = cursor.rowcount
        
        cursor.close()
        conn.close()
        
        log_message(f"Cleaned up {deleted_count} old statistics records", level='INFO')
        
    except Exception as e:
        log_message(f"Error cleaning up old statistics: {str(e)}", level='ERROR')
        raise
