import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from DatabaseManagement.Connections import get_gz_connection
from Common.Constants import court_mapping, sanitize_name, nas_plaintiff_folder
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch, prepare_value
import pandas as pd
import base64
import zlib
import cv2
from skimage.metrics import structural_similarity as ssim
import json
import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk
from FileManagement.SendToNAS import connect_to_nas, transfer_nas_to_local, sftp_exists
import io
from FileManagement.Tencent_COS import get_cos_client, delete_cos_files


def get_unique_court_values(table_name):
    gz_connection = get_gz_connection()
    gz_cursor = gz_connection.cursor()
    
    # Query to get unique values from the court field
    query = f"SELECT DISTINCT court FROM {table_name}"
    gz_cursor.execute(query)
    
    # Fetch all unique court values
    unique_courts = [row[0] for row in gz_cursor.fetchall()]
    
    gz_cursor.close()
    gz_connection.close()
    
    return unique_courts


def update_court_names_from_lexis_to_docketalarm(table_name, court_mapping):
    gz_connection = get_gz_connection()
    gz_cursor = gz_connection.cursor()

    for original_court, mapped_court in court_mapping.items():
        # Update the court field in the database
        update_query = f"""
        UPDATE {table_name}
        SET court = %s
        WHERE court = %s
        """
        gz_cursor.execute(update_query, (mapped_court, original_court))

    gz_connection.commit()  # Commit the changes to the database
    gz_cursor.close()
    gz_connection.close()


def find_duplicate_cases(table_name):
    df = get_table_from_GZ(table_name)
    
    # Find duplicates where docket and court are the same
    duplicates = df[df.duplicated(subset=['docket', 'court'], keep=False)]
    
    return duplicates


def resolve_duplicates(duplicates, table_name):
    gz_connection = get_gz_connection()
    gz_cursor = gz_connection.cursor()
    
    for _, group in duplicates.groupby(['docket', 'court']):
        # Convert group to a DataFrame
        group_df = group.sort_values(by='id', ascending=False)  # Sort by id descending
        
        # Most recent and oldest records
        most_recent = group_df.iloc[0]
        oldest = group_df.iloc[-1]
        oldest_index = group_df.index[-1]  # Get the index of the oldest record
        
        # Update the duplicates DataFrame with values from the most recent record
        for column in group_df.columns:
            if column not in ['id', 'create_time']:
                duplicates.at[oldest_index, column] = most_recent[column]
        
        # Delete the most recent record from the database
        duplicates.drop(most_recent.name, inplace=True)

        delete_query = f"DELETE FROM tb_case_steps WHERE case_id = %s"
        gz_cursor.execute(delete_query, (int(oldest['id']),))

        update_query = f"UPDATE tb_case_steps SET case_id = %s WHERE case_id = %s"
        gz_cursor.execute(update_query, (int(oldest['id']), int(most_recent['id'])))

        delete_query = f"DELETE FROM {table_name} WHERE id = %s"
        gz_cursor.execute(delete_query, (int(most_recent['id']),))
        

    gz_connection.commit()
    gz_cursor.close()
    gz_connection.close()

    # Call the function to update the database with the modified duplicates DataFrame
    insert_and_update_df_to_GZ_batch(duplicates, table_name, 'id')




def remove_duplicate_plaintiff_pictures():    
    sftp, transport = connect_to_nas()
    client, bucket = get_cos_client()
    df_cases = get_table_from_GZ("tb_case")
    df_plaintiff = get_table_from_GZ("tb_plaintiff")

    for index, row in df_plaintiff.iterrows():
        plaintiff_name = sanitize_name(row['plaintiff_name'])
        nas_this_plaintiff_folder = nas_plaintiff_folder + "/" + plaintiff_name
        cos_this_plaintiff_folder = f'plaintiff_images/{row["id"]}'

        if not sftp_exists(sftp, nas_this_plaintiff_folder):
            continue

        # Process full webp images
        full_images = sorted([f for f in sftp.listdir(nas_this_plaintiff_folder) if f.endswith("_full.webp")], key=lambda x: sftp.stat(f"{nas_this_plaintiff_folder}/{x}").st_mtime)
        
        for file in full_images:
            files_to_remove = []
            file_path = f"{nas_this_plaintiff_folder}/{file}"
            
            files_to_compare = [f for f in full_images if f != file and int(f.split("page")[1].split("_")[0]) == int(file.split("page")[1].split("_")[0])]
            for file2 in files_to_compare:
                file2_path = f"{nas_this_plaintiff_folder}/{file2}"
                
                # Download files temporarily for comparison
                with sftp.open(file_path, 'rb') as f1, sftp.open(file2_path, 'rb') as f2:
                    temp_file1 = f"/tmp/{file}"
                    temp_file2 = f"/tmp/{file2}"
                    with open(temp_file1, 'wb') as tf1, open(temp_file2, 'wb') as tf2:
                        tf1.write(f1.read())
                        tf2.write(f2.read())
                
                if compare_images(temp_file1, temp_file2):
                    files_to_remove.append(file2)
                
                # Clean up temp files
                os.remove(temp_file1)
                os.remove(temp_file2)

            if files_to_remove:
                if show_comparison_window(file, files_to_remove, nas_this_plaintiff_folder, sftp):
                    # Find relevant cases by decompressing first
                    mask = df_cases['images'].apply(lambda x: file in x)
                    idx_original = df_cases[mask].index[0]
                    case_row = df_cases.loc[idx_original]
                    images_dict_original = json.loads(case_row['images'])
                    original_key = get_image_key(file, images_dict_original)
                    position_of_original_image_in_full_filename = images_dict_original[original_key]['full_filename'].index(file)

                    if not original_key:
                        continue

                    relevant_mask = df_cases['images'].apply(lambda x: any(file_to_remove in x for file_to_remove in files_to_remove))
                    relevant_cases = df_cases[relevant_mask]

                    # Now process only relevant cases
                    for idx_of_file_to_remove, row_of_file_to_remove in relevant_cases.iterrows():
                        for file_to_remove in files_to_remove:
                            if file_to_remove in relevant_cases.at[idx_of_file_to_remove, 'images']:
                                break
                                                                                                            
                        images_dict_of_file_to_remove = json.loads(relevant_cases.at[idx_of_file_to_remove, 'images'])
                        duplicate_key = get_image_key(file_to_remove, images_dict_of_file_to_remove)
                        position_of_image_to_remove_in_full_filename = images_dict_of_file_to_remove[duplicate_key]['full_filename'].index(file_to_remove)
                        if duplicate_key:
                            # Verify reg_no matches
                            if images_dict_original[original_key]['reg_no'][position_of_original_image_in_full_filename] != images_dict_of_file_to_remove[duplicate_key]['reg_no'][position_of_image_to_remove_in_full_filename]:
                                if ask_user_which_to_keep('reg_no', images_dict_original[original_key]['reg_no'][position_of_original_image_in_full_filename], images_dict_of_file_to_remove[duplicate_key]['reg_no'][position_of_image_to_remove_in_full_filename]):
                                    images_dict_of_file_to_remove[duplicate_key]['reg_no'][position_of_image_to_remove_in_full_filename] = images_dict_original[original_key]['reg_no'][position_of_original_image_in_full_filename]
                                else:
                                    images_dict_original[original_key]['reg_no'][position_of_original_image_in_full_filename] = images_dict_of_file_to_remove[duplicate_key]['reg_no'][position_of_image_to_remove_in_full_filename]

                            if images_dict_original[original_key]['trademark_text'][0] != images_dict_of_file_to_remove[duplicate_key]['trademark_text'][0]:
                                if ask_user_which_to_keep('trademark_text', images_dict_original[original_key]['trademark_text'][0], images_dict_of_file_to_remove[duplicate_key]['trademark_text'][0]):
                                    images_dict_of_file_to_remove[duplicate_key]['trademark_text'][0] = images_dict_original[original_key]['trademark_text'][0]
                                else:
                                    images_dict_original[original_key]['trademark_text'][0] = images_dict_of_file_to_remove[duplicate_key]['trademark_text'][0]
                                    
                            # Simply replace the filename in all places it appears
                            images_dict_of_file_to_remove[duplicate_key]['full_filename'][position_of_image_to_remove_in_full_filename] = images_dict_original[original_key]['full_filename'][position_of_original_image_in_full_filename]

                            # If the duplicate key is different from original key, merge and delete
                            if duplicate_key != original_key:
                                images_dict_of_file_to_remove[original_key] = images_dict_of_file_to_remove[duplicate_key]
                                del images_dict_of_file_to_remove[duplicate_key]
                                sftp.remove(f"{nas_this_plaintiff_folder}/{duplicate_key}")
                                sftp.remove(f"{nas_this_plaintiff_folder}/high/{duplicate_key}")
                                sftp.remove(f"{nas_this_plaintiff_folder}/low/{duplicate_key}")
                                delete_cos_files(client, bucket, [
                                    {"Key": f"{cos_this_plaintiff_folder}/high/{duplicate_key}"},
                                    {"Key": f"{cos_this_plaintiff_folder}/low/{duplicate_key}"}
                                ])

                            # Remove the duplicate file
                            sftp.remove(f"{nas_this_plaintiff_folder}/{file_to_remove}")
                            sftp.remove(f"{nas_this_plaintiff_folder}/high/{file_to_remove}")
                            sftp.remove(f"{nas_this_plaintiff_folder}/low/{file_to_remove}")
                            delete_cos_files(client, bucket, [
                                {"Key": f"{cos_this_plaintiff_folder}/high/{file_to_remove}"},
                                {"Key": f"{cos_this_plaintiff_folder}/low/{file_to_remove}"}
                            ])

                            
                        df_cases.at[idx_original, 'images'] = images_dict_original
                        df_cases.at[idx_of_file_to_remove, 'images'] = images_dict_of_file_to_remove

    
    # Save updated cases table
    df_cases['images'] = df_cases['images'].apply(json.dumps)
    insert_and_update_df_to_GZ_batch(df_cases, "tb_case", 'id')


# Add function for user choice dialog
def ask_user_which_to_keep(field_name, value1, value2):
    print(f"\nConflict in {field_name}:")
    print(f"1: {value1}")
    print(f"2: {value2}")
    choice = input("Keep which version? (1/2): ")
    return choice == "1"


def show_comparison_window(original_file, duplicate_files, nas_this_plaintiff_folder, sftp):
    root = tk.Tk()
    root.title("Image Comparison")
    
    # Create frames with padding
    original_frame = ttk.LabelFrame(root, text="Original Image", padding=(5, 5))  # Use padding attribute
    original_frame.pack(side=tk.LEFT)
    duplicates_frame = ttk.LabelFrame(root, text="Duplicate Images", padding=(5, 5))  # Use padding attribute
    duplicates_frame.pack(side=tk.LEFT)
    
    # Load and display original image
    try:
        with sftp.open(f"{nas_this_plaintiff_folder}/{original_file}", 'rb') as f:
            img_data = f.read()
            img = Image.open(io.BytesIO(img_data))
            img = img.convert('RGB')  # Convert to RGB to handle RGBA images
            img.thumbnail((400, 400))  # Resize for display
            photo = ImageTk.PhotoImage(img)
            label = ttk.Label(original_frame, image=photo)
            label.image = photo  # Keep a reference
            label.pack()
            ttk.Label(original_frame, text=original_file).pack()
    except Exception as e:
        print(f"Error loading original image {original_file}: {e}")
        return False
    
    # Load and display duplicate images
    for dup_file in duplicate_files:
        frame = ttk.Frame(duplicates_frame, padding=(5, 5))  # Use padding attribute
        frame.pack()
        
        try:
            with sftp.open(f"{nas_this_plaintiff_folder}/{dup_file}", 'rb') as f:
                img_data = f.read()
                img = Image.open(io.BytesIO(img_data))
                img = img.convert('RGB')
                img.thumbnail((400, 400))
                photo = ImageTk.PhotoImage(img)
                label = ttk.Label(frame, image=photo)
                label.image = photo
                label.pack()
                ttk.Label(frame, text=dup_file).pack()
        except Exception as e:
            print(f"Error loading duplicate image {dup_file}: {e}")
            continue
    
    # Add confirmation buttons
    button_frame = ttk.Frame(root, padding=(0, 10))  # Use padding attribute
    button_frame.pack()
    ttk.Button(button_frame, text="Confirm Merge", command=lambda: setattr(root, 'result', True) or root.destroy()).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="Cancel", command=lambda: setattr(root, 'result', False) or root.destroy()).pack(side=tk.LEFT, padx=5)
    
    # Center the window on screen
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    root.mainloop()
    return getattr(root, 'result', False)



def get_image_key(filename, images_dict):
    for key, value in images_dict.items():
        if filename in value.get('full_filename', []):
            return key
    return None

def compare_images(img1_path, img2_path):
    img1 = cv2.imread(img1_path)
    img2 = cv2.imread(img2_path)
    
    # Convert to grayscale
    gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
    gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
    
    # Resize to same dimensions
    height = min(gray1.shape[0], gray2.shape[0])
    width = min(gray1.shape[1], gray2.shape[1])
    gray1 = cv2.resize(gray1, (width, height))
    gray2 = cv2.resize(gray2, (width, height))
    
    # Compare using SSIM
    score = ssim(gray1, gray2)

    if score > 0.95:
        print(f"Similarity score: {score}")
        return True
    else:
        return False

        

# for different plaintiffs that should be "Unidentified Claimant"
def merge_plaintiffs_to_unidentified():
    gz_connection = get_gz_connection()
    gz_cursor = gz_connection.cursor()
    
    # List of plaintiff IDs to merge
    plaintiff_ids_to_merge = [179, 1601, 1600, 10, 11, 12, 13, 1190, 1191, 1192, 1193, 1194, 1195, 1202]
    target_plaintiff_id = 9
    
    try:
        # Update cases table to point to the target plaintiff ID
        update_cases_query = """
        UPDATE tb_case 
        SET plaintiff_id = %s 
        WHERE plaintiff_id IN ({})
        """.format(','.join(['%s'] * len(plaintiff_ids_to_merge)))
        gz_cursor.execute(update_cases_query, (target_plaintiff_id, *plaintiff_ids_to_merge))
        
        # Delete the old plaintiff records
        delete_plaintiffs_query = """
        DELETE FROM tb_plaintiff 
        WHERE id IN ({})
        """.format(','.join(['%s'] * len(plaintiff_ids_to_merge)))
        gz_cursor.execute(delete_plaintiffs_query, plaintiff_ids_to_merge)
        
        # Update the target plaintiff name
        update_plaintiff_name_query = """
        UPDATE tb_plaintiff 
        SET plaintiff_name = 'Unidentified Claimant' 
        WHERE id = %s
        """
        gz_cursor.execute(update_plaintiff_name_query, (target_plaintiff_id,))
        
        gz_connection.commit()
        print("Successfully merged plaintiffs and updated records")
        
    except Exception as e:
        gz_connection.rollback()
        print(f"Error occurred: {e}")
        
    finally:
        gz_cursor.close()
        gz_connection.close()


def remove_duplicate_case_steps():
    gz_connection = get_gz_connection()
    gz_cursor = gz_connection.cursor()
    
    try:
        # Delete duplicates keeping the latest record (highest id)
        delete_query = """
        DELETE FROM tb_case_steps 
        WHERE id IN (
            SELECT id FROM (
                SELECT id,
                ROW_NUMBER() OVER (
                    PARTITION BY case_id, step_nb
                    ORDER BY id DESC
                ) as row_num
                FROM tb_case_steps
            ) t
            WHERE t.row_num > 1
        )
        """
        gz_cursor.execute(delete_query)
        
        gz_connection.commit()
        print("Successfully removed duplicate case steps")
        
    except Exception as e:
        gz_connection.rollback()
        print(f"Error occurred: {e}")
        
    finally:
        gz_cursor.close()
        gz_connection.close()


# Make sure they are valid JSON after decompression
# Make sure that there is not path like D:\\documents\\ in the images fields
def validate_cases_images():
    gz_connection = get_gz_connection()
    gz_cursor = gz_connection.cursor()
    invalid_records = []
    
    try:
        query = "SELECT id, images FROM tb_case WHERE images IS NOT NULL"
        gz_cursor.execute(query)
        records = gz_cursor.fetchall()
        
        for i, (record_id, images_data) in enumerate(records):
            if i % 200 == 0:
                print(f"Processed {i} records")
            try:
                # Decrypt base64 and decompress
                decoded_data = zlib.decompress(base64.b64decode(images_data)).decode('utf-8')
                
                # Try to load JSON up to 5 times
                current_data = decoded_data
                is_valid = False
                for i in range(5):
                    try:
                        current_data = json.loads(current_data)
                        if isinstance(current_data, dict):
                            is_valid = True
                            need_update = (i != 0)
                            break
                    except json.JSONDecodeError:
                        continue
                
                if is_valid:
                    # Check the images field for paths like D:\\documents\\
                    keys_to_update = []
                    for key, value in current_data.items():
                        if any("/" in str(v) for v in value['full_filename']) or any("\\" in str(v) for v in value['full_filename']):
                            new_value = [v.split("\\")[-1] if "\\" in str(v) else v for v in value['full_filename']]
                            new_value = [v.split("/")[-1] if "/" in str(v) else v for v in value['full_filename']]
                            print(f"Updating {key} from {value['full_filename']} to {new_value}")
                            current_data[key]['full_filename'] = new_value
                            need_update = True
                        if "/" in key or "\\" in key:
                            new_key = key.split("\\")[-1] if "\\" in key else key.split("/")[-1]
                            keys_to_update.append((key, new_key))
                            need_update = True

                    for old_key, new_key in keys_to_update:
                        print(f"Updating {old_key} to {new_key}")
                        current_data[new_key] = current_data.pop(old_key)

                    # Re-encode the valid JSON data
                    if need_update:
                        json_str = json.dumps(current_data)
                        encoded = prepare_value(json_str, 'images', 'tb_case')
                        
                        # Update the database with the cleaned data
                        update_query = "UPDATE cases SET images = %s WHERE id = %s"
                        gz_cursor.execute(update_query, (encoded, record_id))
                else:
                    invalid_records.append((record_id, "Could not convert to valid JSON dictionary after 5 attempts"))
                    
            except Exception as e:
                invalid_records.append((record_id, f"Processing error: {str(e)}"))
        
        gz_connection.commit()
        
        if invalid_records:
            print(f"\nFound {len(invalid_records)} invalid records:")
            for record_id, error in invalid_records:
                print(f"Case ID {record_id}: {error}")
        else:
            print("All images fields processed and updated successfully")
            
    except Exception as e:
        gz_connection.rollback()
        print(f"Error occurred while validating: {e}")
        
    finally:
        gz_cursor.close()
        gz_connection.close()
        
    return invalid_records


def upgrade_images():
    df_cases = get_table_from_GZ("tb_case")
    for index, case in df_cases.iterrows():
        oldimages = case["images"]
        if oldimages:
            df_cases.at[index, "images"] = {'trademarks': {}, 'patents': {}, 'copyrights': {}}
            if "copyright" in case["nos_description"].lower():
                df_cases.at[index, "images"]["copyrights"] = oldimages
            if "trademark" in case["nos_description"].lower():
                df_cases.at[index, "images"]["trademarks"] = oldimages
            if "patent" in case["nos_description"].lower():
                df_cases.at[index, "images"]["patents"] = oldimages
            df_cases.at[index, "images"] = json.dumps(df_cases.at[index, "images"])
    insert_and_update_df_to_GZ_batch(df_cases, "tb_case", "id")

def upgrade_images2():
    df_cases = get_table_from_GZ("tb_case")
    for index, case in df_cases.iterrows():
        if not case["images"]:
            df_cases.at[index, "images"] = {'trademarks': {}, 'patents': {}, 'copyrights': {}}
        df_cases.at[index, "images"] = json.dumps(df_cases.at[index, "images"])
    insert_and_update_df_to_GZ_batch(df_cases, "tb_case", "id")


def hide_cases():
    df_cases = get_table_from_GZ("tb_case")
    df_cases_to_hide = df_cases[pd.to_datetime(df_cases["date_filed"]) < pd.to_datetime("2024-01-01")]
    df_cases_to_hide["deleted"] = 1
    df_cases_to_hide["deleted"] = df_cases_to_hide["deleted"].astype(int)
    insert_and_update_df_to_GZ_batch(df_cases_to_hide, "tb_case", "id")


def missing_plaintiff_ip():
    df_cases = get_table_from_GZ("tb_case", force_refresh=True)
    df_plaintiff = get_table_from_GZ("tb_plaintiff", force_refresh=True)

    # Group by plaintiff_id and check if all cases associated with that plaintiff have a missing nos_description.
    all_missing = df_cases.groupby('plaintiff_id').apply(
        lambda group: (group['nos_description'].isnull() | (group['nos_description'].str.strip() == '')).all()
    )

    plaintiff_ids_all_missing = all_missing[all_missing].index.tolist()

    df_cases_all_missing = df_cases[df_cases['plaintiff_id'].isin(plaintiff_ids_all_missing)]
    df_cases_all_missing_by_date = df_cases_all_missing.groupby('date_filed').size().reset_index(name='count')
    df_cases_all_missing_by_date = df_cases_all_missing_by_date.sort_values('count', ascending=False)
    
    
    print("Total unique plaintiff id with all cases missing nos_description:", len(plaintiff_ids_all_missing))
    
    return df_cases_all_missing_by_date


    


if __name__ == "__main__":
    missing_plaintiff_ip()
    # print(get_unique_court_values("tb_case"))
    # update_court_names_from_lexis_to_docketalarm("tb_case", court_mapping)
    # duplicate_cases = find_duplicate_cases("tb_case")
    # resolve_duplicates(duplicate_cases, "tb_case")
    # remove_duplicate_plaintiff_pictures()
    # merge_plaintiffs_to_unidentified()
    # remove_duplicate_case_steps()
    # validate_cases_images()
    # upgrade_images2()
    # hide_cases()

