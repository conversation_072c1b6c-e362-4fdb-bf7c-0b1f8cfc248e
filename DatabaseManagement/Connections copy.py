import os
# import oracledb
import mysql.connector
import time
# Removed: import logging
import socket
import threading
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from logdata import log_message # Added logdata import

# Removed logging setup
# logger = logging.getLogger(__name__)

# Add a function to test MySQL server accessibility
def test_port_connection(host, port, timeout=5):
    """Test if a TCP port is open and accessible"""
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    s.settimeout(timeout)

    log_message(f"Testing connection to {host}:{port}...", level='INFO')
    try:
        s.connect((host, port))
        log_message(f"Successfully connected to {host}:{port}", level='INFO')
        return True
    except socket.timeout:
        log_message(f"Connection to {host}:{port} timed out", level='WARNING')
        return False
    except ConnectionRefusedError:
        log_message(f"Connection to {host}:{port} refused", level='WARNING')
        return False
    except Exception as e:
        log_message(f"Error connecting to {host}:{port}: {e}", level='ERROR')
        return False
    finally:
        s.close()

# Function to monitor a process and kill it if it takes too long
def timeout_handler(func, args=(), kwargs={}, timeout_duration=30, default=None):
    """Run a function with a timeout"""
    result = [default]
    exception = [None]

    def target():
        try:
            result[0] = func(*args, **kwargs)
        except Exception as e:
            exception[0] = e

    thread = threading.Thread(target=target)
    thread.daemon = True

    log_message(f"Starting function with {timeout_duration} second timeout...", level='DEBUG') # Changed to DEBUG
    thread.start()
    thread.join(timeout_duration)

    if thread.is_alive():
        log_message(f"Function timed out after {timeout_duration} seconds!", level='WARNING')
        return default, TimeoutError(f"Function timed out after {timeout_duration} seconds")

    if exception[0]:
        return None, exception[0]

    return result[0], None


@retry(
    stop=stop_after_attempt(5),
    wait=wait_exponential(multiplier=2, min=4, max=20),  # Adjusted retry timing based on successful connections
    retry=retry_if_exception_type((mysql.connector.Error, AttributeError)),
    reraise=True
)
def get_gz_connection(use_pure, use_ssl, host=os.getenv("MYSQL_HOST"), user=os.getenv("MYSQL_USER"), password=os.getenv("MYSQL_PASSWORD"), database=os.getenv("MYSQL_DATABASE"), port=3306, custom_pool_name=None):
    # log_message(f"Getting connection to {host}:{port} with user {user} and password {password} and database {database}", level='DEBUG') # Optional debug log
    try:
        start_time = time.time()

        # Use custom pool name if provided (for pool reset scenarios)
        pool_name = custom_pool_name if custom_pool_name else f"pool_{host}_{user}_{database}"

        mysql_connection = mysql.connector.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            # Modified pool settings
            pool_size=10,              # Increased from 3 to match concurrent request capacity
            pool_name=pool_name,
            # Connection stability settings
            connect_timeout=15,        # successful connections typically take 2-4 seconds
            connection_timeout=15,     # Added explicit connection timeout
            pool_reset_session=True,
            # Automatic reconnection settings
            autocommit=True,
            get_warnings=True,
            raise_on_warnings=True,
            # Use pure Python implementation
            use_pure=use_pure,
            ssl_disabled=use_ssl,
            # Buffer settings
            buffered=True,
            # Character settings
            charset='utf8mb4',
            collation='utf8mb4_unicode_ci',
        )
        end_time = time.time()

        log_message(f"connected!!!! use_pure: {use_pure} use_ssl: {use_ssl} in {end_time - start_time:.1f} seconds", level='INFO')
        # if (end_time - start_time) > 15:
        #     close_idle_connections(mysql_connection)

        return (end_time - start_time)

    except mysql.connector.Error as e:
        log_message(f"Error connecting to MySQL database {host}:{port} with user {user} and password {password} and database {database}: {e}. Going to retry...", level='ERROR')
        return 200


def is_connection_alive(connection):
    """Check if the database connection is still alive"""
    try:
        if connection is None:
            return False
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
        return True
    except Exception:
        return False


def check_active_connections(connection):
    """Checks and prints the number of active connections."""
    try:
        with connection.cursor() as cursor:
            cursor.execute("SHOW STATUS LIKE 'Threads_connected';")  # Or Threads_running for active queries
            result = cursor.fetchone()
            log_message(f"Active Connections (Threads_connected): {result}", level='DEBUG') # Changed to DEBUG

            cursor.execute("SHOW PROCESSLIST;")
            processlist = cursor.fetchall()
            log_message("Processlist:", level='DEBUG') # Changed to DEBUG
            for row in processlist:
                log_message(str(row), level='DEBUG') # Log each row

    except mysql.connector.Error as err:
        log_message(f"Error checking connections: {err}", level='ERROR')


def handle_pool_exhaustion(host, user, password, database, port=3306):
    """Handle cases where the connection pool is exhausted by force-closing idle connections"""
    try:
        # Try to establish a direct connection outside the pool to execute admin commands
        direct_connection = mysql.connector.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            use_pure=True,
            pool_name=None  # Ensure we're not using the pool
        )

        # Close idle connections
        close_idle_connections(direct_connection, idle_threshold_seconds=60)  # Lower threshold for emergency cleanup

        # Reset the pool and get the new pool name
        new_pool_name = reset_connection_pool(host, user, database)

        # Close the direct connection
        direct_connection.close()


        # Return the new pool name so it can be used for subsequent connections
        return new_pool_name

    except Exception as e:
        return None



def reset_connection_pool(host, user, database):
    """Reset the connection pool when problems occur"""
    try:
        pool_name = f"pool_{host}_{user}_{database}"
        # MySQLConnectionPool._remove_pool is not a reliable API method
        # Instead, create a new connection with a new pool name to avoid the exhausted pool
        new_pool_name = f"{pool_name}_{int(time.time())}"

        # Force garbage collection to help release any lingering connections
        import gc
        gc.collect()

        return new_pool_name

    except Exception as e:
        return None





def close_idle_connections(connection, idle_threshold_seconds=100):
    """Closes MySQL connections that are idle (sleeping) for longer than the threshold and not from localhost."""
    try:
        with connection.cursor() as cursor:
            cursor.execute("SHOW PROCESSLIST;")
            processlist = cursor.fetchall()
            log_message(f"Processlist length: {len(processlist)}", level='DEBUG') # Changed to DEBUG

            connections_closed = 0
            for process in processlist:
                conn_id = process[0]
                user = process[1]
                host = process[2]
                db = process[3]
                command = process[4]
                time_idle = process[5]
                state = process[6]
                info = process[7]

                if command == 'Sleep' and time_idle > idle_threshold_seconds and 'localhost' not in host and '127.0.0.1' not in host:
                    try:
                        kill_query = f"KILL {conn_id};"
                        cursor.execute(kill_query)
                        log_message(f"Closed connection ID {conn_id} from host {host}, idle for {time_idle:.1f} seconds.", level='INFO')
                        connections_closed += 1
                    except mysql.connector.Error as kill_err:
                        log_message(f"Error killing connection ID {conn_id}: {kill_err}", level='ERROR')

            log_message(f"Closed {connections_closed} idle connections on server.", level='INFO')

    except mysql.connector.Error as err:
        log_message(f"Error checking processlist: {err}", level='ERROR')

    except Exception as e:
        log_message(f"An unexpected error occurred: {e}", level='ERROR')


def empty_table(table_name):
    connection = get_gz_connection()
    cursor = connection.cursor()
    cursor.execute(f"TRUNCATE TABLE {table_name}")
    connection.commit()
    cursor.close()
    connection.close()


if __name__ == "__main__":
    pure_ssl = 0
    nopure_ssl = 0
    pure_nossl = 0
    nopure_nossl = 0

    for i in range(10):
        time_taken = get_gz_connection(use_pure=True, use_ssl=False)
        pure_ssl += time_taken

        time_taken = get_gz_connection(use_pure=False, use_ssl=True)
        nopure_ssl += time_taken

        time_taken = get_gz_connection(use_pure=True, use_ssl=True)
        pure_nossl += time_taken

        time_taken = get_gz_connection(use_pure=False, use_ssl=False)
        nopure_nossl += time_taken

    print(f"pure_ssl: {pure_ssl/10}")
    print(f"nopure_ssl: {nopure_ssl/10}")
    print(f"pure_nossl: {pure_nossl/10}")
    print(f"nopure_nossl: {nopure_nossl/10}")
