import os
import mysql.connector
import pandas as pd
from dotenv import load_dotenv
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
# Removed: import logging
from logdata import log_message # Added logdata import

# Removed logging setup
# logger = logging.getLogger(__name__)

load_dotenv()


# @retry(
#     stop=stop_after_attempt(5),
#     wait=wait_exponential(multiplier=2, min=4, max=20),  # Adjusted retry timing based on successful connections
#     retry=retry_if_exception_type((mysql.connector.Error, AttributeError)),
#     reraise=True
# )
def get_gz_connection_ip(ip, user, password, database):
    try:
        mysql_connection = mysql.connector.connect(
            host=ip,
            user=user,
            password=password,
            database=database,
            autocommit=True,
            get_warnings=True,
            raise_on_warnings=True,
            # Use pure Python implementation
            use_pure=True,
            # Buffer settings
            buffered=True,
            # Character settings
            charset='utf8mb4',
            collation='utf8mb4_unicode_ci'
        )

        log_message(f"connected to {ip}:{database}", level='INFO')

        return mysql_connection

    except mysql.connector.Error as e:
        log_message(f"Error connecting to MySQL database: {e}", level='ERROR')
        raise



def get_table_from_GZ(ip, user, password, database, table_name):
    gz_connection = get_gz_connection_ip(ip, user, password, database)
    gz_cursor = gz_connection.cursor()

    # Read data from US database
    # Fetch all data at once
    import time
    start_time = time.time()  # Start timing
    query = f"SELECT * FROM {table_name}"
    gz_cursor.execute(query)


    data = gz_cursor.fetchall()
    end_time = time.time()  # End timing

    # Log the time taken to fetch data
    log_message(f"Time taken to fetch data: {end_time - start_time:.2f} seconds", level='INFO')

    # Convert to DataFrame first
    df = pd.DataFrame(data, columns=[desc[0] for desc in gz_cursor.description])

    gz_cursor.close()
    gz_connection.close()

    return df


def get_table_from_GZ_with_progress_bar(ip, user, password, database, table_name):
    gz_connection = get_gz_connection_ip(ip, user, password, database)
    gz_cursor = gz_connection.cursor()

    # Read data from US database
    # Fetch all data at once
    import time
    start_time = time.time()  # Start timing
    query = f"SELECT * FROM {table_name}"

    # Get the total number of rows for the progress bar
    count_query = f"SELECT COUNT(*) FROM {table_name}"
    gz_cursor.execute(count_query)
    total_rows = gz_cursor.fetchone()[0]

    gz_cursor.execute(query)

    # Use tqdm to show a progress bar
    from tqdm import tqdm

    data = []
    batch_size = 1000  # Adjust batch size as needed
    with tqdm(total=total_rows, desc="Fetching data", unit="rows") as progress_bar:
        while True:
            rows = gz_cursor.fetchmany(batch_size)
            if not rows:
                break
            data.extend(rows)
            progress_bar.update(len(rows))

    end_time = time.time()  # End timing

    # Log the time taken to fetch data
    log_message(f"Time taken to fetch data: {end_time - start_time:.2f} seconds", level='INFO')

    # Convert to DataFrame first
    df = pd.DataFrame(data, columns=[desc[0] for desc in gz_cursor.description])

    gz_cursor.close()
    gz_connection.close()

    return df

if __name__ == "__main__":
    df = get_table_from_GZ_with_progress_bar(os.getenv("MYSQL_HOST"), os.getenv("MYSQL_USER"), os.getenv("MYSQL_PASSWORD"), os.getenv("MYSQL_DATABASE"), "tb_case_steps")