# Connection property while using Oracle wallets.
oracle.net.wallet_location=(SOURCE=(METHOD=FILE)(METHOD_DATA=(DIRECTORY=${TNS_ADMIN})))
# FOLLOW THESE STEPS FOR USING JKS
# (1) Uncomment the following properties to use J<PERSON>.
# (2) Comment out the oracle.net.wallet_location property above
# (3) Set the correct password for both trustStorePassword and keyStorePassword.
# It's the password you specified when downloading the wallet from OCI Console or the Service Console.
#javax.net.ssl.trustStore=${TNS_ADMIN}/truststore.jks
#javax.net.ssl.trustStorePassword=<password_from_console>
#javax.net.ssl.keyStore=${TNS_ADMIN}/keystore.jks
#javax.net.ssl.keyStorePassword=<password_from_console>