# Dead/Cancelled Trademark Check Feature

## Overview
Added functionality to check if a trademark is dead/cancelled when it's not found in the database during the trademark filename migration process.

## Changes Made

### 1. Modified `IP/Trademarks/USPTO_TSDR_API.py`

**Enhanced `process_xml_content()` method:**
- Added extraction of `status_code` from `<ns2:MarkCurrentStatusCode>` XML element
- Added extraction of `ser_no` from `<ns1:ApplicationNumberText>` XML element
- These fields are now captured in the returned data dictionary

```python
data["ser_no"] = soup.find("ns1:ApplicationNumberText").text if soup.find("ns1:ApplicationNumberText") else None
data["status_code"] = soup.find("ns2:MarkCurrentStatusCode").text if soup.find("ns2:MarkCurrentStatusCode") else None
```

### 2. Modified `Alerts/PicturesProcessing/migrate_trademark_filenames.py`

**Added TSDRApi integration:**
- Added import for `TSDRApi` from `IP.Trademarks.USPTO_TSDR_API`
- Enhanced `TrademarkFilenameMigrator` class with async context manager support
- Added `tsdr_api` instance to the class

**Added `check_if_trademark_is_dead()` method:**
- Checks if trademark status code is in dead/cancelled status codes list
- Status codes checked: `["600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "614", "618", "620", "622", "626", "710", "711", "712", "713", "714", "900"]`
- Downloads and saves XML files to `local_ip_folder/Trademarks/XML/` directory
- Checks for existing local XML files before making USPTO API calls
- Returns tuple: `(is_dead, ser_no, reg_no, mark_text)`

**Enhanced `process_trademark_entry()` method:**
- Added dead/cancelled trademark check after database lookup fails
- If trademark is dead/cancelled, uses special filename format: `{ser_no}_dead_full.webp`
- Continues processing with the data from the XML file

**Updated main function:**
- Changed to use async context manager: `async with TrademarkFilenameMigrator()`

## Dead/Cancelled Status Codes
The following USPTO status codes indicate a dead/cancelled trademark:
- 600-609: Various cancellation/abandonment statuses
- 614, 618, 620, 622, 626: Additional cancellation statuses  
- 710-714: Opposition/cancellation related statuses
- 900: Generic dead status

## Workflow
1. Try to get `ser_no` and `mark_text` from database using `reg_no`
2. If not found, check if trademark is dead/cancelled:
   - Check for existing XML file in `local_ip_folder/Trademarks/XML/`
   - If not found locally, query USPTO API using `get_status_info_xml()`
   - Save XML file locally for future use
   - Extract `status_code`, `ser_no`, `reg_no`, and `mark_text` from XML
   - Check if `status_code` is in dead/cancelled list
3. If trademark is dead/cancelled:
   - Use the `ser_no`, `reg_no`, and `mark_text` from XML
   - Generate filename as `{ser_no}_dead_full.webp`
   - Continue with normal processing
4. If not dead/cancelled or no XML data, continue with existing LLM/user input flow

## Files Modified
- `IP/Trademarks/USPTO_TSDR_API.py` - Enhanced XML processing
- `Alerts/PicturesProcessing/migrate_trademark_filenames.py` - Added dead trademark check

## Test File
- `test_dead_trademark_check.py` - Simple test script to verify functionality

## Benefits
- Reduces manual intervention for dead/cancelled trademarks
- Automatically handles trademarks that are not in the database due to being cancelled
- Maintains audit trail by saving XML files locally
- Uses special filename format to distinguish dead trademarks
