#!/usr/bin/env python3
"""
Simple script to update database with your filtered cases dataframe.
"""

from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
from DatabaseManagement.Connections import get_gz_connection

# Load the data (same as your code)
print("Loading cases from database...")
all_cases_df = get_table_from_GZ("tb_case", force_refresh=True)

# Remove the specific IDs (same as your code)
# ids_to_remove = [14416, 14425, 14422, 14420, 14419, 14415, 14421, 14423, 14418, 14417, 14424, 14426]
ids_to_remove = list(range(14415, 14439))
print(f"Original count: {len(all_cases_df)}")

# Show what will be removed
cases_to_remove = all_cases_df[all_cases_df['id'].isin(ids_to_remove)]
print(f"Cases to remove: {len(cases_to_remove)}")
if len(cases_to_remove) > 0:
    print(cases_to_remove[['id', 'docket', 'court']].to_string())

# Filter the dataframe
all_cases_df = all_cases_df[~all_cases_df['id'].isin(ids_to_remove)]
print(f"Filtered count: {len(all_cases_df)}")

# Delete the specific cases from database first
print("Deleting specific cases from database...")
connection = get_gz_connection()
cursor = connection.cursor()

try:
    # Delete from tb_case
    placeholders = ','.join(['%s'] * len(ids_to_remove))
    delete_query = f"DELETE FROM tb_case WHERE id IN ({placeholders})"
    cursor.execute(delete_query, ids_to_remove)
    print(f"Deleted {cursor.rowcount} cases")
    
    # Delete related steps
    steps_query = f"DELETE FROM tb_case_steps WHERE case_id IN ({placeholders})"
    cursor.execute(steps_query, ids_to_remove)
    print(f"Deleted {cursor.rowcount} case steps")
    
    connection.commit()
    print("Deletion completed successfully")
    
except Exception as e:
    connection.rollback()
    print(f"Error: {e}")
    raise
finally:
    cursor.close()
    connection.close()

print("Database update completed!")
