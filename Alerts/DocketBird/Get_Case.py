from selenium.webdriver.common.by import By
from selenium.webdriver.common.action_chains import Action<PERSON>hains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from selenium.common.exceptions import NoSuchElementException
import pandas as pd
from Common.Constants import court_mapping
import traceback
from logdata import log_message
import json
from datetime import datetime, timedelta
from Alerts.LexisNexis.Get_Case_Steps import assess_download_requirement
from Alerts.Chrome_Driver import save_browser_state, move_mouse_to, random_delay


def docketbird_get_a_case(i, driver, cases_df, steps_df, df_stats, index, docket_number, court, date_filed, file_type, nas, refresh_days_threshold):
    random_delay()
    
    # Get case details
    refresh = get_case_details(cases_df, index, driver, docket_number, court, refresh_days_threshold)
    log_message(f'  {i+1}.1 Case {docket_number} - Case found and details obtained. Refreshed: {refresh}')
    # existing_steps = len(steps_df[steps_df["case_id"] == index])
    steps_df, download_status, pacer_case_count, files_downloaded = get_case_steps_and_files(driver, docket_number, court, date_filed, steps_df, file_type, nas)
    
    log_message(f'  {i+1}.2 Case {docket_number} - Download status: {download_status} with {pacer_case_count} pacer files downloaded.')
    df_stats = pd.concat([df_stats, pd.DataFrame({'date_filed': [date_filed], 'docket': [docket_number], 'court': [court], 'pacer_refresh': [refresh], 'pacer_file': [pacer_case_count], 'total_file': [files_downloaded]})], ignore_index=True)

    return cases_df, steps_df, df_stats


def get_case_details(df, index, driver, docket_number, court, refresh_days_threshold):
    refresh = False
    ### Get case details
    try:
        # Locate the table within the div with id "Header"
        refresh_span = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "last-updated-plain-english")))
        try: 
            refresh_days_ago = int(refresh_span.text.split(' ')[0])
        except:
            table = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "docket_sheet")))
            date_elem = table.find_element(By.CLASS_NAME, "filing_date") #Find the first date in the table
            refresh_days_ago = (datetime.now() - pd.to_datetime(date_elem.text.strip())).days
        print(f"refresh_days_ago: {refresh_days_ago}")

        # if int(refresh_days_ago) >= int(refresh_days_threshold):
        #     log_message(f"Case {docket_number} is open and needs to be refreshed")
        #     # Get current number of steps in the table
        #     steps_table = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CLASS_NAME, "SS_DataTable")))
        #     current_steps_count = len(steps_table.find_elements(By.TAG_NAME, 'tr'))
            
        #     try: 
        #         # refresh_button = driver.find_element(By.CLASS_NAME, 'SS_DocketUpdateNow')
        #         refresh_button = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.ID, 'refresh-link')))
        #         move_mouse_to(driver, refresh_button)
        #         refresh_button.click()
        #         log_message(f"Refreshing {docket_number} ...")

        #         confirm_refresh_button = WebDriverWait(driver, 10).until(EC.element_to_be_clickable((By.ID, 'confirm-refresh-button')))
        #         move_mouse_to(driver, confirm_refresh_button)
        #         confirm_refresh_button.click()

        #         # I need to wait for the refresh_button to desapear
        #         WebDriverWait(driver, 180).until(EC.invisibility_of_element_located((By.CLASS_NAME, 'SS_DocketUpdateNow')))
        #         header_div = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "Header")))
        #         steps_table = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.CLASS_NAME, "SS_DataTable")))
        #         # Do we need to wait for the steps table to be loaded?
        #         new_steps_count = len(steps_table.find_elements(By.TAG_NAME, 'tr'))
        #         log_message(f"Refresh button desappeared => Case refreshed with {new_steps_count - current_steps_count} new steps")
        #         random_delay()
        #         log_message(f"After waiting 2 seconds => Case refreshed with {new_steps_count - current_steps_count} new steps")
                
        #         if new_steps_count > current_steps_count:
        #             refresh = True

        #         df.loc[index, 'date_updated'] = datetime.now()
            
        #     except TimeoutException:
        #         df.loc[index, 'date_updated'] = datetime.now() - timedelta(days=int(refresh_days_ago))
        #         log_message("Refresh button still visible after 120 seconds. Proceeding anyway.")
    
        #     except:
        #         df.loc[index, 'date_updated'] = datetime.now() - timedelta(days=int(refresh_days_ago))
        #         log_message("Case Open and already refreshed")

        # else:
        #     log_message(f"Case {docket_number} is closed or not needs to be refreshed: Refreshed Date is {refresh_date} and threshold is {refresh_days_threshold}")
        df.loc[index, 'date_updated'] = datetime.now() - timedelta(days=refresh_days_ago)

        df.loc[index, 'date_checked'] = datetime.now()
        
    except Exception as e:
        print(f"Error in get_case_details for {docket_number} from {court}: {e}")
        print(f"Traceback:\n{traceback.format_exc()}")

    return refresh


def get_case_steps_and_files(driver, docket_number, court, date_filed, steps_df, file_type, nas):
# Note: This is only getting the steps, not the files at the moment!
    pacer_file_download_count = 0
    total_files_downloaded = 0

    try:
        # Wait for the docket sheet to be present
        table = WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.ID, "docket_sheet")))

        # Method 1: Filter by div ID pattern
        # Count hyphens in ID to get main entries only (4 hyphens)
        main_entries_m1 = [
            div for div in table.find_elements(By.CLASS_NAME, 'docket_sheet_row') 
            if div.get_attribute('id') and (
                div.get_attribute('id').count('-') == 4 or
                (div.get_attribute('id').count('-') == 5 and div.get_attribute('id').split('-')[-1].startswith('t'))
            )
        ]

        # Method 2: Exclude attachments
        # Get rows with docket_sheet_row class but not attachment class
        main_entries_m2 = table.find_elements(By.CSS_SELECTOR, 'div.docket_sheet_row:not(.attachment)')

        # Compare methods
        print(f"\nComparing methods for case {docket_number}:")
        print(f"Method 1 (hyphen count) found {len(main_entries_m1)} entries")
        print(f"Method 2 (class selection) found {len(main_entries_m2)} entries")

        # Compare entry IDs
        ids_m1 = set(div.get_attribute('id') for div in main_entries_m1)
        ids_m2 = set(div.get_attribute('id') for div in main_entries_m2)

        # Find differences
        only_in_m1 = ids_m1 - ids_m2
        only_in_m2 = ids_m2 - ids_m1

        if only_in_m1:
            print("\nEntries only in Method 1:")
            for id in only_in_m1:
                print(f"  - {id}")

        if only_in_m2:
            print("\nEntries only in Method 2:")
            for id in only_in_m2:
                print(f"  - {id}")

        if not (only_in_m1 or only_in_m2):
            print("\nBoth methods found the same entries!")

        print("\n")

        docket_entries = main_entries_m2
        
        for entry in docket_entries:
            # Extract required fields
            number_elem = entry.find_element(By.CLASS_NAME, "docket_sheet_number")
            date_elem = entry.find_element(By.CLASS_NAME, "filing_date")
            text_elem = entry.find_element(By.CLASS_NAME, "document_title")
            
            # Get download link status (Free/PACER)
            downloadable = "downloadable" in entry.get_attribute('class')
            
            # Create new row
            new_row = pd.DataFrame({
                'step_nb': [number_elem.text.strip()],
                'step_date_filed': [pd.to_datetime(date_elem.text.strip()).date()],
                'proceeding_text': [text_elem.text.strip()],
                'docket': [docket_number],
                'court': [court],
                'priority_name': [assess_download_requirement(downloadable, text_elem.text.strip())],
                'files_downloaded': [None],
                'files_number': [None],
                'files_failed': [None]
            })
            steps_df = pd.concat([steps_df, new_row], ignore_index=True)

        return steps_df, True, pacer_file_download_count, total_files_downloaded

    except Exception as e:
        print(f"Error get_case_steps_and_files: {docket_number} from {court}: {e}")
        error_msg = str(e)
        try:
            error_dir = save_browser_state(driver, error_msg, traceback.format_exc())
            log_message(f"Error state saved to: {error_dir}", level='ERROR')
        except Exception as save_error:
            log_message(f"Failed to save error state: {save_error}", level='ERROR')

        print(f"Traceback:\n{traceback.format_exc()}")
        return steps_df, False, pacer_file_download_count, total_files_downloaded