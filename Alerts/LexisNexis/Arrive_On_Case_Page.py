# Alerts/Arrive_On_Case_Page.py
import pandas as pd
from datetime import datetime
from typing import Dict, Any, Optional, Tuple
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from logdata import log_message
from Alerts.LexisNexis import Search_Cases
from typing import Any
import traceback
import langfuse
from langfuse import observe

@observe(name="Arrive On Case Page", capture_input=False, capture_output=False)
def arrive_on_case_page(driver, cases_df: pd.DataFrame, case_identifier: Dict[str, Any]) -> Tuple[bool, pd.DataFrame, Optional[Dict[str, Any]]]:
    """
    Identifies the case, navigates to the case page on LexisNexis.
    Returns:
        A tuple: (bool success_status, pd.DataFrame modified_case_row_df_slice_or_empty, Optional[Dict] data_for_next_step).
                 The DataFrame is a slice of the processed case row or an empty DataFrame on failure.
                 The Dict contains necessary data for the next processing step if successful.
    """

    # Initial log message from original function context
    log_message(f"📥 Starting case arrival for: {case_identifier}", level='INFO')

    idx = None
    nav_success_for_span = "Failed/Skipped" # For span logging

    langfuse.get_client().update_current_span(
        input={"CaseIdentifierKeys": list(case_identifier.keys()), "CaseID_param": case_identifier.get('case_id'), "Docket_param": case_identifier.get('docket'), "Court_param": case_identifier.get('court'), "InitialCasesDfShape": cases_df.shape}
    )

    try:
        ### 1. Identify Case Row, Filing Date, navigate to case page on LexisNexis ###
        if case_identifier.get('case_id'):
            case_indices = cases_df.index[cases_df['id'] == case_identifier.get('case_id')].tolist()
            if not case_indices:
                log_message(f"❌ Cannot find case {case_identifier.get('case_id')} in DataFrame.", level='ERROR')
                langfuse.get_client().update_current_span(output="Error: Case ID not found")
                return False, pd.DataFrame(), None
            idx = case_indices[0]
            log_message(f"   ✅ Found existing case {case_identifier.get('case_id')} in the Database at index {idx}.", level='INFO')
        elif case_identifier.get('docket') and case_identifier.get('court') and case_identifier.get('date_filed'):
            log_message(f"ℹ️ Processing potential new case: {case_identifier.get('docket')} / {case_identifier.get('court')} / {case_identifier.get('date_filed')}", level='INFO')
            try:
                case_identifier['date_filed'] = datetime.strptime(case_identifier.get('date_filed'), '%Y-%m-%d').date()
                filtered_cases = cases_df[
                    (cases_df['docket'] == case_identifier.get('docket')) &
                    (cases_df['court'] == case_identifier.get('court')) &
                    (cases_df['date_filed'] == case_identifier.get('date_filed'))
                ]
                if not filtered_cases.empty:
                    idx = filtered_cases.index[0]
                    log_message(f"   ✅ Found existing case matching docket/court/date at index {idx}.", level='INFO')
                    title = cases_df.loc[idx].get('title', title)
                else:
                    log_message(f"✨ Case {case_identifier.get('docket')} not found in DB, treating as new.", level='INFO')
                    new_case_series = pd.Series(case_identifier)
                    new_case_series['id'] = None
                    idx = len(cases_df) # Potential new index
                    aligned_series = new_case_series.reindex(cases_df.columns)
                    cases_df.loc[idx] = aligned_series
                    log_message(f"   ✅ Appended new case placeholder row in-place. Assigned index {idx}.", level='INFO')
            except (ValueError, TypeError) as e:
                log_message(f"   ❌ Invalid date format '{case_identifier.get('date_filed')}' when checking for existing case: {e}", level='ERROR')
                langfuse.get_client().update_current_span(output=f"Error: Invalid date format {e}")
                return False, pd.DataFrame(), None # No specific row to return if new and date invalid
            except KeyError as e:
                log_message(f"   ❌ Missing expected column in DataFrame during check: {e}", level='ERROR')
                langfuse.get_client().update_current_span(output=f"Error: Missing column {e}")
                return False, pd.DataFrame(), None
        else:
            log_message(f"   ❌ Insufficient information to identify case: {case_identifier}. Need case_id or docket/court/date_filed.", level='ERROR')
            langfuse.get_client().update_current_span(output="Error: Insufficient case information")
            return False, pd.DataFrame(), None

        # Navigate to Case Page (if needed)
        nav_success = False
        # Try to get current page titles to check if already on the page
        try:
            page_titles_elements = WebDriverWait(driver, 3).until(EC.presence_of_all_elements_located((By.ID, "SS_DocumentTitle")))
            current_page_title_text = page_titles_elements[0].text if page_titles_elements else ""
        except TimeoutException:
            current_page_title_text = "" # Not on a page with SS_DocumentTitle or it's very slow

        docket_number = cases_df.loc[idx, 'docket']
        if docket_number and docket_number.split("-cv-")[-1].lstrip('0') in current_page_title_text:
            log_message(f"   ℹ️ Already on the case page for {docket_number}.", level='INFO')
            nav_success_for_span = "AlreadyOnPage"
            nav_success = True
        elif cases_df.loc[idx, 'ln_url']: # If not already on page, try ln_url if available
            log_message(f"🚗 Attempting direct navigation using ln_url: {cases_df.loc[idx, 'ln_url']} for {docket_number}", level='INFO')
            try:
                driver.get(cases_df.loc[idx, 'ln_url'])
                WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.ID, "Header"))) # Wait for header

                # Verify if the loaded page is the correct one
                page_titles_after_ln_url_nav_elements = WebDriverWait(driver, 5).until(EC.presence_of_all_elements_located((By.ID, "SS_DocumentTitle")))
                page_title_after_ln_url_nav = page_titles_after_ln_url_nav_elements[0].text if page_titles_after_ln_url_nav_elements else ""

                if docket_number and docket_number.split("-cv-")[-1].lstrip('0') in page_title_after_ln_url_nav:
                    log_message(f"✅ Successfully navigated to {docket_number} using ln_url. Title matches.", level='INFO')
                    nav_success = True
                    nav_success_for_span = "SucceededViaLNURL"
                elif page_title_after_ln_url_nav: # Page loaded, title present but doesn't match docket, or docket_number is None
                    log_message(f"✅ Navigated using ln_url. Page title: '{page_title_after_ln_url_nav}'. Docket match inconclusive. Assuming correct page.", level='INFO')
                    nav_success = True
                    nav_success_for_span = "SucceededViaLNURL_TitleMismatchOrNoDocket"
                else: # Header loaded, but no SS_DocumentTitle found quickly
                    log_message(f"✅ Navigated using ln_url. Header found, but specific title check inconclusive. Assuming correct page.", level='INFO')
                    nav_success = True
                    nav_success_for_span = "SucceededViaLNURL_HeaderOnly"
            except TimeoutException:
                log_message(f"⚠️ Timeout after navigating with ln_url for {docket_number}. Page might not have loaded correctly or is not the expected case page. Falling back to search.", level='WARNING')
                nav_success = False # Fallback to search
            except Exception as e_ln_url_nav:
                log_message(f"⚠️ Error during navigation with ln_url for {docket_number}: {e_ln_url_nav}. Falling back to search.", level='WARNING')
                nav_success = False # Fallback to search

        if not nav_success: # If not already on page, and ln_url not used or failed
            log_message(f"🚗 Navigating to case page for {docket_number} by searching...", level='INFO')
            search_nav_success = Search_Cases.lexis_search_a_case_by_docket(driver, docket_number, cases_df.loc[idx, 'court'], cases_df.loc[idx, 'title'])
            if not search_nav_success:
                log_message(f"❌ Failed to navigate to case page for {docket_number} via search. Aborting.", level='ERROR')
                cases_df.loc[idx, 'file_status'] = "Failed: Search Navigation Error"
                return False, cases_df.loc[[idx]].copy(), None

            log_message(f"✅ Navigation successful for {docket_number} via search.", level='INFO')
            nav_success_for_span = "SucceededViaSearch"
            nav_success = True # Mark overall navigation as successful

        if not nav_success: # Should be caught by earlier returns, but as a safeguard
            log_message(f"❌ All navigation attempts failed for {docket_number}. Aborting.", level='ERROR')
            cases_df.loc[idx, 'file_status'] = "Failed: All Navigation Attempts Failed"
            return False, cases_df.loc[[idx]].copy(), None

        log_message("---------------------------------------") # End of this part's direct actions before handoff

        # Determine the DataFrame slice to return. If idx is valid, return that row.
        df_slice_to_return = cases_df.loc[[idx]].copy() if idx is not None and idx in cases_df.index else pd.DataFrame()
        langfuse.get_client().update_current_span(output=f"{{NavSuccess: {nav_success_for_span}}}")
        return True, df_slice_to_return, idx

    except Exception as e: # Catch-all for unexpected errors in this function
        log_message(f"💥 Unhandled exception in arrive_on_case_page: {e}\n{traceback.format_exc()}", level='CRITICAL')
        langfuse.get_client().update_current_span(output=f"Critical Error in arrive_on_case_page: {str(e)[:100]}")

        df_to_return_on_error = pd.DataFrame()
        if idx is not None and idx in cases_df.index:
            try:
                cases_df.loc[idx, 'file_status'] = f"Failed: Critical Arrival Error - {str(e)[:50]}"
                df_to_return_on_error = cases_df.loc[[idx]].copy()
            except Exception as df_err:
                log_message(f"❌ Failed to update error status in DataFrame during critical error handling: {df_err}", level='ERROR')
        return False, df_to_return_on_error, None