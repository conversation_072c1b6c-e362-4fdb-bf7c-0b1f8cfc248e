import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) # Add the project root directory to the Python path
from datetime import datetime, timedelta, date
import pandas as pd
import traceback
import time
import asyncio # Keep for local_run_process_files if needed

# Import necessary components for refactored logic
from . import zz_WorkflowManager
# Removed: from DatabaseManagement.database_handler import DatabaseHandler
from DatabaseManagement.ImportExport import get_table_from_GZ # Keep if used in local runs
from Common.Constants import FILE_TYPE_STRATEGY_IP_ONLY # Import default strategy
from logdata import log_message, update_step_status, finish_run, get_context, task_context # Keep for logging/run status



# The main task - Refactored to use WorkflowManager
def get_alerts(run_id, selected_date, loop_back_days, force_redo=False):
    """
    Main entry point for daily alert processing, now delegates to WorkflowManager.
    """
    log_message(f'{time.strftime("%Y-%m-%d %H:%M:%S")}: Starting Job Id {run_id} via WorkflowManager...')
    start_time = time.time()

    # Define default options for the workflow
    # These could potentially be loaded from config
    default_options = {
        'file_type_strategy': FILE_TYPE_STRATEGY_IP_ONLY, # Example default
        'force_refresh': force_redo, # Pass the specific flag from the wrapper
        'update_steps': True, # Default for daily run
        'process_pictures': True,
        'upload_files': True,
        # 'run_ai_tasks': True, # Deprecated - use specific flags
        'run_plaintiff_overview': True, # Enable AI for daily fetch
        'run_summary_translation': True,
        'run_step_translation': True,
        'refresh_cache_after_ai': True,
        'max_retries': 3,
        'delay_between_retries': 5
    }

    try:
        # Convert selected_date string to date object if necessary
        if isinstance(selected_date, str):
            selected_date_obj = datetime.strptime(selected_date, '%Y-%m-%d').date()
        elif isinstance(selected_date, datetime):
             selected_date_obj = selected_date.date()
        elif isinstance(selected_date, date):
             selected_date_obj = selected_date
        else:
             raise ValueError("selected_date must be a date string (YYYY-MM-DD), datetime, or date object")

        # Call the WorkflowManager function
        zz_WorkflowManager.process_daily_fetch(
            run_id=run_id,
            selected_date=selected_date_obj,
            loop_back_days=loop_back_days,
            force_redo=force_redo,
            options=default_options
        )

        duration = time.time() - start_time
        log_message(f"Job Id {run_id} completed via WorkflowManager in {duration:.1f} seconds")
        # finish_run is likely called within WorkflowManager now, remove if redundant
        # finish_run(run_id, 'Completed') # Check if WorkflowManager handles this

    except Exception as e:
        error_msg = traceback.format_exc()
        log_message(f'Error during get_alerts orchestration (Run ID: {run_id}): {error_msg}', level='CRITICAL')
        # finish_run is likely called within WorkflowManager's error handling, remove if redundant
        # finish_run(run_id, 'Failed') # Check if WorkflowManager handles this
        # Re-raise the exception so the process wrapper knows it failed
        raise


# --- Removed Helper Functions ---
# search_and_get_cases, process_files, upload_to_database, upload_files, do_ai_tasks
# These functions' logic should now be encapsulated within Alerts.CaseProcessor.process_case
# and orchestrated by Alerts.WorkflowManager.


# --- Refactored Local Run Functions ---

# A1. Local run for testing: get cases for a date range and process them.
def local_run_get_cases_and_upload_to_database(date_start, date_end, file_type="1+free+evidence", redo=False):
    """
    Local run to process cases within a date range using WorkflowManager.
    Fetches case IDs first, then calls process_specific_cases.
    """
    log_message(f"--- Starting Local Run: Process Cases from {date_start} to {date_end} ---", level='INFO')
    run_id = int(time.time()) # Simple run_id for local test
    log_message(f"Run ID: {run_id}", level='INFO')

    # Define options for this local run
    local_options = {
        'file_type_strategy': file_type, # Use provided file_type
        'force_refresh': redo,
        'update_steps': True,
        'process_pictures': True,
        'upload_files': True, # Set based on local testing needs
        # 'run_ai_tasks': True, # Deprecated - use specific flags
        'run_plaintiff_overview': True, # Enable AI for local runs (configurable)
        'run_summary_translation': True,
        'run_step_translation': True,
        'refresh_cache_after_ai': True,
        'max_retries': 2,
        'delay_between_retries': 3
    }

    try:
        # 1. Find case IDs within the date range
        # NOTE: This local run function now requires modification to find cases without DatabaseHandler.
        #       It could filter a loaded DataFrame or use another method.
        #       For now, it will likely fail or process an empty list.
        log_message(f"Finding cases between {date_start.date()} and {date_end.date()}...", level='INFO')
        log_message("WARNING: DatabaseHandler removed. Case finding logic needs update for local run.", level='WARNING')
        # Placeholder: Add logic here to get case IDs from another source (e.g., filtering cases_df)
        # Example:
        # cases_df = get_table_from_GZ("tb_case")
        # cases_df['date_filed_dt'] = pd.to_datetime(cases_df['date_filed']).dt.date
        # date_filtered_df = cases_df[
        #     (cases_df['date_filed_dt'] >= date_start.date()) &
        #     (cases_df['date_filed_dt'] <= date_end.date())
        # ]
        # case_ids_to_process = date_filtered_df['id'].tolist()
        case_ids_to_process = [] # Defaulting to empty list

        if not case_ids_to_process:
            log_message("No cases found in the specified date range.", level='INFO')
            return

        log_message(f"Found {len(case_ids_to_process)} cases to process.", level='INFO')

        # 3. Call WorkflowManager to process these specific cases
        zz_WorkflowManager.process_specific_cases(
            run_id=run_id,
            case_list=case_ids_to_process,
            options=local_options
        )

        log_message(f"--- Local Run (Run ID: {run_id}) Completed ---", level='INFO')

    except Exception as e:
        log_message(f"--- Local Run (Run ID: {run_id}) Failed: {e} ---", level='ERROR', exc_info=True)
        # traceback.print_exc() # log_message with exc_info=True handles this


# A2. Local run for testing: get cases for a date range (all at once) - Effectively same as A1 now.
def local_run_get_cases_and_upload_to_database_all_at_once(date_start, date_end, file_type="1+free", redo=False):
    """
    Local run to process cases within a date range using WorkflowManager.
    (Functionally similar to local_run_get_cases_and_upload_to_database after refactoring).
    """
    log_message(f"--- Starting Local Run (All at Once): Process Cases from {date_start} to {date_end} ---", level='INFO')
    # Delegate to the other local run function as the logic is now identical
    local_run_get_cases_and_upload_to_database(date_start, date_end, file_type, redo)


# A3. Local run for testing: get a list of cases using dockets.
def local_run_get_cases_and_upload_to_database_using_dockets(df_cases_input, file_type="1+free", redo=False):
    """
    Local run to process a specific list of cases (provided as DataFrame) using WorkflowManager.
    """
    log_message(f"--- Starting Local Run: Process {len(df_cases_input)} Specific Dockets ---", level='INFO')
    run_id = int(time.time())
    log_message(f"Run ID: {run_id}", level='INFO')

    # Define options
    local_options = {
        'file_type_strategy': file_type,
        'force_refresh': redo,
        'update_steps': True,
        'process_pictures': True,
        'upload_files_nas': True,
        'upload_files_cos': True,
        'run_plaintiff_overview': True,
        'run_summary_translation': True,
        'run_step_translation': True,
        'refresh_cache_after_ai': True,
        'max_retries': 2,
        'delay_between_retries': 3
    }

    # Extract case identifiers (assuming df_cases_input has 'id' or 'docket'/'court'/'date_filed')
    case_identifiers = []
    if 'id' in df_cases_input.columns:
         # Prefer using ID if available
        case_identifiers = df_cases_input['id'].tolist()
        log_message(f"Processing based on {len(case_identifiers)} Case IDs.", level='INFO')
    elif all(col in df_cases_input.columns for col in ['docket', 'court', 'date_filed']):
        # Fallback to docket/court/date if ID is missing
        for _, row in df_cases_input.iterrows():
             case_identifiers.append({
                 'docket': row['docket'],
                 'court': row['court'],
                 'date': str(row['date_filed'].date()) # Ensure date is string YYYY-MM-DD
             })
        log_message(f"Processing based on {len(case_identifiers)} Docket/Court/Date identifiers.", level='INFO')
    else:
        log_message("Error: Input DataFrame must contain 'id' column or 'docket', 'court', 'date_filed' columns.", level='ERROR')
        return

    if not case_identifiers:
        log_message("No valid case identifiers found in the input DataFrame.", level='WARNING')
        return

    try:
        # Call WorkflowManager
        zz_WorkflowManager.process_specific_cases(
            run_id=run_id,
            case_list=case_identifiers,
            options=local_options
        )
        log_message(f"--- Local Run (Run ID: {run_id}) Completed ---", level='INFO')

    except Exception as e:
        log_message(f"--- Local Run (Run ID: {run_id}) Failed: {e} ---", level='ERROR', exc_info=True)


# B. Local run for testing: process files and upload for specific cases.
def local_run_process_files(date_start=None, date_end=None, docket_nb=None, case_ids=None, force=False):
    """
    Local run to process specific cases (identified by date, docket, or ID list) using WorkflowManager.
    This replaces the old logic that directly called processing steps.
    """
    log_message(f"--- Starting Local Run: Process Files ---", level='INFO')
    run_id = int(time.time())
    log_message(f"Run ID: {run_id}", level='INFO')

    # Define options
    local_options = {
        'file_type_strategy': FILE_TYPE_STRATEGY_IP_ONLY, # Default or make configurable
        'force_refresh': force, # Use force flag for refresh
        'update_steps': True, # Assume we want to update steps if processing files
        'process_pictures': True, # Assume processing pictures is the goal
        'upload_files': True, # Assume upload needed
        # 'run_ai_tasks': True, # Deprecated
        'run_plaintiff_overview': True,
        'run_summary_translation': True,
        'run_step_translation': True,
        'refresh_cache_after_ai': True,
        'max_retries': 2,
        'delay_between_retries': 3
    }

    identifiers_to_process = []

    try:
        # Initialize DB handler locally if needed for finding cases
        # NOTE: This local run function now requires modification to find cases without DatabaseHandler.
        #       It could filter a loaded DataFrame or use another method.
        #       For now, it will likely fail or process an empty list.
        log_message("WARNING: DatabaseHandler removed. Case finding logic needs update for local run.", level='WARNING')
        # Placeholder: Add logic here to get case IDs from another source (e.g., filtering cases_df)
        # db_handler = None # Removed

        if case_ids:
            log_message(f"Processing specific Case IDs: {case_ids}", level='INFO')
            identifiers_to_process = list(case_ids)
        elif docket_nb:
            log_message(f"Finding cases matching docket number: {docket_nb}", level='INFO')
            # Placeholder: Replace with actual DB method
            # case_infos = db_handler.find_cases_by_docket(docket_nb) # Removed db_handler call
            case_infos = [] # Placeholder
            identifiers_to_process = [info['id'] for info in case_infos if 'id' in info]
        elif date_start and date_end:
            log_message(f"Finding cases between {date_start.date()} and {date_end.date()}", level='INFO')
            # Placeholder: Replace with actual DB method
            # case_infos = db_handler.find_cases_for_daily_run(date_start.date(), date_end.date(), force_redo=True) # Removed db_handler call
            case_infos = [] # Placeholder
            identifiers_to_process = [info['id'] for info in case_infos if 'id' in info]
        else:
            log_message("Error: Must provide case_ids, docket_nb, or date_start/date_end.", level='ERROR')
            return

        if not identifiers_to_process:
            log_message("No cases found matching the criteria.", level='WARNING')
            return

        log_message(f"Found {len(identifiers_to_process)} cases to process.", level='INFO')

        # Call WorkflowManager
        zz_WorkflowManager.process_specific_cases(
            run_id=run_id,
            case_list=identifiers_to_process,
            options=local_options
        )

        log_message(f"--- Local Run (Run ID: {run_id}) Completed ---", level='INFO')

    except Exception as e:
        log_message(f"--- Local Run (Run ID: {run_id}) Failed: {e} ---", level='ERROR', exc_info=True)


if __name__ == "__main__":
   # Example usage for refactored local runs (replace with actual data/dates)
   log_message("Get_Alerts.py refactored. Use local run functions for testing.", level='INFO')

   # --- Example: Test processing cases for a date range ---
    # test_start_date = datetime(2024, 4, 1)
    # test_end_date = datetime(2024, 4, 2)
    # local_run_get_cases_and_upload_to_database(test_start_date, test_end_date, file_type='ip_only', redo=False)

    # --- Example: Test processing specific dockets (using a dummy DataFrame) ---
    # try:
    #     # Create a dummy DataFrame for testing
    #     dummy_data = {
    #         'docket': ['1:24-cv-00123', '2:24-cv-00456'],
    #         'court': ['cand', 'nyed'],
    #         'date_filed': [date(2024, 4, 1), date(2024, 4, 2)],
    #         # Add other columns if needed by the function or CaseProcessor's lookup
    #         'title': ['Dummy Case 1', 'Dummy Case 2']
    #     }
    #     df_dummy_cases = pd.DataFrame(dummy_data)
    #     df_dummy_cases['date_filed'] = pd.to_datetime(df_dummy_cases['date_filed']).dt.date # Ensure correct type
    #     local_run_get_cases_and_upload_to_database_using_dockets(df_dummy_cases, file_type='ip_only', redo=True)
    # except Exception as e:
    #     log_message(f"Error setting up or running docket test: {e}", level='ERROR', exc_info=True)


    # --- Example: Test processing files for specific case IDs ---
    # test_case_ids = [12345, 67890] # Replace with actual IDs
    # local_run_process_files(case_ids=test_case_ids, force=True)

pass # Keep the script runnable