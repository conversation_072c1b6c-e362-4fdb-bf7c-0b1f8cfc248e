# Alerts/WorkflowManager.py
import pandas as pd
from datetime import date, timedelta, datetime
from typing import List, Dict, Any, Optional, Union, Tuple
import queue # For progress reporting in process_single_case
from selenium.common.exceptions import WebDriverException # Added for driver errors
from selenium.webdriver.common.by import By # Added for finding elements

# Import LexisNexis modules
from Alerts.LexisNexis import Login, Search_Cases # Added (Get_Cases removed as specific functions imported)
from Alerts.LexisNexis.Search_Cases import wait_for_results_to_stabilize, go_to_next_page # Specific imports for clarity
from Common.Constants import court_mapping # Needed for parsing results

# Import the core processor
from . import CaseProcessor
from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
from logdata import log_message

# --- Workflow Functions ---

def process_daily_fetch(
    run_id: int,
    selected_date: date,
    loop_back_days: int,
    force_redo: bool,
    options: Dict[str, Any]
):
    """
    Processes cases based on a date range, similar to the old Get_Alerts.

    Args:
        run_id: Identifier for this processing run.
        selected_date: The target date for fetching cases.
        loop_back_days: How many days prior to selected_date to include.
        force_redo: Flag indicating if cases already processed should be redone.
        options: General processing options passed down to CaseProcessor.
                 Should include AI flags: 'run_plaintiff_overview', 'run_summary_translation', 'run_step_translation', 'refresh_cache_after_ai'.
    """
    log_message(f"🚀 Starting daily fetch workflow. Run ID: {run_id}, Date: {selected_date}, Loopback: {loop_back_days}, Force Redo: {force_redo}", level='INFO')
    modified_indices = set() # Track modified rows for saving
    driver = None # Initialize driver variable

    start_date = selected_date - timedelta(days=loop_back_days)
    end_date = selected_date

    # Log start of run
    # db_handler.log_run_start(run_id, 'daily_fetch', {'selected_date': str(selected_date), 'loop_back_days': loop_back_days, 'force_redo': force_redo}) # Commented out DB logging

    try:
        # 1. Initialize Browser and Login
        log_message("🌐 Initializing browser and logging into LexisNexis...", level='INFO')
        driver = Login.get_logged_in_browser() # Assuming this returns a configured driver
        if not driver:
            raise Exception("Failed to initialize browser or login to LexisNexis.")
        log_message("✅ Browser initialized and logged in.", level='INFO')

        # 2. Perform LexisNexis Search
        log_message(f"🔍 Performing LexisNexis search for date range: {start_date} to {end_date}", level='INFO')
        Search_Cases.lexis_search_cases_by_date(driver, start_date, end_date)
        log_message("✅ LexisNexis search initiated.", level='INFO')

        # 3. Load DataFrames (needed for cross-referencing and updates)
        log_message("💾 Loading cases and plaintiffs data...", level='INFO')
        cases_df = get_table_from_GZ("tb_case", force_refresh=True) # Force refresh for daily run?
        plaintiff_df = get_table_from_GZ("tb_plaintiff")
        log_message(f"📊 Loaded {len(cases_df)} cases and {len(plaintiff_df)} plaintiffs.", level='INFO')

        # 4. Prepare options for CaseProcessor (Pass through AI flags)
        case_options = options.copy()
        case_options['force_refresh'] = force_redo # Override force_refresh based on workflow param
        # Ensure AI flags are present, defaulting to False if not provided in input `options`
        case_options.setdefault('run_plaintiff_overview', False)
        case_options.setdefault('run_summary_translation', False)
        case_options.setdefault('run_step_translation', False)
        case_options.setdefault('refresh_cache_after_ai', True) # Default to True

        # 5. Iterate through LexisNexis Results Pages and process each case
        processed_count = 0
        failed_count = 0
        page_number = 1
        while True:
            log_message(f"📄 Processing LexisNexis results page {page_number}...", level='INFO')
            results = wait_for_results_to_stabilize(driver) # Use imported function
            log_message(f"📊 Found {len(results)} results on page {page_number}.", level='INFO')

            if not results:
                # Check for "No documents found" message
                try:
                    # Use By from selenium.webdriver.common.by
                    no_results_element = driver.find_element(By.CSS_SELECTOR, '.results-list.search.noresults')
                    if no_results_element:
                        log_message("✅ No more results found on LexisNexis.", level='INFO')
                        break # Exit loop if no results container is found
                except: # NoSuchElementException expected if results exist
                    pass # Continue processing if no "no results" element found

                # If results list is empty but no "no results" element, might be end of pages or error
                log_message("⚠️ Empty results list encountered, assuming end of pages or potential issue.", level='WARNING')
                break # Exit loop

            for i, result_element in enumerate(results):
                try:
                    # Re-acquire elements to avoid staleness (important inside loop)
                    current_results = wait_for_results_to_stabilize(driver)
                    if i >= len(current_results): continue # Check if index is still valid
                    element = current_results[i]

                    link_element = element.find_element(By.CSS_SELECTOR, 'h2.doc-title a')
                    element_metadata = element.find_element(By.CLASS_NAME, 'metadata')
                    spans = element_metadata.find_elements(By.TAG_NAME, 'span')

                    court_raw = spans[0].text.strip()
                    court = court_mapping.get(court_raw, court_raw) # Use imported mapping
                    date_filed_str = spans[1].text.strip()
                    date_filed = pd.to_datetime(date_filed_str, errors='coerce').date()
                    docket_raw = spans[2].text.strip()
                    # Basic cleaning - CaseProcessor might do more specific cleaning
                    docket_number = docket_raw.replace("CV", "cv").replace("cv", "-cv-")
                    title = link_element.text.strip()

                    if not date_filed or "cv" not in docket_number.lower():
                        log_message(f"⚠️ Skipping result {i+1} on page {page_number} due to invalid date or docket: {docket_raw}, {date_filed_str}", level='WARNING')
                        continue

                    log_message(f"➡️ Identifying case: Docket={docket_number}, Court={court}, Date={date_filed}", level='DEBUG')

                    # Check if case needs processing based on DB status and force_redo flag
                    existing_case = cases_df[(cases_df['docket'] == docket_number) & (cases_df['court'] == court)] # Match on docket & court
                    case_id_to_process = None
                    process_this_case = True
                    db_index = None

                    if not existing_case.empty:
                        db_index = existing_case.index[0]
                        case_id_to_process = existing_case.iloc[0]['id']
                        status = existing_case.iloc[0]['file_status']
                        log_message(f"ℹ️ Case {docket_number} found in DB (ID: {case_id_to_process}, Status: {status}).", level='DEBUG')
                        if not force_redo:
                            success_statuses = ['Completed', 'IP Found', 'Completed - No IP', 'Completed - Check Needed'] # Reuse statuses
                            if status in success_statuses:
                                log_message(f"✅ Skipping already processed case {case_id_to_process} (force_redo=False).", level='INFO')
                                process_this_case = False
                    else:
                        log_message(f"✨ New case found on LexisNexis: {docket_number}. Will be added/processed.", level='INFO')
                        # CaseProcessor will handle adding it if it doesn't exist after trying to find by ID=None

                    if process_this_case:
                        log_message(f"🚀 Processing case: {docket_number} (DB ID: {case_id_to_process})", level='INFO')
                        case_identifier = {
                            'case_id': case_id_to_process, # Pass None if new
                            'docket': docket_number,
                            'court': court,
                            'date_filed': str(date_filed), # Pass date info
                            'title': title # Pass title info
                        }
                        # Step 1: Call arrive_on_case_page
                        arrival_success, arrival_df_slice, arrival_data = CaseProcessor.arrive_on_case_page(
                            driver=driver,
                            case_identifier=case_identifier,
                            cases_df=cases_df, # Pass the current state of cases_df
                            run_id=run_id
                        )

                        success = False # Default success to False
                        updated_case_data = arrival_df_slice if arrival_df_slice is not None else pd.DataFrame() # Default to slice from arrival

                        if arrival_success and arrival_data:
                            # Update cases_df with the one potentially modified by arrive_on_case_page
                            cases_df = arrival_data["cases_df"]

                            # Step 2: Call process_case with data from arrive_on_case_page
                            success, updated_case_data_from_proc = CaseProcessor.process_case(
                                driver=driver,
                                options=case_options,
                                plaintiff_df=plaintiff_df,
                                run_id=run_id,
                                **arrival_data # Unpack all returned data from arrival_data
                            )
                            # The main cases_df is updated within process_case if successful,
                            # and updated_case_data_from_proc is the slice of the modified row.
                            if not updated_case_data_from_proc.empty:
                                updated_case_data = updated_case_data_from_proc
                                # Update the main DataFrame with the results from process_case
                                # process_case returns a slice, so we update the main df
                                processed_case_id = updated_case_data.iloc[0]['id']
                                final_indices = cases_df.index[cases_df['id'] == processed_case_id].tolist()
                                if final_indices:
                                    db_index = final_indices[0]
                                    # cases_df.update(updated_case_data) # process_case modifies cases_df internally via arrival_data
                                    modified_indices.add(db_index)
                                else:
                                    log_message(f"⚠️ Could not find index for processed case ID {processed_case_id} in main DataFrame after process_case.", level='WARNING')
                            elif success: # Success true but empty df
                                log_message(f"⚠️ process_case for {docket_number} reported success but returned empty data.", level='WARNING')
                                # Keep updated_case_data as the slice from arrival if process_case returns empty
                        else:
                            # Arrival failed
                            log_message(f"❌ Arrival on case page failed for {docket_number}. Full processing aborted.", level='ERROR')
                            success = False # Ensure success is False
                            # updated_case_data is already set to arrival_df_slice
                            if not updated_case_data.empty: # If arrival returned a slice with error status
                                processed_case_id = updated_case_data.iloc[0].get('id')
                                if processed_case_id:
                                    final_indices = cases_df.index[cases_df['id'] == processed_case_id].tolist()
                                    if final_indices:
                                        db_index = final_indices[0]
                                        # cases_df.update(updated_case_data) # Ensure main df reflects error status from arrival
                                        modified_indices.add(db_index)
                            # Clean up Langfuse trace if arrival_data exists (trace/span were created)
                            if arrival_data:
                                if arrival_data.get("span_identify_case"):
                                    try:
                                        arrival_data["span_identify_case"].end(output="Failed during case arrival phase in daily_fetch")
                                    except Exception as e_span:
                                        log_message(f"Error ending span_identify_case on arrival failure (daily_fetch): {e_span}", "ERROR")
                                if arrival_data.get("process_case_trace"):
                                    try:
                                        arrival_data["process_case_trace"].update(output="OverallSuccess: False, FinalCaseStatus: Failed during case arrival in daily_fetch")
                                    except Exception as e_trace:
                                        log_message(f"Error updating process_case_trace on arrival failure (daily_fetch): {e_trace}", "ERROR")


                        if success:
                            processed_count += 1
                        else:
                            failed_count += 1
                            log_message(f"❌ Failed to process case: {docket_number} during daily fetch (Run ID: {run_id}). Final success: {success}", level='ERROR')
                        # Optional: Add delay between cases if needed
                        # time.sleep(1)

                except WebDriverException as wd_err:
                    log_message(f"💥 WebDriver error processing result {i+1} on page {page_number}: {wd_err}", level='ERROR', exc_info=True)
                    failed_count += 1
                    # Attempt to recover or break? For now, continue to next result.
                except Exception as item_err:
                    log_message(f"💥 Error processing result {i+1} on page {page_number}: {item_err}", level='ERROR', exc_info=True)
                    failed_count += 1 # Count as failed

            # Try to go to the next page
            if not go_to_next_page(driver): # Use imported function
                log_message("✅ Reached the last page of LexisNexis results.", level='INFO')
                break # Exit loop if no next page
            page_number += 1
            # Optional: Add delay between pages
            # time.sleep(2)

        # 6. Log end of run and save modified data (Adjusted step number)
        final_status = f"Completed - Processed: {processed_count}, Failed: {failed_count}"
        log_message(f"🏁 Daily fetch workflow finished for Run ID: {run_id}. Status: {final_status}", level='INFO')
        # db_handler.log_run_end(run_id, final_status, processed_count, failed_count) # Commented out DB logging
        if modified_indices:
            log_message(f"💾 Saving {len(modified_indices)} modified case rows to database...", level='INFO')
            try:
                # Select only the modified rows using the collected indices
                rows_to_save = cases_df.loc[list(modified_indices)]
                # Define columns to save (adjust as needed, include 'id' and changed columns)
                columns_to_save = ['id', 'file_status', 'last_run_id', 'update_time', 'images', 'images_status'] # Add other potentially modified columns
                columns_exist = [col for col in columns_to_save if col in rows_to_save.columns]
                insert_and_update_df_to_GZ_batch(rows_to_save[columns_exist], "tb_case", "id")
                log_message("✅ Successfully saved modified case data.", level='INFO')
            except Exception as save_err:
                log_message(f"❌ Failed to save modified case data: {save_err}", level='ERROR', exc_info=True)

    except Exception as e:
        log_message(f"💥 Critical error during daily fetch workflow (Run ID: {run_id}): {e}", level='CRITICAL', exc_info=True)
        # db_handler.log_run_end(run_id, f"Failed: {e}") # Commented out DB logging
        # Re-raise or handle as appropriate for the application lifecycle
    finally:
        # Ensure browser is closed
        if driver:
            log_message("🌐 Closing browser...", level='INFO')
            try:
                driver.quit()
                log_message("✅ Browser closed.", level='INFO')
            except Exception as quit_err:
                log_message(f"⚠️ Error closing browser: {quit_err}", level='WARNING')

def process_specific_cases(
    run_id: int,
    case_list: List[Union[int, Dict[str, Any]]],
    options: Dict[str, Any]
):
    """
    Processes a specific list of cases identified by ID or details.

    Args:
        run_id: Identifier for this processing run.
        case_list: A list where each item is either a case_id (int) or a
                   dictionary with case details (e.g., {'docket': ..., 'court': ...}).
        options: Processing options passed down to CaseProcessor.
                 Should include AI flags: 'run_plaintiff_overview', 'run_summary_translation', 'run_step_translation', 'refresh_cache_after_ai'.
    """
    log_message(f"🚀 Starting specific cases workflow. Run ID: {run_id}, Cases: {len(case_list)}", level='INFO')
    modified_indices = set()
    driver = None # Initialize driver variable

    # Log start of run
    # db_handler.log_run_start(run_id, 'specific_cases', {'case_count': len(case_list)}) # Commented out DB logging

    try:
        # 1. Initialize Browser and Login
        log_message("🌐 Initializing browser and logging into LexisNexis...", level='INFO')
        driver = Login.get_logged_in_browser()
        if not driver:
            raise Exception("Failed to initialize browser or login to LexisNexis.")
        log_message("✅ Browser initialized and logged in.", level='INFO')

        # 2. Load DataFrames
        log_message("💾 Loading cases and plaintiffs data...", level='INFO')
        cases_df = get_table_from_GZ("tb_case", force_refresh=True) # Refresh needed? Maybe not always.
        plaintiff_df = get_table_from_GZ("tb_plaintiff")
        log_message(f"📊 Loaded {len(cases_df)} cases and {len(plaintiff_df)} plaintiffs.", level='INFO')

        # 3. Process Cases
        processed_count = 0
        failed_count = 0
        # Prepare options - ensure AI flags are present, defaulting if needed
        case_options = options.copy()
        case_options.setdefault('run_plaintiff_overview', False)
        case_options.setdefault('run_summary_translation', False)
        case_options.setdefault('run_step_translation', False)
        case_options.setdefault('refresh_cache_after_ai', True) # Default to True

        for case_identifier_item in case_list:
            try:
                identifier_dict = {}
                case_id_for_log = None # For logging errors
                if isinstance(case_identifier_item, int):
                    identifier_dict = {'case_id': case_identifier_item}
                    case_id_for_log = case_identifier_item
                    log_message(f"➡️ Processing case_id: {case_identifier_item} as part of Run ID: {run_id}", level='INFO')
                elif isinstance(case_identifier_item, dict):
                    identifier_dict = case_identifier_item
                    case_id_for_log = case_identifier_item.get('case_id') or case_identifier_item.get('docket')
                    log_message(f"➡️ Processing case identifier: {case_identifier_item} as part of Run ID: {run_id}", level='INFO')
                else:
                    log_message(f"⚠️ Skipping invalid case identifier type in list: {case_identifier_item}", level='WARNING')
                    failed_count += 1 # Count as failed if identifier is wrong
                    continue

                # Find the index in the DataFrame if case_id is known (process_case handles finding by docket/court/date too)
                current_case_id = identifier_dict.get('case_id')
                case_indices = []
                if current_case_id:
                    case_indices = cases_df.index[cases_df['id'] == current_case_id].tolist()

                # Step 1: Call arrive_on_case_page
                arrival_success, arrival_df_slice, arrival_data = CaseProcessor.arrive_on_case_page(
                    driver=driver,
                    case_identifier=identifier_dict,
                    cases_df=cases_df, # Pass current cases_df
                    run_id=run_id
                )

                success = False # Default
                updated_case_data = arrival_df_slice if arrival_df_slice is not None else pd.DataFrame()

                if arrival_success and arrival_data:
                    cases_df = arrival_data["cases_df"] # Update with potentially modified df

                    # Step 2: Call process_case
                    success, updated_case_data_from_proc = CaseProcessor.process_case(
                        driver=driver,
                        options=case_options,
                        plaintiff_df=plaintiff_df,
                        run_id=run_id,
                        **arrival_data
                    )
                    if not updated_case_data_from_proc.empty:
                        updated_case_data = updated_case_data_from_proc
                        processed_case_id = updated_case_data.iloc[0]['id']
                        final_indices = cases_df.index[cases_df['id'] == processed_case_id].tolist()
                        if final_indices:
                            modified_indices.add(final_indices[0])
                        else:
                            log_message(f"⚠️ Could not find index for processed case ID {processed_case_id} after process_case (specific_cases).", level='WARNING')
                    elif success:
                         log_message(f"⚠️ process_case for {case_identifier_item} reported success but returned empty data.", level='WARNING')
                else:
                    log_message(f"❌ Arrival on case page failed for {case_identifier_item}. Full processing aborted.", level='ERROR')
                    success = False
                    if not updated_case_data.empty:
                        processed_case_id = updated_case_data.iloc[0].get('id')
                        if processed_case_id:
                            final_indices = cases_df.index[cases_df['id'] == processed_case_id].tolist()
                            if final_indices:
                                modified_indices.add(final_indices[0])
                    # Clean up Langfuse trace
                    if arrival_data:
                        if arrival_data.get("span_identify_case"):
                            try:
                                arrival_data["span_identify_case"].end(output="Failed during case arrival phase in specific_cases")
                            except Exception as e_span:
                                log_message(f"Error ending span_identify_case on arrival failure (specific_cases): {e_span}", "ERROR")
                        if arrival_data.get("process_case_trace"):
                            try:
                                arrival_data["process_case_trace"].update(output="OverallSuccess: False, FinalCaseStatus: Failed during case arrival in specific_cases")
                            except Exception as e_trace:
                                log_message(f"Error updating process_case_trace on arrival failure (specific_cases): {e_trace}", "ERROR")

                if success:
                    processed_count += 1
                else:
                    failed_count += 1
                    log_message(f"❌ Failed to process case: {case_identifier_item} during specific cases run (Run ID: {run_id}). Final success: {success}", level='ERROR')
                # Optional: Add delay

            except Exception as e:
                log_message(f"💥 Error processing item {case_identifier_item} in specific cases run (Run ID: {run_id}): {e}", level='ERROR', exc_info=True)
                failed_count += 1
                # Optionally update DB status for this specific case if possible (handled by process_case now)

        # Log end of run and save (Indented to be inside the main try block)
        final_status = f"Completed - Processed: {processed_count}, Failed: {failed_count}"
        log_message(f"🏁 Specific cases workflow finished for Run ID: {run_id}. Status: {final_status}", level='INFO')
        # db_handler.log_run_end(run_id, final_status, processed_count, failed_count) # Commented out DB logging
        if modified_indices:
            log_message(f"💾 Saving {len(modified_indices)} modified case rows to database...", level='INFO')
            try:
                rows_to_save = cases_df.loc[list(modified_indices)]
                columns_to_save = ['id', 'file_status', 'last_run_id', 'update_time', 'images', 'images_status'] # Adjust as needed
                columns_exist = [col for col in columns_to_save if col in rows_to_save.columns]
                insert_and_update_df_to_GZ_batch(rows_to_save[columns_exist], "tb_case", "id")
                log_message("✅ Successfully saved modified case data.", level='INFO')
            except Exception as save_err:
                log_message(f"❌ Failed to save modified case data: {save_err}", level='ERROR', exc_info=True)

    except Exception as e: # Correctly aligned with the 'try' at line 267
        log_message(f"💥 Critical error during specific cases workflow (Run ID: {run_id}): {e}", level='CRITICAL', exc_info=True)
        # db_handler.log_run_end(run_id, f"Failed: {e}") # Commented out DB logging
    finally:
        # Ensure browser is closed
        if driver:
            log_message("🌐 Closing browser...", level='INFO')
            try:
                driver.quit()
                log_message("✅ Browser closed.", level='INFO')
            except Exception as quit_err:
                log_message(f"⚠️ Error closing browser: {quit_err}", level='WARNING')

def process_single_case(
    case_id: int,
    options: Dict[str, Any],
    progress_queue: Optional[queue.Queue] = None
):
    """
    Processes a single case, potentially with progress reporting.

    Args:
        case_id: The ID of the case to process.
        options: Processing options passed down to CaseProcessor.
                 Should include AI flags: 'run_plaintiff_overview', 'run_summary_translation', 'run_step_translation', 'refresh_cache_after_ai'.
        progress_queue: Optional queue for sending progress updates (e.g., to a UI).
    """
    log_message(f"🚀 Starting single case workflow for case_id: {case_id}", level='INFO')
    driver = None # Initialize driver variable

    # Set the progress queue for the CaseProcessor if provided
    if progress_queue:
        CaseProcessor.set_progress_queue(progress_queue)
        CaseProcessor.report_progress(f"Initiating processing for case {case_id}...", 0) # Initial message

    try:
        # 1. Initialize Browser and Login
        log_message("🌐 Initializing browser and logging into LexisNexis...", level='INFO')
        driver = Login.get_logged_in_browser()
        if not driver:
            raise Exception("Failed to initialize browser or login to LexisNexis.")
        log_message("✅ Browser initialized and logged in.", level='INFO')

        # 2. Load DataFrames
        log_message("💾 Loading cases and plaintiffs data...", level='INFO')
        cases_df = get_table_from_GZ("tb_case", force_refresh=True) # Refresh needed?
        plaintiff_df = get_table_from_GZ("tb_plaintiff")
        log_message(f"📊 Loaded {len(cases_df)} cases and {len(plaintiff_df)} plaintiffs.", level='INFO')

        # 3. Process the case
        # Prepare options - ensure AI flags are present, defaulting if needed
        case_options = options.copy()
        case_options.setdefault('run_plaintiff_overview', False)
        case_options.setdefault('run_summary_translation', False)
        case_options.setdefault('run_step_translation', False)
        case_options.setdefault('refresh_cache_after_ai', True) # Default to True

        # Step 1: Call arrive_on_case_page
        arrival_success, arrival_df_slice, arrival_data = CaseProcessor.arrive_on_case_page(
            driver=driver,
            case_identifier={'case_id': case_id},
            cases_df=cases_df,
            run_id=None # No specific run_id for single case processing in this context
        )

        success = False # Default
        updated_case_data = arrival_df_slice if arrival_df_slice is not None else pd.DataFrame()

        if arrival_success and arrival_data:
            cases_df = arrival_data["cases_df"] # Update with potentially modified df

            # Step 2: Call process_case
            success, updated_case_data_from_proc = CaseProcessor.process_case(
                driver=driver,
                options=case_options,
                plaintiff_df=plaintiff_df,
                run_id=None,
                **arrival_data
            )
            if not updated_case_data_from_proc.empty:
                updated_case_data = updated_case_data_from_proc
            elif success: # Success true but empty df from process_case
                 log_message(f"⚠️ process_case for {case_id} reported success but returned empty data.", level='WARNING')
                 # Keep updated_case_data as arrival_df_slice if process_case returns empty
        else:
            log_message(f"❌ Arrival on case page failed for {case_id}. Full processing aborted.", level='ERROR')
            success = False
            # Clean up Langfuse trace
            if arrival_data:
                if arrival_data.get("span_identify_case"):
                    try:
                        arrival_data["span_identify_case"].end(output="Failed during case arrival phase in single_case")
                    except Exception as e_span:
                        log_message(f"Error ending span_identify_case on arrival failure (single_case): {e_span}", "ERROR")
                if arrival_data.get("process_case_trace"):
                    try:
                        arrival_data["process_case_trace"].update(output="OverallSuccess: False, FinalCaseStatus: Failed during case arrival in single_case")
                    except Exception as e_trace:
                        log_message(f"Error updating process_case_trace on arrival failure (single_case): {e_trace}", "ERROR")


        if success:
            log_message(f"✅ Successfully processed single case_id: {case_id}", level='INFO')
            CaseProcessor.report_progress(f"Case {case_id} processing complete.", 100)
            log_message(f"💾 Saving updated data for case {case_id}...", level='INFO')
            try:
                if not updated_case_data.empty:
                    columns_to_save = ['id', 'file_status', 'last_run_id', 'update_time', 'images', 'images_status']
                    columns_exist = [col for col in columns_to_save if col in updated_case_data.columns]
                    insert_and_update_df_to_GZ_batch(updated_case_data[columns_exist], "tb_case", "id")
                    log_message("✅ Successfully saved updated case data.", level='INFO')
                else:
                    log_message(f"⚠️ Success reported for case {case_id}, but no data returned/found to save.", level='WARNING')
                    CaseProcessor.report_progress(f"Warning: No updated data to save for case {case_id}.", 100)
            except Exception as save_err:
                log_message(f"❌ Failed to save updated case data for case {case_id}: {save_err}", level='ERROR', exc_info=True)
                CaseProcessor.report_progress(f"Warning: Failed to save updates for case {case_id}.", 100)
        else:
            log_message(f"❌ Failed to process single case_id: {case_id}. Final success: {success}", level='ERROR')
            CaseProcessor.report_progress(f"Error processing case {case_id}.", 100)

    except Exception as e:
        log_message(f"💥 Critical error during single case workflow (case_id: {case_id}): {e}", level='CRITICAL', exc_info=True)
        CaseProcessor.report_progress(f"Critical error processing case {case_id}: {e}", 100)
        # Handle error appropriately

    finally:
        # Ensure browser is closed
        if driver:
            log_message("🌐 Closing browser...", level='INFO')
            try:
                driver.quit()
                log_message("✅ Browser closed.", level='INFO')
            except Exception as quit_err:
                log_message(f"⚠️ Error closing browser: {quit_err}", level='WARNING')
        # Clear the progress queue in CaseProcessor to avoid interference
        CaseProcessor.set_progress_queue(None)


# Example Usage (Illustrative)
if __name__ == '__main__':
    print("WorkflowManager module loaded. Call functions with appropriate arguments.")
    # Example:
    test_run_id = int(datetime.now().timestamp())
    test_options = {
        'file_type_strategy': 'IP_ONLY',
        'force_refresh': False,
        'update_steps': True,
        'process_pictures': True,
        'upload_files': False, # Assume local testing doesn't upload
        # 'run_ai_tasks': False, # Deprecated
        'run_plaintiff_overview': True,
        'run_summary_translation': True,
        'run_step_translation': False,
        'refresh_cache_after_ai': True, # Explicitly set or rely on default
        'max_retries': 2,
        'delay_between_retries': 1
    }

    # --- Test Daily Fetch ---
    # print(f"\n--- Testing Daily Fetch (Run ID: {test_run_id}) ---")
    # today = date.today()
    # process_daily_fetch(test_run_id, today, 1, False, test_options)

    # --- Test Specific Cases ---
    # test_run_id += 1
    print(f"\n--- Testing Specific Cases (Run ID: {test_run_id}) ---")
    specific_cases = [12345, {'docket': '1:24-cv-00555', 'court': 'nyed', 'date': '2024-03-15'}] # Example identifiers
    process_specific_cases(test_run_id, specific_cases, test_options)

    # --- Test Single Case ---
    # print(f"\n--- Testing Single Case ---")
    # single_case_id = 67890
    # # Example with a progress queue
    # q = queue.Queue()
    # import threading
    # def progress_listener(q):
    #     while True:
    #         try:
    #             item = q.get(timeout=10) # Timeout after 10s of inactivity
    #             print(f"PROGRESS: {item}")
    #             if item.get('percentage') == 100:
    #                 break
    #             q.task_done()
    #         except queue.Empty:
    #             print("Progress queue empty, listener exiting.")
    #             break
    # listener_thread = threading.Thread(target=progress_listener, args=(q,), daemon=True)
    # listener_thread.start()
    # process_single_case(single_case_id, test_options, progress_queue=q)
    # listener_thread.join() # Wait for listener to finish
