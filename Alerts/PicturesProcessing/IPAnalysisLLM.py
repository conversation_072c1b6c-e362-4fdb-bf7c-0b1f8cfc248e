import os
import json
from typing import List, Dict, Tuple, Optional, Any
import pandas as pd # Only needed if associated_steps is passed as Data<PERSON>ram<PERSON>
from langfuse import observe
import langfuse

from Alerts.IPTrackingManager import IPTrackingManager
from AI.GC_VertexAI import vertex_genai_multi_async
from AI.LLM_shared import get_json
from logdata import log_message
import Common.Constants as Constants
from IP import Patent
from IP.Copyrights import Copyright_USCO
from IP.Trademarks import USPTO_TSDR_API
import traceback



@observe(capture_input=False, capture_output=False)
async def analyze_pdf_for_ip(
    pdf_paths: str,
    attachment_details: List[dict]=None, # Info on direct attachments for the current step
    associated_steps: List[dict]=None,   # Info on other relevant steps (e.g., same day)
) -> Optional[Dict[str, Dict[str, Any]]]: # Return parsed LLM data or None
    """
    Analyzes a main step PDF and associated context (attachments, other steps presented
    as a unified list of exhibits) using an LLM to identify the scope of IP involved
    and the location index(es) of IP representations.

    Args:
        pdf_path: Absolute path to the main step PDF file being analyzed.
        attachment_details: List of dicts with direct attachment info for the current step.
                            Expected keys: 'index' (original 1-based index), 'name'.
        associated_steps: List of dicts representing other relevant steps.
                          Expected keys: 'step_nb', 'description'.

    Returns:
        A dictionary containing the parsed and mapped LLM analysis results for trademark,
        copyright, and patent, or None if parsing or validation fails. The caller is
        responsible for updating the IPTrackingManager with this data.
        Example structure:
        {
          "trademark": {"owned_by_plaintiff": bool, "registration_numbers": [...], "target_attachment_indices": [int], "target_step_nbs": [str], ...},
          "copyright": {"owned_by_plaintiff": bool, "registration_numbers": [...], "target_attachment_indices": [int], "target_step_nbs": [str], ...},
          "patent":    {"owned_by_plaintiff": bool, "registration_numbers": [...], "target_attachment_indices": [int], "target_step_nbs": [str], ...}
        }
    """
    
    if isinstance(pdf_paths, str):
        pdf_paths = [pdf_paths]

    # --- Internal: Create Unified Exhibit List & Mapping ---
    exhibit_context_list = []
    current_unified_index = 1

    # Add direct attachments
    if attachment_details:
        for att in attachment_details:
            try:
                exhibit_context_list.append({
                    "exhibit_index": current_unified_index,
                    "name": str(att['name']),
                    # Store original info for mapping later
                    "original_type": "attachment",
                    "original_attachment_index": int(att['index'])
                })
                current_unified_index += 1
            except (ValueError, TypeError, KeyError):
                log_message(f"           ⚠️ Skipping invalid attachment detail when building context: {att}", level='warning')

    # Add associated steps
    if associated_steps:
        for step in associated_steps:
            try:
                desc = str(step.get('description', ''))
                # Use only the description snippet as the name, max 80 chars
                exhibit_name = (desc[:80] + '...') if len(desc) > 80 else desc
                if not exhibit_name: # Skip if description is empty
                    continue

                exhibit_context_list.append({
                    "exhibit_index": current_unified_index,
                    "name": exhibit_name,
                    # Store original info for mapping later
                    "original_type": "step",
                    "original_step_nb": str(step['step_nb']) # Ensure step_nb is stored
                })
                current_unified_index += 1
            except (ValueError, TypeError, KeyError):
                log_message(f"           ⚠️ Skipping invalid associated step detail when building context: {step}", level='warning')

    # log_message("           Starting IP analysis for PDF.", {"path": pdf_path, "num_exhibits_context": len(exhibit_context_list)})


    # --- Construct the prompt for the LLM ---
    prompt_document_context = ""
    prompt_document_search = ""
    if exhibit_context_list:
        prompt_document_context += "\nThe following documents/exhibits are potentially relevant:\n"
        prompt_document_context += f"- Index: 0, Name: The primary document attached \n"
        for exhibit in exhibit_context_list:
             # Only include index and name for the LLM
            prompt_document_context += f"- Index: {exhibit['exhibit_index']}, Name: {exhibit['name']}\n"
        prompt_document_context += "\n"
        
        prompt_document_search = f"""4.  **Location(s) of Primary IP Representation:** Identify where the primary visual representation (e.g., images, drawings) or the most comprehensive list of the asserted IP for this type is located, according to the text in the primary document.
            *   If the text explicitly references one or more of the exhibits listed above (using terms like "Exhibit A", "Attachment 2", "Declaration of John Doe", etc., which correspond to the 'Name' in the provided context), return a **list of all corresponding integer `Index` values** from the context list.
            *   If the representation/list is primarily within the main PDF document itself, or if the location is unclear or not referenced, return an **empty list `[]`**."""


    if len(pdf_paths) > 1:
        document = "documents"
    else:
        document = "document"
    
    prompt = f"""
        Analyze the content of the provided PDF {document} (the primary {document}) to identify the intellectual property (IP) owned by the plaintiff in this legal case.
        {prompt_document_context}
        Based *only* on the text within the primary PDF {document}, determine the following for Trademarks, Copyrights, and Patents:

        1.  **Is this type of IP owned by the plaintiff?** (true/false)
        2.  **Extract Registration Numbers:** If any registration numbers are explicitly mentioned for this IP type *in the primary {document}*, list them.
            *   Use standard formats:
                *   Trademark (TM): 6-8 digits (e.g., 1234567, 98765432)
                *   Copyright (CR): VA..., TX..., PA..., SR..., RE... (e.g., VA 1-234-567, TXu 987-654)
                *   Patent (PT): US D..., US ..., US ... Bx, US ... S (e.g., US D123,456, US 11,222,333 B2, US 9,876,543, US D999,999 S) - Extract the full number including prefixes/suffixes.
            *   If no numbers are explicitly mentioned, provide an empty list [].
        3.  **Completeness of Numbers:** Based on the context in the primary document, estimate if the extracted numbers represent the complete list of asserted IP ("WholeSet"), only a subset mentioned as examples ("Subset"), or if it's unclear ("Unknown").
        {prompt_document_search}
        **Output Format:**
        Return your analysis **ONLY** as a single JSON object matching this exact structure. Do not include any text before or after the JSON object.

        ```json
        {{
          "trademark": {{
            "owned_by_plaintiff": boolean,
            "registration_numbers": [list of strings],
            "registration_numbers_status": "WholeSet" | "Subset" | "Unknown",
            {'"target_location_indices": [list of integers] // List of Index values from context, or []' if len(exhibit_context_list) > 0 else ''}
          }},
          "copyright": {{
            "owned_by_plaintiff": boolean,
            "registration_numbers": [list of strings],
            "registration_numbers_status": "WholeSet" | "Subset" | "Unknown",
            {'"target_location_indices": [list of integers] // List of Index values from context, or []' if len(exhibit_context_list) > 0 else ''}
          }},
          "patent": {{
            "owned_by_plaintiff": boolean,
            "registration_numbers": [list of strings],
            "registration_numbers_status": "WholeSet" | "Subset" | "Unknown",
            {'"target_location_indices": [list of integers] // List of Index values from context, or []' if len(exhibit_context_list) > 0 else ''}
          }}
        }}
        ```

        **Important:**
        *   Ensure keys and value types match exactly.
        {'*   `target_location_indices` MUST be a list of integers (potentially empty).' if len(exhibit_context_list) > 0 else ''}
        """

    try:
        log_message(f"           Sending {len(pdf_paths)} PDF to LLM for IP analysis. Prompt_length: {len(prompt)}")
        data_list = [("text", prompt)]
        for pdf_path in pdf_paths:
            data_list.append(("pdf_path", pdf_path))

        llm_response_text = await vertex_genai_multi_async(data_list=data_list, model_name=Constants.SMART_MODEL_FREE_LIMITED, useVertexAI=Constants.SMART_MODEL_FREE_LIMITED_VERTEX) # Adjust model if needed

        if not llm_response_text:
            log_message("           ❌ Error: Received empty response from LLM.", level='ERROR')
            return None
        
        # Parse the JSON response
        parsed_json = get_json(llm_response_text)

        if parsed_json is None:
            log_message(f"           ❌ Error: Failed to parse JSON from LLM response. Response: {llm_response_text[:500]}", level='ERROR')
            return None
        
        # --- Validation of the parsed JSON structure ---
        log_message(f"           Answer received from LLM, and parsing successful. Now starting validation.", level='DEBUG')
        is_valid_structure = True
        expected_ip_keys = ["trademark", "copyright", "patent"]
        expected_sub_keys = ["owned_by_plaintiff", "registration_numbers", "registration_numbers_status"] # Changed key

        if not isinstance(parsed_json, dict) or any(key not in parsed_json for key in expected_ip_keys):
            is_valid_structure = False
        else:
            for ip_type in expected_ip_keys:
                if not isinstance(parsed_json[ip_type], dict) or any(sub_key not in parsed_json[ip_type] for sub_key in expected_sub_keys):
                    is_valid_structure = False; break
                # Type validation for specific fields
                if not isinstance(parsed_json[ip_type].get("owned_by_plaintiff"), bool): is_valid_structure = False; break
                if not isinstance(parsed_json[ip_type].get("registration_numbers"), list): 
                    is_valid_structure = False; break
                else: # Ensure correct formar for all registration numbers
                    if ip_type == "trademark":
                        parsed_json[ip_type]["registration_numbers"] = [USPTO_TSDR_API.format_reg_number(rn) for rn in parsed_json[ip_type]["registration_numbers"] if USPTO_TSDR_API.format_reg_number(rn)]
                        
                    if ip_type == "copyright":
                        parsed_json[ip_type]["registration_numbers"] = [await Copyright_USCO.extract_formated_copyright_registration_number(rn) for rn in parsed_json[ip_type]["registration_numbers"]]
                        
                    if ip_type == "patent":
                        parsed_json[ip_type]["registration_numbers"] = [Patent.clean_reg_no(rn) for rn in parsed_json[ip_type]["registration_numbers"]]

                    parsed_json[ip_type]["registration_numbers"] = [rn for rn in parsed_json[ip_type]["registration_numbers"] if rn] # Remove potential None/empty results
                    
                if parsed_json[ip_type].get("registration_numbers_status") not in ["WholeSet", "Subset", "Unknown"]: is_valid_structure = False; break
                target_indices_val = parsed_json[ip_type].get("target_location_indices", [])
                # Check if it's a list and all elements are integers
                if not isinstance(target_indices_val, list) or not all(isinstance(i, int) for i in target_indices_val):
                    is_valid_structure = False; break


        if not is_valid_structure:
            log_message(f"           ❌ Error: Parsed JSON does not match the expected structure or type constraints. JSON: {parsed_json}", level='ERROR')
            return None
        # --- End Validation ---
        
        log_text = "        💡 Valid LLM response for IP analysis: "
        for ip_type, data in parsed_json.items():
            if data.get("owned_by_plaintiff", False):
                log_text += f"\n                 - {ip_type.capitalize()}: {data.get('registration_numbers_status', 'Unknown')} = {data.get('registration_numbers', [])}"
            else:
                log_text += f"\n                 - {ip_type.capitalize()}: not relevant"
        log_message(log_text, level='INFO')

        # --- Map unified indices back to original attachment/step references ---
        ip_types = ["trademark", "copyright", "patent"]
        for ip_type in ip_types:
            if ip_type in parsed_json:
                # Ensure the ip_type dict exists and has the key before proceeding
                if isinstance(parsed_json[ip_type], dict) and "target_location_indices" in parsed_json[ip_type]:
                    unified_indices = parsed_json[ip_type].get("target_location_indices", [])
                    mapped_attachment_indices = set()
                    mapped_step_nbs = set()

                    # Validate unified_indices is a list before iterating
                    if isinstance(unified_indices, list):
                        for unified_index in unified_indices:
                            # Find the corresponding exhibit in the context list
                            found_exhibit = None
                            for exhibit in exhibit_context_list:
                                if exhibit.get('exhibit_index') == unified_index:
                                    found_exhibit = exhibit
                                    break

                            if found_exhibit:
                                original_type = found_exhibit.get('original_type')
                                if original_type == 'attachment':
                                    original_att_index = found_exhibit.get('original_attachment_index')
                                    if original_att_index is not None:
                                        mapped_attachment_indices.add(original_att_index)
                                elif original_type == 'step':
                                    original_step_nb = found_exhibit.get('original_step_nb')
                                    if original_step_nb is not None:
                                        mapped_step_nbs.add(original_step_nb)
                            elif unified_index == 0:
                                parsed_json[ip_type]['target_main_doc'] = True # Mark the main document as a target                               
                            else:
                                log_message(f"           ⚠️ Could not find exhibit context for unified_index {unified_index} during mapping.", level='warning')
                    else:
                         log_message(f"           ⚠️ 'target_location_indices' for {ip_type} is not a list: {unified_indices}. Skipping mapping for this type.", level='warning')


                    # Remove the old key and add the new mapped keys
                    del parsed_json[ip_type]["target_location_indices"]
                    parsed_json[ip_type]['target_attachment_indices'] = sorted(list(mapped_attachment_indices)) # Sort for consistency
                    parsed_json[ip_type]['target_step_nbs'] = sorted(list(mapped_step_nbs)) # Sort for consistency
                elif "target_location_indices" not in parsed_json.get(ip_type, {}):
                    # If the key was missing initially (e.g., LLM didn't return it), ensure the new keys exist with empty lists
                    if isinstance(parsed_json.get(ip_type), dict):
                        parsed_json[ip_type]['target_attachment_indices'] = []
                        parsed_json[ip_type]['target_step_nbs'] = []

        # --- End Mapping ---

        langfuse.get_client().update_current_span(
            input={"NumPDFPaths": (len(pdf_paths) if isinstance(pdf_paths, list) else 1), "NumAttachmentDetails": (len(attachment_details) if attachment_details else 0), "NumAssociatedSteps": (len(associated_steps) if associated_steps else 0)},
            output={"LLMResponseParsed": ('Yes' if 'parsed_json' in locals() and parsed_json else 'No'), "ValidStructure": (is_valid_structure if 'is_valid_structure' in locals() else 'N/A'), "TM_Owned": (parsed_json['trademark']['owned_by_plaintiff'] if 'parsed_json' in locals() and parsed_json and 'trademark' in parsed_json else 'N/A'), "CR_Owned": (parsed_json['copyright']['owned_by_plaintiff'] if 'parsed_json' in locals() and parsed_json and 'copyright' in parsed_json else 'N/A'), "PT_Owned": (parsed_json['patent']['owned_by_plaintiff'] if 'parsed_json' in locals() and parsed_json and 'patent' in parsed_json else 'N/A'), "TM_RegNos": (len(parsed_json['trademark']['registration_numbers']) if 'parsed_json' in locals() and parsed_json and 'trademark' in parsed_json else 0), "CR_RegNos": (len(parsed_json['copyright']['registration_numbers']) if 'parsed_json' in locals() and parsed_json and 'copyright' in parsed_json else 0), "PT_RegNos": (len(parsed_json['patent']['registration_numbers']) if 'parsed_json' in locals() and parsed_json and 'patent' in parsed_json else 0)}
        )
        return parsed_json # Return the mapped LLM data


    except Exception as e:
        log_message(f"Exception during LLM call or processing for IP analysis: {e}, {traceback.format_exc()}", level='exception')
        # Optionally log traceback: import traceback; log_message(traceback.format_exc(), level='debug')
        return None # Return None on error

# Example Usage (Optional - for testing)
# if __name__ == '__main__':
#     # Create dummy PDF files for testing if needed
#     # dummy_pdf_path1 = "path/to/your/test_complaint1.pdf"
#     # dummy_pdf_path2 = "path/to/your/test_complaint2.pdf"
#     # Ensure these files exist or adjust paths
#     # test_paths = [dummy_pdf_path1, dummy_pdf_path2]
#     test_paths = ["d:/Documents/Programing/TRO/USSideRefactoring/Alerts/PicturesProcessing/test_complaint.pdf"] # Replace with actual path
#     if not os.path.exists(test_paths[0]):
#          print(f"Test file not found: {test_paths[0]}")
#     else:
#         # Example attachment details
#         # attachments = [{'index': 1, 'name': 'Exhibit A.pdf'}, {'index': 2, 'name': 'Exhibit B.pdf'}]
#         # analysis_result = analyze_complaint_pdf_for_ip(test_paths, attachment_details=attachments)
#         analysis_result = analyze_complaint_pdf_for_ip(test_paths) # Without attachments
#         print("Analysis Result:")
#         import json
#         print(json.dumps(analysis_result, indent=2))