#!/usr/bin/env python3
"""
Script to identify and collect trademark images with potentially problematic
registration or serial numbers (containing '000').

This script:
1. Fetches all cases from the database.
2. Iterates through each case's trademarks.
3. Checks if 'reg_no' or 'ser_no' fields contain '000'.
4. If a problematic entry is found, it downloads the corresponding trademark images
   (both normal and full versions) from their public COS URL.
5. Saves the downloaded images into a local 'problems/{plaintiff_id}/' directory for manual review.
"""

import os
import sys
import json
import requests
import pandas as pd

# Add project root to path
sys.path.append(os.getcwd())

from DatabaseManagement.ImportExport import get_table_from_GZ

# Define the base directory for storing problematic files, relative to this script's location.
PROBLEMS_BASE_DIR = os.path.join(os.path.dirname(__file__), "problems")

class ProblematicTrademarkReviewer:
    def __init__(self):
        """Initializes the reviewer."""
        self.cases_df = None
        print(f"Problematic files will be saved in: {PROBLEMS_BASE_DIR}")

    def download_file_for_review(self, plaintiff_id: int, filename: str):
        """Downloads a file from its public COS URL and saves it to the problems directory."""
        if not filename:
            print("Skipping download for empty filename.")
            return

        # Define local destination path
        dest_dir = os.path.join(PROBLEMS_BASE_DIR, str(plaintiff_id))
        os.makedirs(dest_dir, exist_ok=True)
        dest_path = os.path.join(dest_dir, filename)

        if os.path.exists(dest_path):
            print(f"File already exists locally, skipping download: {dest_path}")
            return

        # Construct public URL
        base_url = "http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com"
        
        # Try high-res first
        image_url_high = f"{base_url}/plaintiff_images/{plaintiff_id}/high/{filename}"
        
        response = None
        try:
            print(f"Attempting to download from {image_url_high}")
            response = requests.get(image_url_high, stream=True, timeout=30)
            response.raise_for_status()
        except requests.exceptions.RequestException as e:
            print(f"Failed to download high-res: {e}. Trying low-res.")
            # Try low-res
            image_url_low = f"{base_url}/plaintiff_images/{plaintiff_id}/low/{filename}"
            try:
                print(f"Attempting to download from {image_url_low}")
                response = requests.get(image_url_low, stream=True, timeout=30)
                response.raise_for_status()
            except requests.exceptions.RequestException as e_low:
                print(f"Failed to download low-res as well: {e_low}. Skipping file {filename}.")
                return

        # Save the file
        if response:
            try:
                with open(dest_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)
                print(f"Successfully downloaded image to {dest_path}")
            except IOError as e:
                print(f"Error saving file {dest_path}: {e}")

    def review_cases(self):
        """Iterates through all cases and checks for problematic trademarks."""
        print("Loading cases from database...")
        self.cases_df = get_table_from_GZ("tb_case")
        print(f"Loaded {len(self.cases_df)} cases.")

        for index, row in self.cases_df.iterrows():
            case_id = row['id']
            plaintiff_id = int(float(row['plaintiff_id']))
            images_data = row.get('images', {})
            if not isinstance(images_data, dict):
                # images might be a stringified JSON
                try:
                    images_data = json.loads(images_data) if images_data else {}
                except (json.JSONDecodeError, TypeError):
                    print(f"Could not parse images data for case {case_id}. Skipping.")
                    continue

            trademarks_data = images_data.get('trademarks', {})

            if not trademarks_data:
                continue

            for tm_filename, tm_data in trademarks_data.items():
                problem_found = False
                
                # Check reg_no
                reg_nos = tm_data.get('reg_no', [])
                if isinstance(reg_nos, list):
                    for reg_no in reg_nos:
                        if '000' in str(reg_no):
                            print(f"Found problematic reg_no '{reg_no}' in case {case_id} for plaintiff {plaintiff_id}")
                            problem_found = True
                            break
                
                # Check ser_no if no problem found yet
                if not problem_found:
                    ser_nos = tm_data.get('ser_no', [])
                    if isinstance(ser_nos, list):
                        for ser_no in ser_nos:
                            if '000' in str(ser_no):
                                print(f"Found problematic ser_no '{ser_no}' in case {case_id} for plaintiff {plaintiff_id}")
                                problem_found = True
                                break
                
                if problem_found:
                    print(f"Problem found in trademark entry for file: {tm_filename}. Downloading files for review.")
                    # Download the main file (key of the trademark entry)
                    self.download_file_for_review(plaintiff_id, tm_filename)

                    # Download the full_filename files
                    full_filenames = tm_data.get('full_filename', [])
                    if isinstance(full_filenames, list):
                        for full_filename in full_filenames:
                            self.download_file_for_review(plaintiff_id, full_filename)

def main():
    """Main function to run the review process."""
    print("Starting Problematic Trademark Review")
    print("=" * 50)

    reviewer = ProblematicTrademarkReviewer()
    reviewer.review_cases()

    print("=" * 50)
    print("Review process completed.")

if __name__ == "__main__":
    main()