#!/usr/bin/env python3
"""
Migration script to update patent records in the database based on registration numbers found in case data.
 
This script:
1. Processes all cases with patent images in the dataframe.
2. Finds patent records using `reg_no` in the patent database.
3. Updates database records (`tro=TRUE`, `plaintiff_id`).
4. Updates dataframe with new filenames and `reg_no` information.
"""

import os
import sys
import re
import asyncio
from typing import Dict, Optional, Tuple
import traceback

USER_ANSWERS_CACHE_FILE = os.path.join(os.path.dirname(__file__), "user_answers_cache.json")

PROCESSED_PATENTS = set()
PROBLEMATIC_PATENTS = set()


sys.path.append(os.getcwd())

import pandas as pd
from DatabaseManagement.ImportExport import get_table_from_GZ
from IP.Patents_Bulk.patent_db_grant import get_db_connection


class PatentFilenameMigrator:
    def __init__(self):
        self.db_conn = None
        self.processed_count = 0
        self.skipped_count = 0
        self.error_count = 0
        self.tro_updated_reg_nos = set() # To track patents for which tro has been updated

        # Summary counters
        self.summary_processed_patents = 0
        self.summary_patents_found_in_patents = 0
        self.summary_patents_not_found_in_patents = 0
        self.summary_patents_found_in_patents_all = 0
        self.summary_patents_with_date_lt_2011 = 0
        self.summary_other_not_found_patents = 0
        self.summary_top_10_other_not_found_patents = []

    async def __aenter__(self):
        self.db_conn = get_db_connection()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.db_conn:
            self.db_conn.close()

    def __enter__(self):
        self.db_conn = get_db_connection()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.db_conn:
            self.db_conn.close()

    def reset_patent_tro_plaintiff_ids(self) -> bool:
        """Set tro and plaintiff_id columns in patents table to NULL."""
        try:
            cursor = self.db_conn.cursor()
            cursor.execute("""
                UPDATE patents
                SET tro = NULL, plaintiff_id = NULL;
            """)
            self.db_conn.commit()
            print("✅ Successfully set 'tro' and 'plaintiff_id' to NULL in the 'patents' table.")
            return True
        except Exception as e:
            print(f"❌ Error resetting 'tro' and 'plaintiff_id' in 'patents' table: {e}")
            self.db_conn.rollback()
            return False
    
    def get_patent_from_database(self, reg_no: str, table: str = "patents") -> Optional[Tuple[str, Optional[str]]]:
        """Query database to get reg_no and date_filed from patents table"""
        if not reg_no:
            return None
        try:
            cursor = self.db_conn.cursor()
            cursor.execute(f"SELECT reg_no, date_published FROM {table} WHERE reg_no = %s", (reg_no,))
            result = cursor.fetchone()
            if result:
                return result[0], result[1] # Return reg_no and date_filed
            return None
        except Exception as e:
            print(f"Database error getting reg_no for reg_no {reg_no}: {e}")
            return None
    
    def update_patent_record(self, reg_no: str, plaintiff_id: int) -> bool:
        """Update database record with tro='TRUE' and plaintiff_id for patents"""
        try:
            cursor = self.db_conn.cursor()

            cursor.execute("""
                UPDATE patents
                SET tro = 'TRUE', plaintiff_id = %s
                WHERE reg_no = %s;
            """, (plaintiff_id, reg_no))

            self.db_conn.commit()
            return True
        except Exception as e:
            print(f"Database update error for reg_no {reg_no}: {e}")
            return False
    
    
    async def process_patent_entry(self, patent_data: Dict, plaintiff_id: int, case_id) -> bool:
        """Process a single patent entry and return updated data"""
        original_doc_no = patent_data.get('patent_number', "")
        # Clean and extract the registration number from the patent number
        original_doc_no = re.sub(r',', '', original_doc_no).strip()
        reg_no_match = re.search(r'(D?\d+)', original_doc_no)
        
        if not reg_no_match:
            print(f"⚠️ No valid reg_no found for patent data: {patent_data}. Skipping.")
            return False
        
        
        original_reg_no = reg_no_match.group(1)

        if (case_id, original_reg_no, plaintiff_id) in PROCESSED_PATENTS:
            return True

        # Check if patent exists in patents database in PostgreSQL
        found_patent_data = self.get_patent_from_database(original_reg_no, "patents")
        found_reg_no = found_patent_data[0] if found_patent_data else None
        found_date_filed = found_patent_data[1] if found_patent_data else None

        PROCESSED_PATENTS.add((case_id, original_reg_no, plaintiff_id))
        self.summary_processed_patents += 1

        if not found_reg_no:
            self.summary_patents_not_found_in_patents += 1
            print(f"⚠️ Patent with reg_no {original_reg_no} not found in 'patents' table.")
            found_reg_no_all_data = self.get_patent_from_database(original_reg_no, "patents_all")
            if found_reg_no_all_data:
                self.summary_patents_found_in_patents_all += 1
            elif found_date_filed and found_date_filed.year < 2011:
                self.summary_patents_found_with_date_lt_2011 += 1
            else:
                self.summary_other_not_found_patents += 1
                if len(self.summary_top_10_other_not_found_patents) < 10:
                    self.summary_top_10_other_not_found_patents.append(original_reg_no)

            PROBLEMATIC_PATENTS.add((case_id, original_reg_no, plaintiff_id, 'no_reg'))
            return False
        else:
            self.summary_patents_found_in_patents += 1

        if found_reg_no in self.tro_updated_reg_nos:
            return True

        # Update database record for this reg_no
        if not self.update_patent_record(found_reg_no, plaintiff_id):
            print(f"⚠️ Failed to update database for reg_no {found_reg_no}. Keeping original entry for this specific reg_no.")
            PROBLEMATIC_PATENTS.add((case_id, original_reg_no, plaintiff_id, 'other_error'))
            return False
        
        self.tro_updated_reg_nos.add(found_reg_no)
        return True

    async def migrate_case_patents(self, case_row: pd.Series) -> bool:
        """Migrate all patents for a single case"""
        case_id = case_row['id']
        plaintiff_id = int(float(case_row['plaintiff_id']))
        images_data = case_row.get('images', {})
        patents_data = images_data.get('patents', {})

        if not patents_data:
            return True  # No patents to migrate


        for old_filename, patent_data in patents_data.items():
            # Process this patent entry
            success = await self.process_patent_entry(patent_data, plaintiff_id, case_id)
 
            if not success:

                self.error_count += 1
                continue
 
            self.processed_count += 1
        return True
 
    async def migrate_all_cases(self, df: pd.DataFrame) -> pd.DataFrame:
        """Migrate all cases in the dataframe"""
        print(f"Starting migration of {len(df)} cases")
 
        for i, (index, case_row) in enumerate(df.iterrows()):
            case_id = case_row.get('id', 'unknown')
            try:
                await self.migrate_case_patents(case_row)
 
                if (i + 1) % 10 == 0:
                    print(f"⛏\033[91m Processed {i + 1}/{len(df)} cases\033[0m")
            except Exception as e:
                print(f"Error processing case {case_id}: {e}, traceback: {traceback.format_exc()}")
                self.error_count += 1
 
        print(f"✅ Migration completed. Processed: {self.processed_count}, Skipped: {self.skipped_count}, Errors: {self.error_count}")
        return df
 
 
def main():
    """Main migration function"""
    print("Starting Patents Filename Migration")
    print("=" * 50)
 
    # Load cases with patent images
    print("Loading cases from database...")
    df = get_table_from_GZ("tb_case")
 
    # Filter cases that have patent images
    cases_with_patents = []

    df = df.sort_values(by='date_filed', ascending=True)
    for index, row in df.iterrows():
        images_data = row.get('images', {})
        if images_data and 'patents' in images_data and images_data['patents']:
            cases_with_patents.append(index)
 
    if not cases_with_patents:
        print("No cases with patent images found.")
        return
 
    df_to_migrate = df.loc[cases_with_patents].copy()
    print(f"Found {len(df_to_migrate)} cases with patent images")

 
    # Perform migration
    async def run_migration():
        async with PatentFilenameMigrator() as migrator:
            # Reset tro and plaintiff_id before migration
            if not migrator.reset_patent_tro_plaintiff_ids():
                print("Failed to reset 'tro' and 'plaintiff_id' columns. Aborting migration.")
                return None # Return None if reset fails
            
            await migrator.migrate_all_cases(df_to_migrate)
            return migrator # Return the migrator instance

    # Run the async migration
    migrator_instance = asyncio.run(run_migration())

    if migrator_instance:
        print("\n" + "=" * 50)
        print("Migration Summary:")
        print(f"Total patents processed: {migrator_instance.summary_processed_patents}")
        print(f"Patents found in patents: {migrator_instance.summary_patents_found_in_patents}")
        print(f"Patents not found patents: {migrator_instance.summary_patents_not_found_in_patents}")
        print(f"-- Patents found in patents_all: {migrator_instance.summary_patents_found_in_patents_all}")
        print(f"-- Patents with date < 2011: {migrator_instance.summary_patents_with_date_lt_2011}")
        print(f"-- Other not found patents: {migrator_instance.summary_other_not_found_patents}")
        if migrator_instance.summary_top_10_other_not_found_patents:
            print("---- Top 10 examples of other not found patents:")
            for reg_no in migrator_instance.summary_top_10_other_not_found_patents:
                print(f"---- reg no: {reg_no}")
        print("=" * 50)

if __name__ == "__main__":
    main()
