import os, sys, cv2, shutil, re
sys.path.append(os.getcwd())
from logdata import log_message
from Alerts.PicturesProcessing.ProcessPicturesShared import crop_white_space, keywords, assess_similarity_to_template_single_folder
from Alerts.PicturesProcessing.ExtractPictures import  extract_images_from_a_pdf_page
from AI.GC_VertexAI import vertex_genai_multi_async
from AI.LLM_shared import get_json, get_json_list
from langfuse import observe
import langfuse
from IP.Copyrights.Copyright_USCO import extract_formated_copyright_registration_number
from Alerts.IPTrackingManager import IPTrackingManager # Added import
import Common.Constants as Constants

# VA == Visual Art
# TX == Computer File
# PA == Performance Art (Motion Picture)


@observe(capture_input=False, capture_output=False)
async def process_copyright_exhibit(df, index, ref_hash, size, case_images_directory, case_directory_1, pdf_document, pdf_file, pages_text_json, certainty, ip_manager: IPTrackingManager, location_id: str):
    """
    Processes a potential copyright exhibit PDF to find copyright certificates and associated images.
    Updates IPTrackingManager based on findings.

    Returns:
        tuple: (bool, bool, list[str])
            - bool: True if copyright exhibit text (registration numbers) was found.
            - bool: True if distinct copyright artwork images were successfully linked to any of those registration numbers.
            - list[str]: A list of unique registration numbers textually identified in the exhibit.
    """
    log_message(f"        - Extracting images from the pdf for exhibit processing at {location_id}.")
    pdf_file_no_ext = os.path.splitext(pdf_file)[0]
    similar_images_in_pdf = [] # List of paths to certificate images
    identified_reg_nos = []
    identified_reg_nos_with_artwork = []
    
    # 1. Image template approach
    folder_path = os.path.join(case_directory_1, pdf_file_no_ext)
    log_message(f"        - Assessing similarity of images to copyright template")
    if os.path.isdir(folder_path):
        similar_images_in_pdf, differences, total_similar_images_folder = assess_similarity_to_template_single_folder(folder_path, ref_hash, size, 20)

    # 2. Keywords approach
    page_numbers_with_keywords = []
    if similar_images_in_pdf or certainty=="high" or ip_manager.is_goal_relevant('copyright'): # if it is the right document, we be easy on keywords
        for page_number in range(pdf_document.page_count):
            page_text = pages_text_json.get(page_number+1, "")
            if any(sum(keyword.lower() in page_text.lower() for keyword in keyword_set) >= 3 for keyword_set in keywords["Copyright"]):
                page_numbers_with_keywords.append(page_number + 1)
    else:
        for page_number_outer in range(pdf_document.page_count): # This will also pick up the catalog format (often used for photographs)
            page_text = pages_text_json.get(page_number_outer+1, "")
            if any(all(keyword.lower() in page_text.lower() for keyword in keyword_set) for keyword_set in keywords["Copyright"]):
                # we found one, now we can look for any word rather than all words
                for page_number_inner in range(pdf_document.page_count):
                    page_text = pages_text_json.get(page_number_inner+1, "")
                    if any(sum(keyword.lower() in page_text.lower() for keyword in keyword_set) >= 3 for keyword_set in keywords["Copyright"]):
                        page_numbers_with_keywords.append(page_number_inner + 1)
                break
    

    if os.path.isdir(folder_path):
        # 4. Merge both lists the other way arround to see if we really need the assess_similarity_to_template_single_folder
        # 1 time and similar_images_in_pdf was wrong
        for file in similar_images_in_pdf:
            page_number = int(os.path.basename(file).split("page")[1].split("_")[0])
            if page_number not in page_numbers_with_keywords:
                page_numbers_with_keywords.append(page_number)
                print(f"\033[91mPage {page_number} was found by the template but not by the keywords. Full text is: {pages_text_json.get(page_number, '')} \033[0m")
    
    prompt = f'I am looking for registration certificates and associated artwork. You are given a PDF and return me a list of json with the information about each copyright: [{{"registration number": "VA000xxxxxxx", "certificate page": xx, "artwork page": xx}}, {{...}},...]. I have identified the following pages as potentially containing certificates: {page_numbers_with_keywords}. If the certificate is present but not the artwork, return -1 for the artwork page.'
    prompt_list = [("text", prompt), ("pdf_path", os.path.join(case_directory_1, pdf_file))]
    ai_answer = await vertex_genai_multi_async(prompt_list, model_name=Constants.SMART_MODEL_FREE)
    ai_answer_json = get_json_list(ai_answer)
    
    # Ensure structure exists
    if 'exhibit' not in df.at[index, 'images_status']['copyright_status'] or not isinstance(df.at[index, 'images_status']['copyright_status']['exhibit'], dict):
        df.at[index, 'images_status']['copyright_status']['exhibit'] = {'count': 0, 'steps': []}
        
    current_step_name = os.path.basename(os.path.dirname(pdf_file))
    
    for item in ai_answer_json:
        certificate_page = int(item["certificate page"])
        if certificate_page > 0 and certificate_page <= pdf_document.page_count:
            registration_number = await extract_formated_copyright_registration_number(item["registration number"])
            identified_reg_nos.append(registration_number)
            
            source_certificate_path = os.path.join(case_directory_1, pdf_file_no_ext, f"{pdf_file_no_ext}_page{certificate_page}_0.jpg")
            dest_certificate_filename = os.path.splitext(os.path.basename(source_certificate_path))[0] + "_full.webp"
            dest_certificate_path = os.path.join(case_images_directory, dest_certificate_filename)
            img = cv2.imread(source_certificate_path)
            if img.shape[1] > img.shape[0]:  # if width > height
                img = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)
            cv2.imwrite(dest_certificate_path, img, [cv2.IMWRITE_WEBP_QUALITY, 80])
            
            artwork_page = int(item["artwork page"])
            if artwork_page == -1:
                copyright_image_filename_webp = os.path.basename(dest_certificate_path).replace("_full.webp", ".webp")
                # Use the certificate as the image
                shutil.copy(dest_certificate_path, os.path.join(case_images_directory, copyright_image_filename_webp))
                ip_manager.add_copyright_to_dataframe({"registration_number": registration_number, "image_found": 0, "certificate_found": 1}, location_id)
            elif artwork_page > 0 and artwork_page <= pdf_document.page_count:
                copyright_images_folder = os.path.join(folder_path, 'copyright')
                os.makedirs(copyright_images_folder, exist_ok=True)
                artwork_page_image_file = os.path.join(case_directory_1, pdf_file_no_ext, f"{pdf_file_no_ext}_page{artwork_page}_0.jpg")
                extracted_image_paths = extract_images_from_a_pdf_page(pdf_file, pdf_document, artwork_page-1, copyright_images_folder, 5)
                if len(extracted_image_paths) == 0:
                    artwork_image_file = artwork_page_image_file
                else:
                    if len(extracted_image_paths) > 1:
                        log_message(f"        Found {len(extracted_image_paths)} images on page {artwork_page} for {item['registration number']}. Not sure which one to use => asking LLM.", level="WARNING")
                        prompt = 'I am looking for a copyrighted artwork. Which of these images has the artwork? You answer with a json with a single artwork: {"artwork": "image_name"}'
                        prompt_list = [("text", prompt)]
                        for i, image_file in enumerate(extracted_image_paths):
                            prompt_list.append(("text", f"\n\nImage_{i+1}"))
                            prompt_list.append(("image_path", os.path.join(copyright_images_folder, image_file)))
                        ai_answer = await vertex_genai_multi_async(prompt_list, model_name=Constants.SMART_MODEL_FREE)
                        ai_answer_json = get_json(ai_answer)
                        if ai_answer_json and "artwork" in ai_answer_json:
                            artwork_image_tag = ai_answer_json["artwork"]
                            artwork_image_nb = int(re.sub("[^0-9]", "", artwork_image_tag))
                            if (artwork_image_nb-1) >=0 and (artwork_image_nb-1) < len(extracted_image_paths):
                                artwork_image_file = extracted_image_paths[artwork_image_nb - 1]
                            else:
                                log_message(f"        🔥 LLM returned invalid image number {artwork_image_tag} for {registration_number} while the possible values are {list(range(1, len(extracted_image_paths)+1))}.", level="WARNING")
                                continue
                    else:
                        artwork_image_file = extracted_image_paths[0]

                # The image: Remove all the white space around the image
                img = cv2.imread(artwork_image_file)
                cropped_image, (x_offset, y_offset) = crop_white_space(img)
                copyright_image_filename_webp = f"{os.path.splitext(os.path.basename(artwork_image_file))[0]}.webp"
                cv2.imwrite(os.path.join(case_images_directory, copyright_image_filename_webp), cropped_image, [cv2.IMWRITE_WEBP_QUALITY, 80])
                ip_manager.record_finding('copyright', location_id, found_reg_nos=[registration_number])
                ip_manager.add_copyright_to_dataframe({"registration_number": registration_number, "image_found": 1, "image_source": "exhibit"}, location_id)
                identified_reg_nos_with_artwork.append(registration_number)
                df.at[index, 'images_status']['copyright_status']['exhibit']['count'] += 1
                if current_step_name not in df.at[index, 'images_status']['copyright_status']['exhibit'].get('steps', []):
                    df.at[index, 'images_status']['copyright_status']['exhibit'].setdefault('steps', []).append(current_step_name)

            df.at[index, 'images']['copyrights'][copyright_image_filename_webp] = {'full_filename': [os.path.basename(dest_certificate_path)], 'reg_no': [registration_number]}
                    
    
    if identified_reg_nos_with_artwork:
        log_message(f"     ✅ Found Copyright Exhibit in {pdf_file} (Reg Nos with artwork: {identified_reg_nos_with_artwork}, Reg Nos without artwork: {set(identified_reg_nos)-set(identified_reg_nos_with_artwork)})")
    else:
        log_message(f"     ⚠️ Found Copyright Exhibit in {pdf_file} (Reg Nos: {identified_reg_nos}) but Artwork found.")
    
    langfuse.get_client().update_current_span(
        input={
            "CaseIndex": index,
            "PDFFile": os.path.basename(pdf_file),
            "Certainty": certainty,
            "NumPages": pdf_document.page_count,
            "LocationID": location_id
        },
        output={
            "Registration Numbers Found": identified_reg_nos,
            "Registration Numbers with Artwork Found": identified_reg_nos_with_artwork
        }
    )
    return identified_reg_nos