import os
from PIL import Image
import hashlib
import sys
import imagehash
sys.path.append(os.getcwd())
from Common.Constants import sanitize_name
import shutil

def remove_text_duplicates(directory, images):
    list_of_images_removed = []
    trademark_text_dict = {}
    for image_filename, image_data in images.items():
        if 'trademark_text' in image_data:
            trademark_text = ' '.join(image_data['trademark_text'])
            if len(trademark_text) > 1:
                if trademark_text in trademark_text_dict:
                    list_of_images_removed.append((image_filename, trademark_text_dict[trademark_text]))
                    file_path = os.path.join(directory, image_filename)
                    os.remove(file_path)
                else:
                    trademark_text_dict[trademark_text] = image_filename
    
    return list_of_images_removed

def remove_exact_duplicates(directory, list_of_images):
    """
    Removes exact duplicate files in the specified directory.
    """
    hash_dict = {}
    list_of_images_removed = []
    for filename in os.listdir(directory):
        file_path = os.path.join(directory, filename)
        if os.path.isfile(file_path) and filename in list_of_images:
            file_hash = compute_md5(file_path)
            if file_hash in hash_dict:
                original_file = os.path.basename(hash_dict[file_hash])
                # print(f"Removing exact duplicate: {file_path}")
                os.remove(file_path)
                list_of_images_removed.append((filename, original_file))
            else:
                hash_dict[file_hash] = file_path

    return list_of_images_removed

def compute_md5(file_path):
    """
    Computes MD5 hash of the given file.
    """
    hash_md5 = hashlib.md5()
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)
    return hash_md5.hexdigest()

def remove_near_duplicates(directory, list_of_images, hash_size=16, threshold=10):
    """
    Removes near-duplicate images in the specified directory based on perceptual hashing.
    """
    hashes = {}
    list_of_images_removed = []
    for filename in os.listdir(directory):
        file_path = os.path.join(directory, filename)
        if os.path.isfile(file_path) and filename in list_of_images and is_image(file_path):
            try:
                with Image.open(file_path) as img:
                    img_hash = imagehash.phash(img, hash_size=hash_size)
                # Compare with existing hashes
                duplicate_found = False
                for existing_hash, existing_file in hashes.items():
                    if img_hash - existing_hash < threshold:
                        original_file = os.path.basename(hashes[existing_hash])
                        # print(f"Removing near duplicate: {file_path} (similar to {existing_file})")
                        os.remove(file_path)
                        duplicate_found = True
                        list_of_images_removed.append((filename, original_file))
                        break
                if not duplicate_found:
                    hashes[img_hash] = file_path
            except Exception as e:
                print(f"Error processing {file_path}: {e}")

    return list_of_images_removed


def remove_near_duplicates2(directory, hash_size=16, threshold=10):
    """
    Removes near-duplicate images in the specified directory based on perceptual hashing.
    """
    hashes = {}
    dupe_folder = os.path.join(directory, "dupe")
    if not os.path.exists(dupe_folder):
        os.makedirs(dupe_folder)
    list_of_images_removed = []
    for filename in os.listdir(directory):
        file_path = os.path.join(directory, filename)
        if os.path.isfile(file_path) and is_image(file_path) and "_full" not in filename:
            try:
                with Image.open(file_path) as img:
                    img_hash = imagehash.phash(img, hash_size=hash_size)
                # Compare with existing hashes
                duplicate_found = False
                for existing_hash, existing_file in hashes.items():
                    if img_hash - existing_hash < threshold:
                        original_file = os.path.basename(hashes[existing_hash])
                        # print(f"Removing near duplicate: {file_path} (similar to {existing_file})")
                        # os.remove(file_path)
                        shutil.copy(file_path, os.path.join(dupe_folder, filename))
                        shutil.copy(os.path.join(directory, original_file), os.path.join(dupe_folder, original_file))
                        duplicate_found = True
                        list_of_images_removed.append((filename, original_file))
                        break
                if not duplicate_found:
                    hashes[img_hash] = file_path
            except Exception as e:
                print(f"Error processing {file_path}: {e}")

    return list_of_images_removed


def is_image(file_path):
    """
    Checks if the file is an image based on its extension.
    """
    image_extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff', '.webp', '.jpx']
    _, ext = os.path.splitext(file_path)
    return ext.lower() in image_extensions

if __name__ == "__main__":
#     df = get_table_from_US("CASES")
#     remove_duplicates(df)
    # list_of_images_removed = remove_near_duplicates2('D:\\Documents\\Programing\\TRO\\USside\\Documents\\2024-09-10 - 1_24-cv-08255\\images', 16, 70)
    # print(len(list_of_images_removed))
    # print("hash = 12")

    # Best setting for trademarks
    list_of_images_removed = remove_near_duplicates2('D:\\Documents\\Programing\\TRO\\USside\\Documents\\2024-09-10 - 1_24-cv-08255\\images', 12, 35)
    print(len(list_of_images_removed))
