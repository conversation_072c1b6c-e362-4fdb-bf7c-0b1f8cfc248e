For each case
    for each step folder
        For each pdf
            Ignore industry report
            OCR the pdf
            Look for keywords in OCR text that indicate some IP (either Trademark works, Copyright words or Patent words):
                Get trademarks from the PDF:
                    OCR to get the reg nb (or for one formart the serial number / application number)
                    Get the data from USPTO (it currently fails with serial numbers!):
                        Trademark info from XML, or from ... if unavaible
                        Image from URL, or from status (?) zip
                        Certificate from ?
                    If any info cannot be retrieved from USPTO: use OCR as a fallback
                Get copyrights from the PDF:
                    .....
                Get patents from the PDF:
                    LLM to get the reg nbs
                    Patent-> Get patents using registration numbers
            If we found IP type same as the nos_description: stop

    If we did not find IP type same as nos_description (or we found copyright certificated but no pictures):
        Send all the PDFs to the AI, and ask for the registration numbers of IP:
            Get trademarks using registration numbers (using API):
                ....
            Get copyrights using registration numbers:
                .....
            Patent-> Get patents using registration numbers

    If we did not find IP type same as nos_description:
        Use the plaintiff name to search nos_description IP:
            For trademark:
                Search uspto website (uc) using "Owner" field (filter out dead and pending trademarks)
                Get the list of serial_number (== application number) for all the trademarks (registration number is not available on result page)
                Get trademarks using serial numbers (using API):
                    ....
            For copyright:
                ....
            For patents:
                Patent-> Search by Name (using quasi API)
                Patent-> Get patents using registration numbers
                Ask LLM to select the best Patents (max 20) based on the Complaint PDF
                Process these patents PDFs:
                    Extract Design pages
                    Extract the Certificate page
                    Store all the information in the case_df dataframe

Patent -> Get patents using registration numbers (using quasi API):
    For each patent reg number: (Registration number needs to be only the middle part (********) of the document_id (US D7,844,454 S))
        If the reg number is in Database
            get patent (html and pdf) from NAS
            get data from html
            add plaintiff id in database
        If the reg number is not in database:
            get patent (html and pdf) from USPTO (using reg number)
            get data from html
            Ask LLM which are the design pages  (should be store these in the database to save time?)
            send to NAS
            save to Database
    Process these patents PDFs:
        Extract Design pages
        Extract the Certificate page
        Store all the information in the case_df dataframe

Patent -> Search by Name (using quasi API):
    Get Assignee, Applicant, Inventor using LLM and the complaint

    Search name is always limited to the first 2 words, and excludes Chinese city names

    Patent Search:
        Try 1: Assignee from AI in Patent Search
            Using granted patents:
                if more than 20 results: rerun the search taking only Design patents
            If no results in Granted Patents => look in applied patents
                if more than 20 results: rerun the search taking only Design patents
        Try 2: ⁠Applicant from AI in Patent Search

    same sub logic as Assignee
        ⁠Inventor from AI in Patent Search

    same sub logic as Assignee
        Plantiff name in Patent Search

    same sub logic as Assignee
    If no result from Patent Search -> Assignee Search:
        ⁠Try 1: Assignee from AI in Assignee Search
            i⁠f more than 20 results: only keep the design patent
        Try 2: ⁠Plaintiff Name in Assignee Search
            if more than 20 results: only keep the design patent

**Copyright**:

Main doc: -> reg number (only store VA in target_reg_no))

    EITHER process_copyright_registration_numbers  (if LLM said IP is in main doc)

    1. try to get the image from the main doc (only VA)

    2. if not: get the artist name and art name (either form the main doc or USCO) and save the info in reg_numbers_pictures_not_found

    OR process_exhibit/other_steps:

    1. Image template + Keyword to identify certificate & OCR the registration number

    2. Look on next page for picture

    3. Add the reg numbers where we found pictures to "found_reg_numbers" and the onces where we did not to "target_reg_nos" or ""non_target_reg_nos" depending if it has "VA". Nothing gets added to "reg_numbers_pictures_not_found" because that field needs Artist name and Art Name.

process_copyright_registration_numbers (this is the wrong function! it focus on main doc_pdf) for any outstanding reg_noumber in ""target" but not in "found"

ChineseWebsites: search case number -> get images and reg numbers -> return a {"reg_no": "image_path", ....} that is **not used**

CopyrightsSearchGoogle???

SearchByPlaintiffName???

```mermaid





graph TD
    A[Trace: process_case] --> B[Span: identify_case_and_navigate];
    B --> C[Span: fetch_case_details];
    C --> D[Span: process_steps_main_loop];
    D --> E{Loop for each relevant_step};
        E --> F[Span: process_step_X];
            F --> G[Span: download_main_document_step_X];
            F --> H[Span: analyze_pdf_for_ip_main_doc_step_X];
            F --> I[Span: process_ip_from_main_doc_step_X];
                I --> I1[Span: process_patent_registration_numbers_main_doc_step_X];
                I --> I2[Span: process_trademark_registration_numbers_main_doc_step_X];
                I --> I3[Span: process_copyright_registration_numbers_main_doc_step_X];
            F --> J[Span: process_llm_identified_locations_step_X];
                J --> K[Span: process_targeted_other_steps_step_X];
                    K --> K1[Span: download_target_step_Y_main_doc];
                    K --> K2[Span: process_attachement_pdfs_target_step_Y];
                J --> L[Span: process_targeted_attachments_step_X];
                    L --> L1[Span: download_target_attachments_step_X];
                    L --> L2[Span: process_attachement_pdfs_target_attachments_step_X];
                J --> M[Span: process_other_attachments_step_X];
                    M --> M1[Span: download_other_attachment_Z_step_X];
                    M --> M2[Span: process_attachement_pdfs_other_attachment_Z_step_X];
            F --> N[Span: analyze_pdf_for_ip_unresolved_pdfs_step_X];
            F --> O[Span: process_ip_from_unresolved_pdfs_step_X];
                O --> O1[Span: process_patent_registration_numbers_unresolved_step_X];
                O --> O2[Span: process_trademark_registration_numbers_unresolved_step_X];
    D --> P[Span: finalize_picture_processing];
        P --> Q[Span: secondary_ip_search];
            Q --> Q1[Span: search_chinese_websites];
                Q1 --> Q1a[Span: scrape_case_data];
                Q1 --> Q1b[Span: process_trademark_regno_chinese_website];
                Q1 --> Q1c[Span: get_and_process_patents_from_uspto_using_regnos_chinese_website];
            Q --> Q2[Span: search_copyright_google_images];
                Q2 --> Q2a[Span: process_copyright_images_from_google];
            Q --> Q3[Span: search_ip_by_plaintiff_name];
                Q3 --> Q3a[Span: scrape_USCO_copyright_by_name];
                Q3 --> Q3b[Span: process_copyright_images_from_google_by_name];
                Q3 --> Q3c[Span: get_and_process_patents_from_uspto_using_plaintiff_name];
                Q3 --> Q3d[Span: get_trademark_data_by_name];
        P --> R[Span: deduplicate_all_trademarks];
        P --> S[Span: create_resized_images];
        P --> T[Span: create_plaintiff_images_view];
    P --> U[Span: file_uploads];
    U --> V[Span: ai_tasks];
        V --> V1[Span: get_ai_summary_translations];
        V --> V2[Span: translate_steps_for_a_case];
    V --> W[Span: final_db_updates];
```




**Search by name: only if ip type is NOS description!**

1. Patent:

   1. Ask LLM to design search criteria
   2. Search USPTO by  name in this order: LLM Assignee, LLM Applicant, LLM Inventor, Plaintiff Name (all 4, no early break, max 50 results each)
   3. Search Patent_Assignment in this order: LLM Assignee, Plaintiff Name (both, no early break). If results are more than 20, only keep design patents
   4. Remove duplicates
   5. Fetch all from USPTO
   6. Give all Patent to LLM and ask LLM to select relevant one
2. Trademark:

   1. Search USPTO using Plaintiff Name, max 100 results
   2. If > 95 results or (more than 20 results and more than 3 owners): Search USPTO using Plaintiff_Names[0], max 100 results. If more than one results, keep this instead of first search
   3. Fetch all from USPTO
3. Copyright:

   1. For each plaintiff_name in plaintiff_names: remove_acronyms, scrape_USCO, if results: break
   2. Get images from Google
