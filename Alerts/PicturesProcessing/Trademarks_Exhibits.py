import os, sys, cv2, os, pytesseract, re, time, asyncio, shutil, json, time
from typing import Optional, Any

sys.path.append(os.getcwd())

import numpy as np
import pandas as pd
from logdata import log_message
from Alerts.PicturesProcessing.ProcessPicturesShared import crop_white_space, keywords
from AI.TradeMark_Extraction import ai_tradeMark_cropping
from IP.Trademarks.Trademark_API import get_trademarks_uspto, get_certificate_local_path
from IP.Trademarks.USPTO_TSDR_API import format_reg_number
from Alerts.PicturesProcessing.OCRProcessor import OCRProcessor
from Common import Constants
from langfuse import observe
import langfuse
from Alerts.IPTrackingManager import IPTrackingManager # Added import
from AI.GC_VertexAI import vertex_genai_multi_async


@observe(capture_input=False, capture_output=False)
async def process_trademark_exhibit(df, index, case_images_directory, case_directory_1, pdf_document, pdf_file, page_text, page_ocr_data, page_images, ip_manager: IPTrackingManager) -> tuple[bool, list[str]]:
    """
    Processes potential trademark exhibits found in PDF pages.

    Args:
        df: DataFrame containing case data.
        index: Index of the current case in the DataFrame.
        case_images_directory: Directory to save extracted images.
        case_directory_1: Another case-specific directory.
        pdf_document: fitz.Document object for the PDF.
        pdf_file: Path to the PDF file.
        page_text: Dictionary mapping page numbers to extracted text.
        page_ocr_data: Dictionary mapping page numbers to detailed OCR data.
        page_images: Dictionary mapping page numbers to image file paths.
        ip_manager: Instance of IPTrackingManager.

    Returns:
        A tuple: (bool indicating if a trademark exhibit was found, list of registration/serial numbers found).
    """
    trademark_list = []
    found_ids = [] # List to store found registration/serial numbers

    pdf_file_no_ext = os.path.splitext(pdf_file)[0]
    os.makedirs(os.path.join(case_directory_1, pdf_file_no_ext), exist_ok=True)
    
    page_numbers_with_keywords = {template_nb: [] for template_nb in [1,2]}
    page_numbers_with_keywords2 = {template_nb: [] for template_nb in [1,2]}

    for page_number in range(pdf_document.page_count):
        page_text[page_number+1] = page_text[page_number+1].lower()
        if any(keyword.lower() in page_text[page_number+1] for keyword in keywords["Trademark"][0]):
            # looking at the position of the "principal" word:
            principal_indices = [i for i, word in enumerate(page_ocr_data[page_number+1]['text']) if word.lower().startswith('principal')]
            leftmost_word = min([page_ocr_data[page_number+1]['left'][i] for i, word in enumerate(page_ocr_data[page_number+1]['text']) if len(word) > 1])
            rightmost_word = max([page_ocr_data[page_number+1]['left'][i] + page_ocr_data[page_number+1]['width'][i] for i, word in enumerate(page_ocr_data[page_number+1]['text']) if len(word) > 1])
            if principal_indices:
                principal_left = min(page_ocr_data[page_number+1]['left'][i] for i in principal_indices)
                difference = abs(principal_left - leftmost_word)
                if difference < 0.10 * (rightmost_word - leftmost_word):
                    page_numbers_with_keywords[1].append(page_number + 1)
                else:
                    page_numbers_with_keywords[2].append(page_number + 1)
    
    # 3. Process the list
    if page_numbers_with_keywords[1] or page_numbers_with_keywords[2]:
        log_message(f"        - Extracting trademark information from images: {len(page_numbers_with_keywords[1])+len(page_numbers_with_keywords[2])} images")
        ocr_fallback_data = []
        for page_number in set(page_numbers_with_keywords[1] + page_numbers_with_keywords[2]):

            template_nb = 1 if page_number in page_numbers_with_keywords[1] else 2
            # filename = f"{pdf_file_no_ext}_page{page_number}_0.jpg"
            trademark_filename = f"{pdf_file_no_ext}_page{page_number}_0.webp"
            image = cv2.imread(page_images[page_number])
            this_page_ocr_data = page_ocr_data[page_number]

            # extract tradeMarks and related information
            full_filename = os.path.splitext(trademark_filename)[0] + "_full.webp"

            metadata = {'template_nb': template_nb, 'full_filename': full_filename, 'trademark_filename': trademark_filename, 'page_number': page_number}

            if template_nb == 1:
                # is_reg = true is registration number, else it is a serial number. id_n is the actual number
                is_reg, id_n, int_cls_list, _ = extract_trademark_regno_intcl_tempalte1(trademark_filename, this_page_ocr_data, image)
                if is_reg == False:
                    print(f"Serial number found for template 1 for file: {trademark_filename}. Retrying for debugging and getting the Reg Number instead.")
                    is_reg, id_n, int_cls_list, _ = extract_trademark_regno_intcl_tempalte1(trademark_filename, this_page_ocr_data, image)
            else: # if the template is 2
                is_reg, id_n, int_cls_list, _, _, _ = await extract_trademark_regno_intcl_tempalte2(trademark_filename, this_page_ocr_data, image, page_images[page_number])
                if is_reg == False: # Case: case_id: 13707  1:25-cv-03112
                    print(f"Serial number found for template 2 for file: {trademark_filename}. Retrying for debugging and getting the Reg Number instead.")
                    is_reg, id_n, int_cls_list, _, _, _ = await extract_trademark_regno_intcl_tempalte2(trademark_filename, this_page_ocr_data, image, page_images[page_number])
            
            if id_n !="":
                metadata['id_n'] = id_n
                metadata['int_cls_list'] = int_cls_list
            
                # uspto_tasks.append((is_reg, id_n, metadata))
                if is_reg:
                    trademark_list.append({"reg_no": id_n, "ser_no": None, "metadata": metadata})
                    if id_n: found_ids.append(id_n) # Collect ID
                else:
                    trademark_list.append({"reg_no": None, "ser_no": id_n, "metadata": metadata})
                    if id_n: found_ids.append(id_n) # Collect ID

        if len(trademark_list) == 0:
            log_message(f"        - 🔥 No initial trademark IDs found via OCR in exhibit {pdf_file}.", level="INFO")
            return False, []
        
        # Filter out trademarks that have already been successfully sourced by IPManager
        already_sourced_reg_nos = ip_manager._state["trademark"]["found_reg_nos"]
        trademarks_to_fetch_from_uspto = []
        for item in trademark_list:
            reg_no_to_check = item.get('reg_no')
            if reg_no_to_check and reg_no_to_check in already_sourced_reg_nos:
                log_message(f"        - Trademark Reg No: {reg_no_to_check} already sourced by IP Manager. Skipping USPTO fetch for it.", level="DEBUG")
                continue
            trademarks_to_fetch_from_uspto.append(item)

        if not trademarks_to_fetch_from_uspto:
            log_message(f"        - All identified trademarks in {pdf_file} were already sourced. No new USPTO calls needed.", level="INFO")
            return True, list(set(found_ids)) # Still return all textually found IDs

        # Process all USPTO calls concurrently
        start_time = time.time()
        try:
            df_trademarks = await get_trademarks_uspto(trademarks_to_fetch_from_uspto, case_date= df.at[index, "date_filed"], plaintiff_names = df.at[index, "plaintiff_names"])
        except Exception as e_uspto:
            import traceback
            log_message(f"        - ❌ Error in USPTO Trademark API call: {e_uspto}. Traceback: {traceback.format_exc()}", level="ERROR")
            df_trademarks = pd.DataFrame() # Ensure df_trademarks is an empty DataFrame on error
        
        log_message(f"        - ✅ USPTO API processed {len(df_trademarks)} trademarks in {time.time() - start_time:.1f}s for {pdf_file}", level="INFO")

        # Track sources for metadata
        processed_count = 0
        
        for df_tr_index, row in df_trademarks.iterrows():
            metadata = row["metadata"]
            
            formatted_reg_no = row.get("reg_no") # Use .get for safety
            ser_no = row.get("ser_no") # Get ser_no if available
            if formatted_reg_no: # Ensure we have a reg_no before proceeding
                tradeMark_img_path = os.path.join(Constants.local_ip_folder, "Trademarks", "Images", f"{formatted_reg_no}.webp")
                # Use new certificate path structure
                certificate_base_folder = os.path.join(Constants.local_ip_folder, "Trademarks", "Certificates")
                certificate_img_path = get_certificate_local_path(certificate_base_folder, ser_no=ser_no, reg_no=formatted_reg_no)
            else:
                tradeMark_img_path = None
                certificate_img_path = None

            if tradeMark_img_path and os.path.exists(tradeMark_img_path):
                shutil.copy(tradeMark_img_path, os.path.join(case_images_directory, metadata['trademark_filename']))
            if certificate_img_path and os.path.exists(certificate_img_path):
                shutil.copy(certificate_img_path, os.path.join(case_images_directory, metadata['full_filename']))

            # Check if at least the trademark image exists from USPTO data
            if not tradeMark_img_path or not os.path.exists(tradeMark_img_path):
                print(f"\033[91m 🔥 Trademark image missing for {str(formatted_reg_no)} during exhibit processing. Falling back.\033[0m")
                ocr_fallback_data.append((df_tr_index, metadata))
            else:
                # Successfully processed via USPTO data
                if formatted_reg_no and formatted_reg_no not in found_ids: # Add reg_no if found via USPTO lookup
                    found_ids.append(formatted_reg_no)
                if isinstance(row["int_cls"], str): # if the trademark was already in the database, it is coming as a string. But to aggregate similar trademarks later, we need it as a list.
                    int_cls_final = json.loads(row["int_cls"])
                else:
                    int_cls_final = row["int_cls"]
                df.at[index, 'images']['trademarks'][metadata['trademark_filename']] = {'reg_no': [metadata['id_n']],'int_cls_list': int_cls_final,'trademark_text': [row["text"]],'full_filename': [metadata['full_filename']]}
                processed_count += 1
                if row['image_source'] not in df.at[index, 'images_status']['trademark_status']['exhibit']['sources']:
                    df.at[index, 'images_status']['trademark_status']['exhibit']['sources'][row['image_source']] = 0 # Initialize if needed
                df.at[index, 'images_status']['trademark_status']['exhibit']['sources'][row['image_source']] += 1
        
        # Handle OCR fallbacks
        for df_tr_index, metadata in ocr_fallback_data:
            print(f"Failed to extract information from USPTO most likelly because the reg_no {metadata['id_n'] if 'id_n' in metadata else 'None'} from OCR data is incorrect or some info are missing from USPTO")

            # 1.  The certificate
            formatted_reg_no_fallback = metadata.get('id_n') # Use the ID from metadata for fallback paths
            uspto_certificate_img_path = None
            if formatted_reg_no_fallback:
                # Use new certificate path structure for fallback
                certificate_base_folder = os.path.join(Constants.local_ip_folder, "Trademarks", "Certificates")
                uspto_certificate_img_path = get_certificate_local_path(certificate_base_folder, ser_no=None, reg_no=formatted_reg_no_fallback)

            if uspto_certificate_img_path and os.path.exists(uspto_certificate_img_path): # if we got the certificate from USPTO use that
                image = cv2.imread(uspto_certificate_img_path)
                ocr_text, this_page_ocr_data = OCRProcessor.text_in_image(image) # Re-run OCR on the certificate if needed
            else: # else use the page from the exhibit
                image = cv2.imread(page_images[metadata['page_number']])
                this_page_ocr_data = page_ocr_data[metadata['page_number']]
            certificate_path = os.path.join(case_images_directory, metadata['full_filename'])
            cv2.imwrite(certificate_path, image, [cv2.IMWRITE_WEBP_QUALITY, 80])

            
            # 2. The trademark image (fallback)
            uspto_tradeMark_img_path = None
            if formatted_reg_no_fallback:
                uspto_tradeMark_img_path = os.path.join(Constants.local_ip_folder, "Trademarks", "Images", f"{formatted_reg_no_fallback}.webp")

            int_cls_ocr = [] # Initialize as list
            if not uspto_tradeMark_img_path or not os.path.exists(uspto_tradeMark_img_path): # the trademark image is missing from USPTO or the text is missing
                # Extract from the USPTO certificate page image
                if uspto_certificate_img_path and os.path.exists(uspto_certificate_img_path): # if we have the certificate from USPTO, use that
                    page_image_fallback = cv2.imread(uspto_certificate_img_path)
                    ocr_text, page_ocr_data_fallback = OCRProcessor.text_in_image(image) # Re-run OCR on the certificate if needed
                else: # Extract from the exhibit page image
                    page_image_fallback = cv2.imread(page_images[metadata['page_number']])
                    page_ocr_data_fallback = page_ocr_data[metadata['page_number']]

                if metadata['template_nb'] == 1:
                    success, trademark_image, id_n_fallback, int_cls_ocr, _ = await extract_trademark_info_template1(metadata['trademark_filename'], page_image_fallback, page_ocr_data_fallback)
                else:
                    success, trademark_image, id_n_fallback, int_cls_ocr, _ = await extract_trademark_info_template2(metadata['trademark_filename'], page_image_fallback, page_ocr_data_fallback, certificate_path)

                if success:
                    cv2.imwrite(os.path.join(case_images_directory, metadata['trademark_filename']), trademark_image, [cv2.IMWRITE_WEBP_QUALITY, 80])
                    if id_n_fallback and id_n_fallback not in found_ids: # Collect ID from OCR fallback
                         found_ids.append(id_n_fallback)
                else:
                    print(f"\033[91m 🔥 Fallback OCR extraction failed for {metadata.get('id_n', 'unknown ID')}\033[0m")
                    continue # Skip adding this entry if OCR fallback fails completely
            else:
                # Copy existing USPTO trademark image
                trademark_image = cv2.imread(uspto_tradeMark_img_path)
                shutil.copy(uspto_tradeMark_img_path, os.path.join(case_images_directory, metadata['trademark_filename'])) # Corrected destination
                if formatted_reg_no_fallback and formatted_reg_no_fallback not in found_ids: # Collect ID if using existing USPTO image
                    found_ids.append(formatted_reg_no_fallback)


            # 3. Trademark Text (fallback)
            # Use the trademark_image obtained either from OCR extraction or existing USPTO file
            trademark_text = df_trademarks.at[df_tr_index, 'text'] if df_tr_index in df_trademarks.index else ""
            if not trademark_text:
                trademark_text = get_trademark_text_ocr(trademark_image) if trademark_image is not None else ""

            # Use int_cls from OCR fallback if USPTO data was missing/None
            # Ensure df_trademarks.at[df_tr_index, 'int_cls'] exists before accessing
            int_cls_from_df = df_trademarks.at[df_tr_index, 'int_cls'] if df_tr_index in df_trademarks.index else None
            int_cls_final = int_cls_ocr if not int_cls_from_df else int_cls_from_df

            # Ensure int_cls_final is a list
            if isinstance(int_cls_final, str):
                try:
                    int_cls_final = json.loads(int_cls_final)
                except json.JSONDecodeError:
                    int_cls_final = [] # Default to empty list if parsing fails
            elif not isinstance(int_cls_final, list):
                 int_cls_final = [] # Default to empty list if not a list


            # Save fallback data in the dataframe
            df.at[index, 'images']['trademarks'][metadata['trademark_filename']] = {
                'reg_no': [id_n_fallback] if 'id_n_fallback' in locals() and id_n_fallback else [formatted_reg_no_fallback] if formatted_reg_no_fallback else [], # Prioritize OCR id_n if available
                'int_cls_list': int_cls_final,
                'trademark_text': [trademark_text],
                'full_filename': [metadata['full_filename']]
            }
            processed_count += 1
            if "OCR" not in df.at[index, 'images_status']['trademark_status']['exhibit']['sources']:
                df.at[index, 'images_status']['trademark_status']['exhibit']['sources']["OCR"] = 0 # Initialize if needed
            df.at[index, 'images_status']['trademark_status']['exhibit']['sources']["OCR"] += 1

        # Update images_status field with exhibit metadata if trademarks were found
        if processed_count > 0:
            # Update exhibit information
            df.at[index, 'images_status']['trademark_status']['exhibit']['count'] += processed_count
            df.at[index, 'images_status']['trademark_status']['exhibit']['steps'].append(os.path.dirname(pdf_file).split("\\")[-1])

        # Return True and the list of found IDs if processing occurred
        return True, list(set(found_ids)) # Ensure uniqueness

    # No keywords found, return False and empty list
    langfuse.get_client().update_current_span(
        input={
            "CaseIndex": index,
            "PDFFile": os.path.basename(pdf_file),
            "NumPages": pdf_document.page_count
        },
        output={
            "ExhibitFound": True if ('page_numbers_with_keywords' in locals() and (page_numbers_with_keywords[1] or page_numbers_with_keywords[2])) else False,
            "FoundIDsCount": len(found_ids) if 'found_ids' in locals() else 0,
            "TrademarksProcessedFromUSPTO": len(df_trademarks) if 'df_trademarks' in locals() else 0,
            "OCRFallbacks": len(ocr_fallback_data) if 'ocr_fallback_data' in locals() else 0
        }
    )
    return False, []



def save_trademark_images(case_images_directory, image, picture_region_cropped, full_filename):
    try:
        # Save both images
        cv2.imwrite(os.path.join(case_images_directory, full_filename), image, [cv2.IMWRITE_WEBP_QUALITY, 80])
        
        trademark_filename = full_filename.replace("_full.webp", ".webp")
        cv2.imwrite(os.path.join(case_images_directory, trademark_filename), picture_region_cropped, [cv2.IMWRITE_WEBP_QUALITY, 80])
        
    except Exception as e:
        print(f"Error processing {full_filename}: {e}")
        return None


# @profile
def get_trademark_text_ocr(picture_region_cropped):
    # trademark_rgb = cv2.cvtColor(picture_region_cropped, cv2.COLOR_BGR2RGB) # Convert image to RGB (for pytesseract) - Not needed because image is grey scale
    # --oem 3: OEM stands for OCR Engine Mode. Mode 3 specifies the use of the LSTM neural network-based OCR engine only.
    # --psm 10: PSM stands for Page Segmentation Mode. Mode 10 is specifically for treating the image block, best for Logos.
    
    # Check if the image is valid
    if picture_region_cropped is None or picture_region_cropped.size == 0:
        print("Error: Empty image passed to get_trademark_text_ocr")
        return ""
    
    # Get detailed OCR data including confidence scores
    ocr_data = pytesseract.image_to_data(picture_region_cropped, lang='eng', config='--oem 3 --psm 10', output_type=pytesseract.Output.DICT)
    
    # Filter out empty strings and low confidence words
    valid_words = []
    for i, word in enumerate(ocr_data['text']):
        word = word.strip()
        confidence = int(ocr_data['conf'][i])
        
        # Skip empty strings and words with confidence below 60%
        if not word or confidence < 60:
            continue
            
        # Skip "words" that are just punctuation or special characters
        if not any(c.isalnum() for c in word):
            continue
            
        valid_words.append(word)
    
    # If we have valid words, combine them into a single string
    if valid_words:
        # Join words and clean up extra whitespace
        trademark_text = ' '.join(valid_words).strip()
        
        # Additional validation: if the text is very long or looks like noise,
        # it's probably not a real text logo
        if len(trademark_text) > 50 or len(valid_words) > 10:
            return ""
            
        return trademark_text
    
    return ""
    

# Function to extract regions based on relative coordinates
def extract_region(image, coords):
    height, width = image.shape[:2]
    x_start = int(coords['x_start'] * width)
    x_end = int(coords['x_end'] * width)
    y_start = int(coords['y_start'] * height)
    y_end = int(coords['y_end'] * height)
    return image[y_start:y_end, x_start:x_end]



# @profile
async def extract_trademark_info_template1(filename, image, page_ocr_data):
  
    is_reg_no, reg_no, int_cls_list, y_percent = extract_trademark_regno_intcl_tempalte1(filename, page_ocr_data, image)
    if reg_no == "":
        return False, None, None, None, None

    ## Get the Logo / Trademark
    # Define ROIs (region of interest) using relative coordinates (adjust these based on your templates)
    y_start = min(0.18, y_percent - 0.02)
    rois_picture = {'x_start': 0.15, 'x_end': 0.85, 'y_start': y_start, 'y_end': (y_percent - 0.003)}

    # Extract the picture region
    picture_region = extract_region(image, rois_picture)

    # If we have part of the image (the first line is not entirely white) => decrease starting point by 1%. Max start 10% of the page.
    height = picture_region.shape[0]
    first_line = picture_region[0, :]
    while (not np.all(first_line > 240)) and rois_picture['y_start'] > 0.10:
        rois_picture['y_start'] -= 0.01
        picture_region = extract_region(image, rois_picture)
        height = picture_region.shape[0]
        first_line = picture_region[0, :]

    # if we have the "United States patent and trademark office" in the image => we should have increased starting point instead of reducing it.
    # logo_text = pytesseract.image_to_string(picture_region).lower()
    logo_data = get_region_ocr_data(page_ocr_data, rois_picture['x_start'], rois_picture['x_end'], rois_picture['y_start'], rois_picture['y_end'])
    logo_text = ' '.join([word for word in logo_data['text'] if word.strip()]).lower()
    if sum(keyword in logo_text for keyword in ["united", "states", "patent", "trademark", "office"]) > 2:
        rois_picture['y_start'] = 0.19
        picture_region = extract_region(image, rois_picture)
        height = picture_region.shape[0]
        first_line = picture_region[0, :]
        while (not np.all(first_line > 240)) and rois_picture['y_start'] < (rois_picture['y_end'] - 0.03):
            rois_picture['y_start'] += 0.01
            picture_region = extract_region(image, rois_picture)
            height = picture_region.shape[0]
            first_line = picture_region[0, :]

    # Remove all the white space around the image
    picture_region_cropped = await crop_trademark_image(picture_region)
    
    return True, picture_region_cropped, reg_no, int_cls_list, picture_region


# @profile
async def extract_trademark_info_template2(filename, image, page_ocr_data, certificate_path):
    """extract trademark information from an image and its associated OCR data
    Used to handle documentation before 2010

    Args:
        filename (str): the name of the image
        image (np.ndarray): the cv2 image in numpy representation
        page_ocr_data (dict): OCR text recognitiation hierarchy in dictionary format

    return:
        (bool): a boolean variable indicating wheather the extraction is successful
        (np.npdarray): region of interest that after aggressive cropping
        (str): the registration number of the document
        (list): a list of int representing the classification number of the document
        (np.npdarray): the region of interest before the performance of agressive cut
    """
    text_y_start_percent = None

    is_reg, reg_no, int_cls_list, image_y_start, image_y_start_percent, text_y_start_percent = await extract_trademark_regno_intcl_tempalte2(filename, page_ocr_data, image, certificate_path)
    if reg_no == "":
        return False, None, None, None, None

    
    # Find the starting location of the text
    if text_y_start_percent is None:
        # find the word the most on the left among word below Principal Register (where page_ocr_data['top'][i] > image_y_start)
        text_after_principal_register = [page_ocr_data['left'][i] for i, word in enumerate(page_ocr_data['text']) if len(word) > 1 and page_ocr_data['top'][i] > image_y_start]
        if len(text_after_principal_register) > 0:
            leftmost_x = min(text_after_principal_register)
            leftmost_x_percent = leftmost_x / image.shape[1]
            text_start_index = min([i for i, word in enumerate(page_ocr_data['text']) if len(word) > 1 and page_ocr_data['top'][i] > image_y_start and abs(page_ocr_data['left'][i] / image.shape[1] - leftmost_x_percent) < 0.01])
            text_y_start = page_ocr_data['top'][text_start_index]
            text_y_start_percent = text_y_start / image.shape[0]
        else:
            text_y_start_percent = min(image_y_start_percent + 0.40, 0.95)

    # Get words that meet our criteria
    valid_words = [(page_ocr_data['left'][i], page_ocr_data['left'][i] + page_ocr_data['width'][i]) 
                  for i, word in enumerate(page_ocr_data['text']) 
                  if len(word) > 1 and page_ocr_data['top'][i] > image_y_start]
    if valid_words:
       # If we found valid words, use their positions
       word_most_left = min(left for left, right in valid_words)
       word_most_right = max(right for left, right in valid_words)
    else:
       # Fallback to default values - using 15% and 85% of image width
       word_most_left = int(image.shape[1] * 0.15)
       word_most_right = int(image.shape[1] * 0.85)
    
    
    
    rois_picture = {'x_start': word_most_left/image.shape[1], 'x_end': word_most_right/image.shape[1], 'y_start': (image_y_start_percent + 0.005), 'y_end': (text_y_start_percent - 0.02)}

    # Extract the picture region
    if rois_picture['y_end'] - rois_picture['y_start'] < 0.05:
        rois_picture['y_end'] = rois_picture['y_start'] + 0.13

    picture_region = extract_region(image, rois_picture)

    picture_region_cropped = await crop_trademark_image(picture_region)

    return True, picture_region_cropped, reg_no, int_cls_list, picture_region


async def crop_trademark_image(picture_region):
    try:
        tradeMark_img = await ai_tradeMark_cropping(picture_region)
    except Exception as e_ai:        
        picture_region_cropped, (x_offset, y_offset) = crop_white_space(picture_region)
    
        pencentage_white_pixels = np.mean(picture_region_cropped > 240) * 100
        if pencentage_white_pixels > 95:
            picture_region_cropped, (x_offset, y_offset) = crop_white_space(picture_region, 0.004)
        
        tradeMark_img = picture_region_cropped

    return tradeMark_img


def get_region_ocr_data(ocr_data, x_start_pct, x_end_pct, y_start_pct, y_end_pct):
    """
    Filter OCR data by region, converting percentage boundaries to pixels.
    Args:
        ocr_data: Dictionary containing OCR data
        x_start_pct, x_end_pct, y_start_pct, y_end_pct: Region boundaries in percentages
    Returns:
        Dictionary containing filtered OCR data
    """
    # Get page dimensions from the OCR data
    # We'll use the maximum values found in the data as the page dimensions
    page_width = max([left + width for left, width in zip(ocr_data['left'], ocr_data['width'])])
    page_height = max([top + height for top, height in zip(ocr_data['top'], ocr_data['height'])])

    # Convert percentages to absolute pixels
    x_start = int(x_start_pct * page_width)
    x_end = int(x_end_pct * page_width)
    y_start = int(y_start_pct * page_height)
    y_end = int(y_end_pct * page_height)

    filtered_data = {key: [] for key in ocr_data.keys()}
    for i, (left, top) in enumerate(zip(ocr_data['left'], ocr_data['top'])):
        if (x_start <= left <= x_end and 
            y_start <= top <= y_end):
            for key in ocr_data.keys():
                filtered_data[key].append(ocr_data[key][i])
    return filtered_data

def extract_trademark_regno_intcl_tempalte1(filename, page_ocr_data, image):
    """
    Extracts the trademark registration number from OCR data of a specific page in a PDF document.

    Args:
        filename (str): The name of the file being processed.
        page_ocr_data (dict): The OCR data for the page.

    Returns:
        tuple: A tuple containing a boolean indicating whether the returned number is
        registration number or serial number, and the extracted registration number (or an empty string if not found).
    """
    # Getting rois for registration number
    # Extract text regions
    rois_reg_no = {'x_start': 0.00, 'x_end': 0.40, 'y_start': 0.20, 'y_end': 0.65}

    # Perform OCR with positional data
    data = get_region_ocr_data(page_ocr_data, rois_reg_no['x_start'], rois_reg_no['x_end'], rois_reg_no['y_start'], rois_reg_no['y_end'])

    # Combine text into full text for regex matching
    full_text = ' '.join(data['text'])

    # Use regex to find "Reg. No." and numbers
    reg_no_match = re.search(r'Reg\,?\.?\s*No\,?\.?\s*([\s\d,]+)', full_text, re.IGNORECASE)
    if reg_no_match:
        reg_no = reg_no_match.group(1).strip()

        # handling issue when extract number was being matched in regex
        # for example, "2,748,784 35 Commander Drive, Orlando" Would result in "2,748,784 35 "
        while len(re.findall(r"\d", reg_no)) > 7:
            reg_no = reg_no[:-1].strip()
        reg_no = format_reg_number(reg_no)

        # Find the index of "Reg. No." in the text data
        height, width = image.shape[:2]
        for i, word in enumerate(data['text']):
            if word.lower().startswith('reg'):
                # Extract the position
                y = data['top'][i]
                y_percent = y / height
                break

        int_cls_match = re.search(r'Int\,?\.?\s*(?:Cls?|Ch|ClL).?\,?\.?:?.?\s*((?:\d+(?:,\s*|\s+and\s+)?)+)(?:\s*\d+)?', full_text, re.IGNORECASE)
        if int_cls_match:
            int_cls_string = int_cls_match.group(1).strip().lower().replace("and", ",").replace(",,", ",")
            int_cls_list = [int(cls.strip()) for cls in int_cls_string.split(",") if cls.strip()]
            int_cls_list = [int(str(cls)[:2]) if len(str(cls)) == 3 else cls for cls in int_cls_list]
            int_cls_list = [int(str(cls)[0]) if cls > 45 else cls for cls in int_cls_list]
            # print(f"Int. Cl.: {int_cls_list}")
            if max(int_cls_list) > 45:
                print(f"Int. Cl. is greater than 45: {int_cls_list} for template 1 for file: {filename}. Full text is: {full_text}.")
        else:
            print(f"Int. Cl. not found for template 1 for file: {filename}. Full text is: {full_text}.")
            int_cls_list = []

        return True, reg_no, int_cls_list, y_percent
    
    else:
        print(f"Reg. No. not found for template 1 for file: {filename}. Full text is: {full_text}.")
        return True, "", "", 0
    



async def extract_trademark_regno_intcl_tempalte2(filename, page_ocr_data, image, page_image):
    text_full = ' '.join(page_ocr_data['text'])

    # Use regex to find "Reg. No." and numbers
    principal_register_match = re.search(r'Principal\.?\s*Register', text_full, re.IGNORECASE)

    text_y_start_percent = None
    is_reg = True

    # Format after 1991: Find the index of "principal_register_match" in the text data
    if principal_register_match:
        reg_no_match = re.search(r'Reg\,?\.?\s*No\,?\.?\s*([\s\d,]+)', text_full[:principal_register_match.start()], re.IGNORECASE)
        int_cls_match = re.search(r'Int\,?\.?\s*(?:Cls?|Ch|ClL).?\,?\.?:?.?\s*((?:\d+(?:,\s*|\s+and\s+)?)+)(?:\s*\d+)?', text_full[:principal_register_match.start()], re.IGNORECASE)
        if not int_cls_match:
            # Not found => look in the rest of the document (for format 1970, it is in the text later, not at the top)
            int_cls_match = re.search(r'Int\,?\.?\s*(?:Cls?|Ch|ClL).?\,?\.?:?.?\s*((?:\d+(?:,\s*|\s+and\s+)?)+)(?:\s*\d+)?', text_full[principal_register_match.start():], re.IGNORECASE)
        
        for i, word in enumerate(page_ocr_data['text']):
            if 'principal' in word.lower():
                # format from the 50s, we start image after "application"
                if "trade-mark" in ' '.join(page_ocr_data['text'][i:i+5]).lower(): 
                    for j, word in enumerate(page_ocr_data['text'][i:], start=i):
                        if word.lower().startswith('application'):
                            i = j
                            break

                # Extract the position
                if 'PRINCIPAL' in page_ocr_data['text'][i]:
                    height_multiplier = 1.1
                else:
                    height_multiplier = 1.5  # the bottom of the text. When pytesseract performs OCR, it reports the height based on what it considers the main body of the text (essentially the x-height in typography terms), which often doesn't include the full extent of descenders (like in 'p', 'g', 'y') or ascenders (like in 'l', 't', 'h'). This is why the word "Principal" might be getting cut. => We multiply by 1.5 because we have P and g in the text.
                
                image_y_start = page_ocr_data['top'][i] + (page_ocr_data['height'][i]*height_multiplier)  
                image_y_start_percent = image_y_start / image.shape[0]
                principal_register_index = i
                break
    else:
        print(f"Principal Register not found for template 2 for file: {filename}. Full text is: {text_full}.")
        return True, "", "", 0


    # Get the Reg. No.
    if reg_no_match:
        reg_no = reg_no_match.group(1).strip()
        
        while len(re.findall(r"\d", reg_no)) > 7:
            reg_no = reg_no[:-1].strip()
        reg_no = format_reg_number(reg_no)

    else:
        # Format 1970-1991: Find the index of "Ser. No." in the text data
        ser_no_match = re.search(r'Ser\,?\.?\s*No\,?\.?\s*([\d,]+)', text_full, re.IGNORECASE)
        if ser_no_match:
            ser_no = ser_no_match.group(1).strip()
            prompt = f"Based on the following image, can you find the registration number of the trademark? The serial number is {ser_no} but what I need is the registration number which is 6 or 7 digits located at the top right of the registration certificate. Only return the 6 or 7 numbers. Do no include any text or the comma(s)."
            prompt_list = [("text", prompt), ("image_path", page_image)]
            ai_answer = await vertex_genai_multi_async(prompt_list, model_name=Constants.IMAGE_MODEL_FREE)
            reg_no = format_reg_number(re.search(r'(\d{6,7})', ai_answer).group(1))
            if not reg_no:
                is_reg = False
                reg_no = re.sub("[^0-9]", "", ser_no)
                
            for i in range(principal_register_index, principal_register_index + 25):
                if page_ocr_data['text'][i].lower().startswith('ser') and not page_ocr_data['text'][i].lower().startswith("service"):
                    image_y_start = page_ocr_data['top'][i] + page_ocr_data['height'][i]
                    image_y_start_percent = image_y_start / image.shape[0]
                    principal_register_index = i
                    break
        else:
            # Format <1970: Find the index of "Registration No." in the text data
            registration_no_match = re.search(r'Registration\s*No\,?\.?\s*([\d,]+)', text_full, re.IGNORECASE)
            if registration_no_match:
                reg_no = registration_no_match.group(1).strip()
                reg_no = format_reg_number(reg_no)
                    
                # Take image form "united" to "Statement": 
                for i in range(principal_register_index, principal_register_index + 10):
                    if page_ocr_data['text'][i].lower().startswith('united'):
                        image_y_start = page_ocr_data['top'][i] + page_ocr_data['height'][i]
                        image_y_start_percent = image_y_start / image.shape[0]
                        principal_register_index = i
                        break
                for i in range(principal_register_index, len(page_ocr_data['text'])):
                    if "statement" in page_ocr_data['text'][i].lower():
                        text_y_start = page_ocr_data['top'][i]
                        text_y_start_percent = text_y_start / image.shape[0]
                        break
            else:
                print(f"Reg. No. not found for template 2 for file: {filename}. Full text is: {text_full}.")
                reg_no = ""

    # Get the Int. Cl.
    if int_cls_match:
        int_cls_string = int_cls_match.group(1).strip().lower().replace("and", ",").replace(",,", ",")
        try: 
            int_cls_list = [int(cls.strip()) for cls in int_cls_string.split(",") if cls.strip()] 
            int_cls_list = [int(str(cls)[:2]) if len(str(cls)) == 3 else cls for cls in int_cls_list]
            int_cls_list = [int(str(cls)[0]) if cls > 45 else cls for cls in int_cls_list]
            # print(f"Int. Cl.: {int_cls_list}")
            if max(int_cls_list) > 45:
                print(f"Int. Cl. is greater than 45: {int_cls_list} for template 2 for file: {filename}. Full text is: {text_full}.")
        except:
            print(f"Error processing Int. Cl. for template 2 for file: {filename}. The string was: {int_cls_string}")
    else:
        print(f"Int. Cl. not found for template 2 for file: {filename}. Full text is: {text_full}.")
        int_cls_list = []

    return is_reg, reg_no, int_cls_list, image_y_start, image_y_start_percent, text_y_start_percent
