from Alerts.ReprocessCases import reprocess_cases
from Alerts.LexisNexis.Scrape_Date_Range import scrape_date_range
from DatabaseManagement.ImportExport import get_table_from_GZ
from datetime import date, timedelta
import asyncio
import pandas as pd
from logdata import log_message
from dateutil.relativedelta import relativedelta


def get_month_range(months_ago):
    today = date.today()
    target_date = today - relativedelta(months=months_ago)
    start_date = target_date.replace(day=1)
    end_date = (start_date + relativedelta(months=1)) - timedelta(days=1)
    description = f"{start_date.strftime('%B %Y')}"
    return start_date, end_date, description

def get_date_range_for_period(period_type):
    today = date.today()

    if period_type == 'weekly':
        start_date = today - timedelta(days=7)
        end_date = today
        description = "last week"

    elif period_type == 'monthly_1':
        start_date, end_date, description = get_month_range(1)

    elif period_type == 'monthly_2':
        start_date, end_date, description = get_month_range(2)

    elif period_type == 'monthly_3':
        start_date, end_date, description = get_month_range(3)

    elif period_type == 'monthly_4':
        start_date = today - relativedelta(months=18)
        end_date = today - relativedelta(months=3)
        description = "open cases 3-18 months old"

    else:
        raise ValueError(f"Unknown period_type: {period_type}")

    return start_date, end_date, description

def run_case_fetch(period_type=None, nb_days=None):
    """
    Generalized fetch function to handle both periodic and daily fetches.
    If `period_type` is provided, uses predefined range logic.
    If `nb_days` is provided, fetches from today - nb_days to today.
    """

    today = date.today()
    if period_type:
        start_date, end_date, description = get_date_range_for_period(period_type)
    elif nb_days is not None:
        start_date = today - timedelta(days=nb_days)
        end_date = today
        description = f"last {nb_days} day(s)"
    else:
        raise ValueError("Either period_type or nb_days must be provided.")

    log_message(f"Starting fetch for {description} ({start_date} to {end_date})", level="INFO")

    # Load database tables - it will save db connection in ReprocessCases as well as Scrape_Date_Range
    log_message("Loading database tables...", level="INFO")
    all_cases_df = get_table_from_GZ("tb_case", force_refresh=True)
    plaintiffs_df = get_table_from_GZ("tb_plaintiff", force_refresh=True)

    # Scrape cases (skip if monthly_4)
    if period_type == 'monthly_4':
        log_message("Skipping scraping for monthly_4 (database only)", level="INFO")
        new_cases_df = pd.DataFrame()
    else:
        log_message(f"Scraping cases from {start_date} to {end_date}...", level="INFO")
        new_cases_df = scrape_date_range(start_date, end_date, df_existing_cases=all_cases_df, plaintiff_df=plaintiffs_df)
        log_message(f"Found {len(new_cases_df)} new cases from scraping", level="INFO")

    # Filter open cases in DB (only for periodic fetches)
    if period_type is not None and 'date_filed' in all_cases_df.columns:
        all_cases_df['date_filed'] = pd.to_datetime(all_cases_df['date_filed']).dt.date
        db_cases_df = all_cases_df[
            (all_cases_df['date_filed'] >= start_date) &
            (all_cases_df['date_filed'] <= end_date) &
            ((all_cases_df['class_code'] == 'Open') | (all_cases_df['class_code'].isna()))
        ].copy()
        log_message(f"Found {len(db_cases_df)} open cases from database", level="INFO")
    else:
        db_cases_df = pd.DataFrame()

    # Merge
    if not new_cases_df.empty and not db_cases_df.empty:
        combined_cases_df = pd.concat([new_cases_df, db_cases_df], ignore_index=True, sort=False)
        log_message(f"Combined dataset has {len(combined_cases_df)} cases after de-duplication", level="INFO")
    elif not new_cases_df.empty:
        combined_cases_df = new_cases_df
        log_message("Using only scraped cases", level="INFO")
    elif not db_cases_df.empty:
        combined_cases_df = db_cases_df
        log_message("Using only DB-filtered cases", level="INFO")
    else:
        log_message("No cases to process", level="INFO")
        return pd.DataFrame(), {
            'cases_no_ip_then_some_ip': 0,
            'cases_no_ip_then_all_ip': 0,
            'cases_some_ip_then_more_ip': 0,
            'cases_some_ip_then_all_ip': 0,
            'cases_ip_regressed': 0,
            'cases_already_all_ip': 0,
            'total_count': 0
        }

    processing_options = {
        'update_steps': True,
        'process_pictures': True,
        'upload_files_nas': True,
        'upload_files_cos': True,
        'run_plaintiff_overview': True,
        'run_summary_translation': True,
        'run_step_translation': True,
        'save_to_db': True,
        'processing_mode': 'resume' if period_type else 'full_reprocess',
        'refresh_days_threshold': 15
    }

    trace_label = f"{period_type.title() if period_type else 'Daily'} Fetch Workflow"

    # Add new scraped cases to the full database cases
    if not new_cases_df.empty:
        all_cases_df = pd.concat([all_cases_df, new_cases_df], ignore_index=True, sort=False)
        log_message(f"Combined full database with {len(new_cases_df)} new cases", level="INFO")

    success_status, tracking_dict = asyncio.run(
        reprocess_cases(
            cases_to_reprocess=combined_cases_df,
            processing_options=processing_options,
            trace_name=trace_label,
            full_cases_df=all_cases_df,
            plaintiff_df=plaintiffs_df
        )
    )

    return combined_cases_df, tracking_dict

# Convenience functions for each schedule type
def daily_fetch(nb_days=1):
    """Daily fetch - scrape cases from the last nb_days"""
    return run_case_fetch(nb_days=nb_days)

def weekly_fetch():
    """Saturday 1:30 AM NY - Search last week"""
    return run_case_fetch(period_type='weekly')

def monthly_fetch_1():
    """1st Sunday 1:30 AM NY - Search last month"""
    return run_case_fetch(period_type='monthly_1')

def monthly_fetch_2():
    """2nd Sunday 1:30 AM NY - Search 2 months ago"""
    return run_case_fetch(period_type='monthly_2')

def monthly_fetch_3():
    """3rd Sunday 1:30 AM NY - Search 3 months ago"""
    return run_case_fetch(period_type='monthly_3')

def monthly_fetch_4():
    """4th Sunday 1:30 AM NY - Search open cases 3-18 months old (database only, no scraping)"""
    return run_case_fetch(period_type='monthly_4')

if __name__ == '__main__':
    # Test different fetch types
    daily_fetch()  # Daily fetch
    # weekly_fetch()  # Weekly fetch (last week)
    # monthly_fetch_1()  # Monthly fetch (last month)
    # monthly_fetch_2()  # Monthly fetch (2 months ago)
    # monthly_fetch_3()  # Monthly fetch (3 months ago)
    # monthly_fetch_4()  # 4th Sunday fetch (open cases 3-18 months old, database only)

    # # Test date range calculation
    # for period in ['weekly', 'monthly_1', 'monthly_2', 'monthly_3', 'monthly_4']:
    #     start, end, desc = get_date_range_for_period(period)
    #     print(f"{period}: {desc} ({start} to {end})")