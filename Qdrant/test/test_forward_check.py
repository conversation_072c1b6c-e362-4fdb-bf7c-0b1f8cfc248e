import asyncio
import aiohttp
import os
from dotenv import load_dotenv
import numpy as np

# Load environment variables from the Qdrant/.env file
load_dotenv()

QDRANT_API_URL = os.getenv("QDRANT_API_URL")
API_BEARER_TOKEN = os.getenv("API_BEARER_TOKEN")

async def test_forward_check_endpoint():
    """
    Tests the /forward_check endpoint of the Qdrant API.
    """
    if not QDRANT_API_URL or not API_BEARER_TOKEN:
        print("Error: QDRANT_URL or API_BEARER_TOKEN not found in Qdrant/.env")
        return
    print(f"API_BEARER_TOKEN used by client: {API_BEARER_TOKEN}")

    print(f"Testing /forward_check endpoint at: {QDRANT_API_URL}/forward_check")

    # Create a dummy SigLIP vector (replace with actual embedding generation if needed)
    # SigLIP embeddings are typically 1024-dimensional for images.
    # dummy_siglip_vector = np.random.rand(1024).tolist()
    image_path = "D:\\Documents\\Programing\\TRO\\USSideRefactoring\\Documents\\IP\\Trademarks\\USPTO_Daily\\Images\\08\\08\\72140808.webp"
    image_path = "D:\\Documents\\Programing\\TRO\\USSideRefactoring\\Documents\\IP\\Trademarks\\USPTO_Daily\\Images\\85\\79\\71547985.webp"
    from Check.RAG.siglip_model import SiglipModel
    siglip_config = {
        "vector_size": 1024,
        "model_name_or_path": "google/siglip2-large-patch16-512"
    }
    model_siglip = SiglipModel(model_id="siglip2_large_patch16_512", config=siglip_config)
    model_siglip.load()
    dummy_siglip_vector = model_siglip.compute_features([image_path])[0].tolist()

    payload = {
        "client_id": "test_client_123",
        "check_id": "test_check_456",
        "search_ip_type": "Trademark",
        "threshold": -1,
        "threshold_text": 0.01,
        "top_n": 10,
        "products": [
            {
                "siglip_vector": dummy_siglip_vector,
                "filename": "test_product_001.jpg"
            }
        ]
    }

    headers = {
        "Authorization": f"Bearer {API_BEARER_TOKEN}",
        "Content-Type": "application/json"
    }

    async with aiohttp.ClientSession() as session:
        try:
            # Log the request details before sending
            print(f"Sending POST request to: {QDRANT_API_URL}/forward_check")
            print(f"Request Payload: {payload}")
            print(f"Request Headers: {headers}")

            async with session.post(
                f"{QDRANT_API_URL}/forward_check",
                json=payload,
                headers=headers
            ) as response:
                print(f"HTTP Status Code: {response.status}")
                response_text = await response.text() # Read as text to catch non-JSON errors
                print("Response Body:")
                print(response_text)

                try:
                    response_json = await response.json()
                    if response.status == 200:
                        print("\nTest successful: Received a 200 OK response.")
                        if "results" in response_json and len(response_json["results"]) > 0:
                            print("API returned potential infringements.")
                        else:
                            print("API returned no potential infringements (this might be expected depending on data).")
                    else:
                        print(f"\nTest failed: Received an error status code {response.status}.")
                        print(f"Error details: {response_json.get('detail', 'No detail provided')}")
                except aiohttp.ContentTypeError:
                    print(f"\nTest failed: Response was not JSON. Raw response: {response_text}")
                    print(f"Status: {response.status}")

        except aiohttp.ClientConnectorError as e:
            print(f"\nConnection Error: Could not connect to the API at {QDRANT_API_URL}. Is the API running?")
            print(f"Details: {e}")
        except Exception as e:
            print(f"\nAn unexpected error occurred: {e}")

if __name__ == "__main__":
    asyncio.run(test_forward_check_endpoint())