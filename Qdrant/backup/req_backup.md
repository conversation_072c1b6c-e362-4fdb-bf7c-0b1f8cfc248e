Create a backup container (using a docker file and cron jobs inside) that make database backup, qdrant backup, sync files with NAS

6.1 Qdrant Backup: An automated daily process must:
o	Execute client.create_snapshot(collection_name="IP_Assets").
o	Execute client.create_snapshot(collection_name="Product_Images").
o	Implement a retention policy (we keep 2 daily and 1 weekly snapshots) from the Qdrant node using client.delete_snapshot().

6.2 Postgresql Backup: An automated daily process must:
o	Execute pg_dumpall -U maidalv -h localhost > /docker/postgresql/backup/dump_$(date +%Y%m%d%H%M%S).sql
o	Implement a retention policy (we keep 2 daily and 1 weekly backups)
Must happen at the same time as the Qdrant backup, for both to be as synced as possible

6.3 Sync with NAS....