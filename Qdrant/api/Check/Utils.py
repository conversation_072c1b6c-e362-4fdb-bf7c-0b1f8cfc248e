
import hashlib
import os
from Common.uuid_utils import generate_obfuscated_key

# def create_ip_url(plaintiff_id, image_filename):
#     """Create IP URL with proper formatting"""
#     IP_Url = f"http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id}/high/{image_filename}"
#     IP_Url_new = f"https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id}/high/{image_filename}"
#     return IP_Url

def create_ip_url(ip_type, seed): # For Trademark the seed is ser_no, for Copyright the seed is reg_no, for Patent the seed is basename(filename) which is the reg_no with the page number
    """Create IP URL with proper formatting"""
    obfuscated_reg_no = generate_obfuscated_key(seed)
    if ip_type == "Patent":
        extension = "png"
    else:
        extension = "webp"
    
    IP_Url = f"https://tro-1330776830.cos.ap-guangzhou.myqcloud.com/ip_assets/{ip_type}s/{obfuscated_reg_no}.{extension}"
    
    return IP_Url