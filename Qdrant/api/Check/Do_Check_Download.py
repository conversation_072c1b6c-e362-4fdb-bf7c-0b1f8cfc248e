import os, asyncio, aiohttp, re
import urllib.parse


# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗

# Global lock management for downloads
_download_locks = {}
_lock_for_locks_dict = asyncio.Lock() # Lock to protect access to the _download_locks dictionary

async def download_from_url(url, local_path, error_file=None, error_message=None):
    # Normalize path for consistent lock keying and existence checks
    abs_local_path = os.path.abspath(local_path)

    # Get or create a lock for this specific path, protecting _download_locks dict access
    async with _lock_for_locks_dict:
        if abs_local_path not in _download_locks:
            _download_locks[abs_local_path] = asyncio.Lock()
        path_lock = _download_locks[abs_local_path]

    async with path_lock: # Acquire the specific lock for this path
        # After acquiring the lock, check if the file already exists.
        # If so, another coroutine likely downloaded it while we were waiting.
        if os.path.exists(local_path): # Use non-absolute local_path for user-facing messages/operations if preferred
            # print(f"\033[94mFile {local_path} already exists. Skipping download for {url}.\033[0m")
            return True

        retries = 3
        delay = 1
        timeout = aiohttp.ClientTimeout(total=60, connect=10, sock_read=30)
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8",
            "Accept-Language": "en-US,en;q=0.9",
            "Connection": "keep-alive"
        }

        # Use a temporary file for atomic download. Original naming is fine with the lock.
        temp_path = f"{local_path}.tmp"
        
        try: # This try/finally ensures temp_path cleanup within the lock
            os.makedirs(os.path.dirname(local_path), exist_ok=True) # Ensure directory for final local_path exists

            for attempt in range(retries):
                try:
                    connector = aiohttp.TCPConnector(
                        ssl=False,
                        force_close=False,
                        limit=10,
                        ttl_dns_cache=300
                    )
                    
                    async with aiohttp.ClientSession(headers=headers, timeout=timeout, connector=connector) as session:
                        async with session.get(url, allow_redirects=True) as response:
                            response.raise_for_status()
                            
                            # Write to temp_path
                            with open(temp_path, 'wb') as fd:
                                while True:
                                    chunk = await response.content.read(8192)
                                    if not chunk:
                                        break
                                    fd.write(chunk)
                    
                    # Successfully downloaded to temp_path
                    # Now, atomically move it to local_path
                    # Remove existing file before renaming to allow overwrite (os.rename fails on Windows if dest exists)
                    if os.path.exists(local_path): # This check might seem redundant due to the one at the start of path_lock
                                                   # but could be a safeguard if an external process created the file
                                                   # or if a very old version existed.
                        try:
                            os.remove(local_path)
                        except OSError as e_remove:
                            # If we can't remove it, rename will likely fail. Log and let it try.
                            print(f"\033[93mWarning: Could not remove existing {local_path} before rename: {e_remove}. Proceeding with rename attempt.\033[0m")
                    
                    os.rename(temp_path, local_path) # temp_path should be released by with open()
                    
                    if attempt > 0: # Log if it wasn't the first attempt
                        print(f"\033[92mAttempt {attempt + 1}/{retries}: Successfully downloaded {url} to {local_path}\033[0m")
                    return True # Download successful
                        
                except Exception as e:
                    print(f"\033[91mAttempt {attempt + 1}/{retries} error downloading {url} (to {local_path} via {temp_path}): {e}\033[0m")
                    if os.path.exists(temp_path): # If download failed, clean up partial temp file before retry
                        try:
                            os.remove(temp_path)
                        except OSError as e_remove_tmp:
                            print(f"\033[93mWarning: Could not remove temporary file {temp_path} after failed attempt: {e_remove_tmp}\033[0m")
                    
                    if attempt < retries - 1:
                        await asyncio.sleep(delay)
                        delay *= 2
                    else: # Last attempt failed
                        # Error will be recorded outside the loop by the caller's logic
                        pass 

        finally:
            # Ensure the specific temp_path for this locked operation is cleaned up if it still exists
            # (e.g., if all retries failed, or an unexpected error before rename)
            if os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except OSError as e:
                    print(f"\033[93mWarning: Could not remove final temporary file {temp_path}: {e}\033[0m")
        
        # If all retries failed and we've exited the loop
        if error_file:
            with open(error_file, 'a') as f:
                if error_message:
                    f.write(error_message)
                else:
                    f.write(f"{url} - Failed after {retries} attempts to download to {local_path}\n")
        return False # Download failed after all retries

# This might not be optimal. Need to compare with using pool = SimpleThreadPool()  pool.add_task(client.download_file, test_bucket, file_cos_key, localName)
# Documentation: https://www.tencentcloud.com/document/product/436/46469

def sanitize_filename_from_url(url):
    parsed_url = urllib.parse.urlparse(url)
    filename = os.path.basename(parsed_url.path)
    if not filename:
        filename = "downloaded_file"
    # Replace problematic characters for Windows filenames.
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    return filename
