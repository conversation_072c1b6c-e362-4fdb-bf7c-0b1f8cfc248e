"""
Authentication utilities for the API.
"""
import os
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

# Security scheme
security = HTTPBearer()

async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """
    Verify the authentication token.
    
    Args:
        credentials: The HTTP authorization credentials.
        
    Returns:
        The verified token.
        
    Raises:
        HTTPException: If the token is invalid.
    """
    if credentials.credentials != os.getenv("API_BEARER_TOKEN"):
        print(f"Invalid token: {credentials.credentials}, expected {os.getenv("API_BEARER_TOKEN")}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )
    return credentials.credentials
