# Api endpoint for the client to collect the reverse check results. 
# Table name: reverse_check_result
# Table structure: 
# id
# client_id
# check_id
# result: {check_id, risk_level, results=[still a list (aggregate hits by check_id)]}
# create_time
# update_time

from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse
from utils.db import get_reverse_check_results_by_client_id_and_date, get_cached_api_keys
import json
from datetime import date, datetime

def json_serial(obj):
    """JSON serializer for objects not serializable by default json code"""
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    raise TypeError ("Type %s not serializable" % type(obj))

router = APIRouter()

# Load API keys from database with caching
allowed_api_keys = get_cached_api_keys()
print(f"✅ Reverse check status: Loaded {len(allowed_api_keys)} API keys from database")

@router.post("/reverse_check_status")
async def reverse_check_status(request: Request):
    data = await request.json()
    api_key = data.get("api_key")
    date = data.get("date")
    start_date = data.get("start_date")
    end_date = data.get("end_date")

    if not api_key or api_key not in allowed_api_keys:
        return JSONResponse(status_code=401, content={"error": "Invalid API Key. Authentication failed."})

    # Support both single date and date range
    client_id = allowed_api_keys[api_key]["id"] if isinstance(allowed_api_keys[api_key], dict) else allowed_api_keys[api_key]

    if date:
        # Legacy single date support
        results = get_reverse_check_results_by_client_id_and_date(client_id, date)
        return JSONResponse(content=json.loads(json.dumps({"results": results}, default=json_serial)))
    elif start_date and end_date:
        # New date range support
        from datetime import datetime
        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_dt = datetime.strptime(end_date, '%Y-%m-%d').date()

            # Validate date range (maximum 1 month)
            if (end_dt - start_dt).days > 31:
                return JSONResponse(status_code=400, content={"error": "Date range cannot exceed 1 month (31 days)"})

            if start_dt > end_dt:
                return JSONResponse(status_code=400, content={"error": "Start date must be before or equal to end date"})

            results = get_reverse_check_results_by_client_id_and_date(client_id, start_date, end_date)

            # Group results by date and check_id for the frontend
            grouped_results = {}
            for result in results:
                date_key = result['date_added'].strftime('%Y-%m-%d') if hasattr(result['date_added'], 'strftime') else str(result['date_added'])
                if date_key not in grouped_results:
                    grouped_results[date_key] = {}
                check_id = str(result['check_id'])
                if check_id not in grouped_results[date_key]:
                    grouped_results[date_key][check_id] = result

            return JSONResponse(content=json.loads(json.dumps({"results": grouped_results}, default=json_serial)))
        except ValueError as e:
            return JSONResponse(status_code=400, content={"error": f"Invalid date format. Use YYYY-MM-DD: {str(e)}"})
    else:
        return JSONResponse(status_code=400, content={"error": "Missing required field: either 'date' or both 'start_date' and 'end_date'"})