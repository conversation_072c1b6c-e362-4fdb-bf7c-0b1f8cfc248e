"""
Qdrant service for vector search operations.
"""

from qdrant_client import Qdrant<PERSON>lient
from qdrant_client.http import models
from typing import List, Dict, Any
import uuid
import os

qdrant_client = QdrantClient(url=os.getenv("QDRANT_URL", "https://vectorstore1.maidalv.com:6333"), api_key=os.getenv("QDRANT_API_KEY"), timeout=30, https=True)

# if os.name != 'nt':
#     # Config in the docker
#     qdrant_client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY, https=True)    
# else:
#     # Config in local
#     qdrant_client = QdrantClient(url=os.environ["QDRANT_API_URL"], api_key=QDRANT_API_KEY)

def generate_product_point_id(check_id: str, image_path: str) -> str:
    """
    Generates a deterministic UUIDv5 for a given check_id and image_path.
    This ensures that the same image for the same check always gets the same ID.
    """
    # Create a stable, unique string for the asset.
    unique_name = f"{check_id}{image_path}"
    return str(uuid.uuid5(uuid.NAMESPACE_DNS, unique_name))

def upsert_product_images(client_id: str, check_id: str, products: List[Dict[str, Any]]):
    """
    Upsert product images into the Product_Images collection.

    Args:
        client_id: The client ID.
        check_id: The check ID.
        products: A list of product image data.

    Returns:
        The result of the upsert operation.
    """
    points = []
    for product in products:
        # Create point
        point = models.PointStruct(
            id=generate_product_point_id(check_id, product.get("filename")),
            vector={
                "siglip_vector": product.get("siglip_vector")
            },
            payload={
                "client_id": client_id,
                "check_id": check_id,
                "filename": product.get("filename")  # Store filename for forward check metadata
            }
        )
        points.append(point)

    # Upsert points into Product_Images collection
    return qdrant_client.upsert(
        collection_name="Product_Images",
        points=points
    )

def upsert_ip_assets(ip_assets: List[Dict[str, Any]]):
    """
    Upsert IP assets into the IP_Assets collection.

    Args:
        ip_assets: A list of IP asset data.

    Returns:
        The result of the upsert operation.
    """
    points = []
    for ip_asset in ip_assets:
        # Create point with single SigLIP vector
        point = models.PointStruct(
            id=ip_asset.get("id"),
            vector={
                "siglip_vector": ip_asset.get("siglip_vector", [])
            },
            payload={
                "ip_type": ip_asset.get("ip_type"),
                "metadata": ip_asset.get("metadata", {})
            }
        )
        points.append(point)

    # Upsert points into IP_Assets collection
    return qdrant_client.upsert(
        collection_name="IP_Assets",
        points=points
    )

def delete_points(collection_name: str, point_ids: List[str]):
    """
    Delete points from a collection.

    Args:
        collection_name: The name of the collection.
        point_ids: A list of point IDs to delete.

    Returns:
        The result of the delete operation.
    """
    return qdrant_client.delete(
        collection_name=collection_name,
        points_selector=models.PointIdsList(points=point_ids)
    )


def query_batch_points(collection_name: str, search_queries: List[models.QueryRequest]):
    return qdrant_client.query_batch_points(collection_name=collection_name, requests=search_queries)

def query_single_point(collection_name: str, query_vector: List[float],
                       min_score_threshold: float = 0.5, max_results: int = 200,
                       ip_type_filter: str = None, query_filename: str = None):
    """
    Execute a query using SigLIP vector embedding.

    Args:
        collection_name: The name of the collection.
        query_vector: The SigLIP query vector.
        min_score_threshold: The minimum score threshold to include results.
        max_results: The maximum number of results to return.
        ip_type_filter: Optional filter for specific IP type (Copyright, Patent, Trademark).

    Returns:
        The filtered query results.
    """
    # Build filter conditions
    must_conditions = []
    if ip_type_filter:
        must_conditions.append(models.FieldCondition(key="ip_type", match=models.MatchValue(value=ip_type_filter)))

    filter_conditions = None
    if must_conditions:
        filter_conditions = models.Filter(must=must_conditions)

    # Query with SigLIP vector
    results = qdrant_client.query_points(
        collection_name=collection_name,
        query=query_vector,
        using="siglip_vector",
        limit=max_results,
        score_threshold=min_score_threshold,
        query_filter=filter_conditions,
        with_payload=True,
        with_vectors=False
    )
    
    # Create a new dictionary with query_filename
    results_with_data_filename = []
    for result in results.points:
        result_dict = {
            "id": result.id,
            "score": result.score,
            "payload": result.payload,
            "query_filename": query_filename
        }
        results_with_data_filename.append(result_dict)

    return results_with_data_filename

if __name__ == "__main__":
    from Check.RAG.siglip_model import SiglipModel
    from Common.uuid_utils import generate_uuid
    
    test_picture = r"D:\Documents\Programing\TRO\ModelTestsWorkbenchData\pictures\copyright\product\Screenshot_2025-07-01_234711.png"
    test_picture = r"D:\Win10User\Downloads\API problems\Screenshot 2025-07-01 234348.png"
    test_picture = r"D:\Win10User\Downloads\API problems\Screenshot 2025-07-02 104350.png"  # handbag, plaintiff is 884, trademark
    
    # ip_type = "Copyright"
    # ip_type = "Patent"
    ip_type = "Trademark"
    
    siglip_model = SiglipModel(model_id="siglip2_large_patch16_512", config={"vector_size": 1024, "model_name_or_path": "google/siglip2-large-patch16-512"})
    siglip_model.load()
    test_picture_vector = siglip_model.compute_features(data_list=[test_picture], data_type="image")[0].tolist()
    
    ### 0. Retrieve the actual match and check the similarity
    if ip_type == "Trademark":
        my_uuid = generate_uuid('88590403')
        print(f"The expected UUID is {my_uuid}")
        # Retrieve that particular point_id and print the score:
        results_uuid = qdrant_client.query_points(
            collection_name="IP_Assets",
            query=test_picture_vector,
            using="siglip_vector",
            query_filter=models.Filter(
                must=[
                    models.HasIdCondition(has_id=[my_uuid])
                ]
            ),
            limit=1
        )
        if results_uuid.points:
            print(f"Similarity score between test picture and point {my_uuid}: {results_uuid.points[0].score}")
    
    
    ### 1. Retrieve similar items using premade function
    results = query_single_point(collection_name="IP_Assets", query_vector=test_picture_vector,
                    min_score_threshold=0.2, max_results=30, ip_type_filter=ip_type)
    
    
    ### 2. Retrieve similar items using querry - the qdrant_service (check) way
    must_conditions = []
    must_conditions.append(models.FieldCondition(key="ip_type", match=models.MatchValue(value=ip_type)))
    must_conditions.append(models.FieldCondition(key="plaintiff_id", match=models.MatchValue(value=884)))
    filter_conditions = models.Filter(must=must_conditions)

    # Query with SigLIP vector
    results2 = qdrant_client.query_points(
        collection_name="IP_Assets",
        query=test_picture_vector,
        using="siglip_vector",
        limit=30,
        score_threshold=0.2,
        query_filter=filter_conditions,
        with_payload=True,
        with_vectors=False
    )
    
    ### 3. Retrieve similar items using batch query - for custom filtering
    from qdrant_client import QdrantClient, models as qdrant_models
    results3 = qdrant_client.query_batch_points(collection_name="IP_Assets",requests=[models.QueryRequest(
        query=test_picture_vector,   # But model test workbench uses query=str(prod_id)
        using="siglip_vector",
        limit=50,
        filter=qdrant_models.Filter(
            must=[
                qdrant_models.FieldCondition(key="ip_type",match=qdrant_models.MatchValue(value=ip_type))
            ]
        ),
        score_threshold=0.2,
        with_payload=True,
        with_vector=False
    )])
    
    
    print(results)
    print("Finished")
