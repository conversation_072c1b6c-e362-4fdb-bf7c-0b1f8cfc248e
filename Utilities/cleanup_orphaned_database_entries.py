#!/usr/bin/env python3
"""
Utility script to clean up orphaned database entries for missing image files.
This removes database entries for files that don't exist on the filesystem.
"""

import os
import sys
import json
from pathlib import Path

# Add the app directory to Python path
sys.path.append('/app')

from DatabaseManagement.ImportExport import get_table_from_GZ, insert_and_update_df_to_GZ_batch
from FileManagement.Tencent_COS import get_case_dir
from logdata import log_message


def cleanup_orphaned_entries(docket_number=None, case_id=None, dry_run=True):
    """
    Clean up orphaned database entries for missing image files
    
    Args:
        docket_number: Case docket number (e.g., '6:25-cv-00274')
        case_id: Case ID from database
        dry_run: If True, only show what would be cleaned up without making changes
    """
    print(f"🧹 {'DRY RUN: ' if dry_run else ''}Cleaning up orphaned database entries...")
    
    # Get case data
    df_cases = get_table_from_GZ('tb_case')
    
    if docket_number:
        case_row = df_cases[df_cases['docket'] == docket_number]
        if case_row.empty:
            print(f"❌ No case found with docket: {docket_number}")
            return
        case_idx = case_row.index[0]
        case_row = case_row.iloc[0]
    elif case_id:
        case_row = df_cases[df_cases['id'] == case_id]
        if case_row.empty:
            print(f"❌ No case found with ID: {case_id}")
            return
        case_idx = case_row.index[0]
        case_row = case_row.iloc[0]
    else:
        print("❌ Please provide either docket_number or case_id")
        return
    
    print(f"📋 Case: {case_row['docket']} (ID: {case_row['id']})")
    
    # Get case directory
    case_dir = get_case_dir(case_row)
    print(f"📁 Case directory: {case_dir}")
    
    if not os.path.exists(case_dir):
        print(f"❌ Case directory does not exist: {case_dir}")
        return
    
    # Get images data
    images_data = case_row['images']
    if not images_data:
        print("ℹ️  No images data in database for this case")
        return
    
    # Track changes
    changes_made = False
    cleanup_stats = {
        'trademarks': {'removed_regular': 0, 'removed_certificates': 0},
        'patents': {'removed_regular': 0, 'removed_certificates': 0},
        'copyrights': {'removed_regular': 0, 'removed_certificates': 0}
    }
    
    # Clean up each IP type
    for ip_type in ['trademarks', 'patents', 'copyrights']:
        if ip_type not in images_data:
            continue
            
        images_to_remove = []
        
        for image_name, image_data in images_data[ip_type].items():
            should_remove = False
            missing_files = []
            
            # Check regular images (high/low)
            for res in ["high", "low"]:
                file_path = os.path.join(case_dir, "images", res, image_name)
                if not os.path.exists(file_path):
                    missing_files.append(f"{res}/{image_name}")
            
            # Check certificate images
            if "full_filename" in image_data:
                for cert_file in image_data["full_filename"]:
                    cert_path = os.path.join(case_dir, "images", "high", cert_file)
                    if not os.path.exists(cert_path):
                        missing_files.append(f"certificate: {cert_file}")
            
            # If any files are missing, mark for removal
            if missing_files:
                should_remove = True
                print(f"   🗑️  {ip_type.upper()}: {image_name} - missing: {', '.join(missing_files)}")
                
                if len(missing_files) >= 2:  # Both high and low missing
                    cleanup_stats[ip_type]['removed_regular'] += 1
                if any('certificate:' in f for f in missing_files):
                    cleanup_stats[ip_type]['removed_certificates'] += 1
            
            if should_remove:
                images_to_remove.append(image_name)
        
        # Remove orphaned entries
        if images_to_remove:
            changes_made = True
            if not dry_run:
                for image_name in images_to_remove:
                    del images_data[ip_type][image_name]
                print(f"   ✅ Removed {len(images_to_remove)} orphaned {ip_type} entries")
            else:
                print(f"   📝 Would remove {len(images_to_remove)} orphaned {ip_type} entries")
    
    # Update database if changes were made
    if changes_made and not dry_run:
        try:
            df_cases.at[case_idx, 'images'] = images_data
            insert_and_update_df_to_GZ_batch(df_cases.loc[[case_idx]], 'tb_case', 'id')
            print(f"💾 Database updated successfully")
        except Exception as e:
            print(f"❌ Error updating database: {e}")
            return
    
    # Print summary
    print(f"\n📊 CLEANUP SUMMARY:")
    total_removed = 0
    for ip_type, stats in cleanup_stats.items():
        if stats['removed_regular'] > 0 or stats['removed_certificates'] > 0:
            print(f"   {ip_type.upper()}: {stats['removed_regular']} regular, {stats['removed_certificates']} certificates")
            total_removed += stats['removed_regular'] + stats['removed_certificates']
    
    if total_removed == 0:
        print("   ✅ No orphaned entries found - database is clean!")
    else:
        action = "Would remove" if dry_run else "Removed"
        print(f"   🧹 {action} {total_removed} total orphaned entries")
        
        if dry_run:
            print(f"\n💡 To actually clean up, run with --no-dry-run")


def cleanup_all_cases(dry_run=True):
    """Clean up orphaned entries across all cases"""
    print(f"🧹 {'DRY RUN: ' if dry_run else ''}Cleaning up orphaned entries across all cases...")
    
    df_cases = get_table_from_GZ('tb_case')
    
    total_cases_processed = 0
    total_cases_with_changes = 0
    total_entries_removed = 0
    
    for idx, case_row in df_cases.iterrows():
        if not case_row['images']:
            continue
            
        total_cases_processed += 1
        case_dir = get_case_dir(case_row)
        
        if not os.path.exists(case_dir):
            continue
        
        # Check for orphaned entries
        images_data = case_row['images']
        changes_made = False
        
        for ip_type in ['trademarks', 'patents', 'copyrights']:
            if ip_type not in images_data:
                continue
                
            images_to_remove = []
            
            for image_name, image_data in images_data[ip_type].items():
                missing_files = []
                
                # Check regular images
                for res in ["high", "low"]:
                    file_path = os.path.join(case_dir, "images", res, image_name)
                    if not os.path.exists(file_path):
                        missing_files.append(f"{res}/{image_name}")
                
                # Check certificates
                if "full_filename" in image_data:
                    for cert_file in image_data["full_filename"]:
                        cert_path = os.path.join(case_dir, "images", "high", cert_file)
                        if not os.path.exists(cert_path):
                            missing_files.append(f"certificate: {cert_file}")
                
                if missing_files:
                    images_to_remove.append(image_name)
            
            if images_to_remove:
                changes_made = True
                total_entries_removed += len(images_to_remove)
                if not dry_run:
                    for image_name in images_to_remove:
                        del images_data[ip_type][image_name]
        
        if changes_made:
            total_cases_with_changes += 1
            if not dry_run:
                try:
                    df_cases.at[idx, 'images'] = images_data
                    insert_and_update_df_to_GZ_batch(df_cases.loc[[idx]], 'tb_case', 'id')
                except Exception as e:
                    print(f"❌ Error updating case {case_row['docket']}: {e}")
    
    print(f"\n📊 GLOBAL CLEANUP SUMMARY:")
    print(f"   • Cases processed: {total_cases_processed}")
    print(f"   • Cases with orphaned entries: {total_cases_with_changes}")
    print(f"   • Total orphaned entries: {total_entries_removed}")
    
    if total_entries_removed > 0:
        action = "Would remove" if dry_run else "Removed"
        print(f"   🧹 {action} {total_entries_removed} orphaned database entries")
        
        if dry_run:
            print(f"\n💡 To actually clean up, run with --no-dry-run")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Clean up orphaned database entries for missing image files")
    parser.add_argument("--docket", help="Case docket number (e.g., '6:25-cv-00274')")
    parser.add_argument("--case-id", type=int, help="Case ID from database")
    parser.add_argument("--all-cases", action="store_true", help="Clean up all cases")
    parser.add_argument("--no-dry-run", action="store_true", help="Actually make changes (default is dry run)")
    
    args = parser.parse_args()
    
    dry_run = not args.no_dry_run
    
    if args.all_cases:
        cleanup_all_cases(dry_run=dry_run)
    elif args.docket or args.case_id:
        cleanup_orphaned_entries(
            docket_number=args.docket,
            case_id=args.case_id,
            dry_run=dry_run
        )
    else:
        # Default: clean up the problematic case
        cleanup_orphaned_entries(docket_number='6:25-cv-00274', dry_run=dry_run)
