#!/usr/bin/env python3
"""
Utility script to diagnose missing image files for cases.
This helps understand why certificate images are missing and provides recommendations.
"""

import os
import sys
import json
from pathlib import Path

# Add the app directory to Python path
sys.path.append('/app')

from DatabaseManagement.ImportExport import get_table_from_GZ
from FileManagement.Tencent_COS import diagnose_missing_images, get_case_dir
from logdata import log_message


def diagnose_case_images(docket_number=None, case_id=None, show_existing=False):
    """
    Diagnose missing images for a specific case
    
    Args:
        docket_number: Case docket number (e.g., '6:25-cv-00274')
        case_id: Case ID from database
        show_existing: Whether to show existing files in output
    """
    print(f"🔍 Diagnosing missing images...")
    
    # Get case data
    df_cases = get_table_from_GZ('tb_case')
    
    if docket_number:
        case_row = df_cases[df_cases['docket'] == docket_number]
        if case_row.empty:
            print(f"❌ No case found with docket: {docket_number}")
            return
        case_row = case_row.iloc[0]
    elif case_id:
        case_row = df_cases[df_cases['id'] == case_id]
        if case_row.empty:
            print(f"❌ No case found with ID: {case_id}")
            return
        case_row = case_row.iloc[0]
    else:
        print("❌ Please provide either docket_number or case_id")
        return
    
    print(f"📋 Case: {case_row['docket']} (ID: {case_row['id']})")
    print(f"👤 Plaintiff ID: {case_row['plaintiff_id']}")
    
    # Get case directory
    case_dir = get_case_dir(case_row)
    print(f"📁 Case directory: {case_dir}")
    
    if not os.path.exists(case_dir):
        print(f"❌ Case directory does not exist: {case_dir}")
        return
    
    # Get images data
    images_data = case_row['images']
    if not images_data:
        print("ℹ️  No images data in database for this case")
        return
    
    # Run diagnosis
    report = diagnose_missing_images(case_dir, images_data, case_row['plaintiff_id'])
    
    # Display results
    print("\n" + "="*60)
    print("📊 DIAGNOSIS REPORT")
    print("="*60)
    
    if show_existing and report["existing_files"]:
        print(f"\n✅ EXISTING FILES ({len(report['existing_files'])}):")
        for file in sorted(report["existing_files"]):
            print(f"   📄 {file}")
    
    if report["missing_regular"]:
        print(f"\n⚠️  MISSING REGULAR IMAGES ({len(report['missing_regular'])}):")
        by_type = {}
        for item in report["missing_regular"]:
            ip_type = item["ip_type"]
            if ip_type not in by_type:
                by_type[ip_type] = {"high": [], "low": []}
            by_type[ip_type][item["resolution"]].append(item["file"])
        
        for ip_type, resolutions in by_type.items():
            print(f"   📂 {ip_type.upper()}:")
            if resolutions["high"]:
                print(f"      🔍 High resolution ({len(resolutions['high'])}): {', '.join(resolutions['high'][:3])}{'...' if len(resolutions['high']) > 3 else ''}")
            if resolutions["low"]:
                print(f"      🔍 Low resolution ({len(resolutions['low'])}): {', '.join(resolutions['low'][:3])}{'...' if len(resolutions['low']) > 3 else ''}")
    
    if report["missing_certificates"]:
        print(f"\n❌ MISSING CERTIFICATE IMAGES ({len(report['missing_certificates'])}):")
        by_type = {}
        for item in report["missing_certificates"]:
            ip_type = item["ip_type"]
            if ip_type not in by_type:
                by_type[ip_type] = []
            by_type[ip_type].append(item["file"])
        
        for ip_type, files in by_type.items():
            print(f"   📂 {ip_type.upper()} ({len(files)}):")
            for file in files[:5]:  # Show first 5
                print(f"      📄 {file}")
            if len(files) > 5:
                print(f"      ... and {len(files) - 5} more")
    
    if report["recommendations"]:
        print(f"\n💡 RECOMMENDATIONS:")
        for i, rec in enumerate(report["recommendations"], 1):
            print(f"   {i}. {rec}")
    
    # Summary statistics
    print(f"\n📈 SUMMARY:")
    print(f"   • Existing files: {len(report['existing_files'])}")
    print(f"   • Missing regular images: {len(report['missing_regular'])}")
    print(f"   • Missing certificates: {len(report['missing_certificates'])}")
    
    # Database statistics
    total_expected = 0
    for ip_type in images_data.keys():
        for image_name in images_data[ip_type].keys():
            total_expected += 2  # high + low
            if "full_filename" in images_data[ip_type][image_name]:
                total_expected += len(images_data[ip_type][image_name]["full_filename"])
    
    print(f"   • Total expected files: {total_expected}")
    print(f"   • Success rate: {len(report['existing_files'])/total_expected*100:.1f}%" if total_expected > 0 else "   • Success rate: N/A")


def show_case_images_summary():
    """Show summary of image issues across all cases"""
    print("🔍 Analyzing image issues across all cases...")
    
    df_cases = get_table_from_GZ('tb_case')
    
    total_cases = 0
    cases_with_images = 0
    cases_with_missing = 0
    
    for _, case_row in df_cases.iterrows():
        if not case_row['images']:
            continue
            
        total_cases += 1
        cases_with_images += 1
        
        case_dir = get_case_dir(case_row)
        if not os.path.exists(case_dir):
            continue
            
        report = diagnose_missing_images(case_dir, case_row['images'], case_row['plaintiff_id'])
        
        if report["missing_regular"] or report["missing_certificates"]:
            cases_with_missing += 1
    
    print(f"\n📊 SUMMARY ACROSS ALL CASES:")
    print(f"   • Total cases: {len(df_cases)}")
    print(f"   • Cases with image data: {cases_with_images}")
    print(f"   • Cases with missing files: {cases_with_missing}")
    print(f"   • Cases with complete files: {cases_with_images - cases_with_missing}")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Diagnose missing image files for cases")
    parser.add_argument("--docket", help="Case docket number (e.g., '6:25-cv-00274')")
    parser.add_argument("--case-id", type=int, help="Case ID from database")
    parser.add_argument("--show-existing", action="store_true", help="Show existing files in output")
    parser.add_argument("--summary", action="store_true", help="Show summary across all cases")
    
    args = parser.parse_args()
    
    if args.summary:
        show_case_images_summary()
    elif args.docket or args.case_id:
        diagnose_case_images(
            docket_number=args.docket,
            case_id=args.case_id,
            show_existing=args.show_existing
        )
    else:
        # Default: diagnose the problematic case
        diagnose_case_images(docket_number='6:25-cv-00274', show_existing=True)
