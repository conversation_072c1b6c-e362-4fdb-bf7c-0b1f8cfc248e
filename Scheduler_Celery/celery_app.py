from celery import Celery
from .config import CeleryConfig

def create_celery_app():
    """Factory function to create Celery app"""

    celery = Celery('case_scheduler')
    
    # Configure Celery from config class
    celery.config_from_object(CeleryConfig)
    
    # Include tasks module
    celery.autodiscover_tasks(['Scheduler_Celery'])
    
    return celery

# create the base celery app
celery_app = create_celery_app()


from app import app as flask_app  

class ContextTask(celery_app.Task):
    def __call__(self, *args, **kwargs):
        with flask_app.app_context():
            return self.run(*args, **kwargs)

# override the base Task
celery_app.Task = ContextTask