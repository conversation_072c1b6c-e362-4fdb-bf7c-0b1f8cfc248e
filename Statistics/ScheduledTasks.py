"""
Scheduled tasks for statistics collection.
"""

from datetime import datetime
from logdata import log_message
from DatabaseManagement.StatisticsDB import init_statistics_table, store_statistics_batch, cleanup_old_statistics
from Statistics.StatisticsCollector import collect_all_statistics

def daily_statistics_collection():
    """
    Daily task to collect and store statistics.
    This function is designed to be called by a scheduler (APScheduler, Celery, etc.)
    """
    try:
        log_message("Starting daily statistics collection task", level='INFO')
        
        # Ensure statistics table exists
        init_statistics_table()
        
        # Collect all statistics
        statistics_data = collect_all_statistics()
        
        # Store in database
        store_statistics_batch(statistics_data)
        
        # Clean up old data (keep 90 days)
        cleanup_old_statistics(days_to_keep=90)
        
        log_message(f"Daily statistics collection completed successfully. Collected {len(statistics_data)} metrics.", level='INFO')
        
        return True
        
    except Exception as e:
        log_message(f"Error in daily statistics collection: {str(e)}", level='ERROR')
        return False

def weekly_statistics_cleanup():
    """
    Weekly task to clean up old statistics data.
    """
    try:
        log_message("Starting weekly statistics cleanup task", level='INFO')
        
        # Clean up old data (keep 90 days)
        cleanup_old_statistics(days_to_keep=90)
        
        log_message("Weekly statistics cleanup completed successfully", level='INFO')
        
        return True
        
    except Exception as e:
        log_message(f"Error in weekly statistics cleanup: {str(e)}", level='ERROR')
        return False

def test_statistics_collection():
    """
    Test function for statistics collection.
    Can be used for manual testing or debugging.
    """
    try:
        log_message("Starting test statistics collection", level='INFO')
        
        # Ensure statistics table exists
        init_statistics_table()
        
        # Collect all statistics
        statistics_data = collect_all_statistics()
        
        # Store in database
        store_statistics_batch(statistics_data)
        
        log_message(f"Test statistics collection completed. Collected {len(statistics_data)} metrics.", level='INFO')
        
        # Print summary for debugging
        for stat in statistics_data:
            print(f"  {stat['metric_name']}: {stat['metric_value']}")
        
        return True
        
    except Exception as e:
        log_message(f"Error in test statistics collection: {str(e)}", level='ERROR')
        print(f"Error: {str(e)}")
        return False

if __name__ == "__main__":
    # Allow running this script directly for testing
    print("Running test statistics collection...")
    success = test_statistics_collection()
    if success:
        print("Test completed successfully!")
    else:
        print("Test failed!")
