<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Checks Report</title>
    <style>
        table {
            border-collapse: collapse;
            width: 100%;
        }
        th, td {
            border: 1px solid black;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .risk-low {
            color: green;
        }
        .risk-medium {
            color: orange;
        }
        .risk-high {
            color: red;
        }
    </style>
</head>
<body>
    <h1>Checks Report</h1>
    <table>
        <thead>
            <tr>
                <th>Check ID</th>
                <th>User ID</th>
                <th>Product Category</th>
                <th>Main Product Image</th>
                <th>Other Product Images</th>
                <th>IP Images</th>
                <th>Keywords</th>
                <th>Product Description</th>
                <th>Reference Source</th>
                <th>Reference Images</th>
                <th>Risk Level</th>
                <th>Results</th>
                <th>Check Logs</th>
            </tr>
        </thead>
        <tbody>
            {% for item in data %}
            <tr>
                <td style="font-size: smaller; word-wrap: break-word; max-width: 100px;">
                    {{ item.id }}
                </td>
                <td style="font-size: smaller; word-wrap: break-word; max-width: 100px;">
                    {{ item.user_id }}
                </td>
                <td>{{ item.product_category }}</td>
                <td>
                    {% if item.product_images %}
                        <img src={{ item.product_images }} alt="Main Product Image" width="100">
                    {% else %}
                        N/A
                    {% endif %}
                </td>
                <td>
                    {% if item.other_product_images %}
                        {% for img_url in item.other_product_images.split(',') %}
                            <img src={{ img_url }} alt="Other Product Image" width="100">
                        {% endfor %}
                    {% else %}
                        N/A
                    {% endif %}
                </td>
                <td>
                    {% if item.images %}
                        {% for img_url in item.images.split(',') %}
                            <img src={{ img_url }} alt="IP Image" width="100">
                        {% endfor %}
                    {% else %}
                        N/A
                    {% endif %}
                </td>
                <td>{{ item.keyword }}</td>
                <td>{{ item.product_describe }}</td>
                <td>{{ item.reference_source }}</td>
                <td>
                    {% if item.reference_images %}
                        {% for img_url in item.reference_images.split(',') %}
                            <img src={{ img_url }} alt="Reference Image" width="100">
                        {% endfor %}
                    {% else %}
                        N/A
                    {% endif %}
                </td>
                {% if item.result %}
                    <td class="risk-{{ item.result.risk_level|lower }}">
                        {{ item.result.risk_level }}
                    </td>
                    <td>
                        <ul>
                            {% for res in item.result.results %}
                                <li>{{ res.type }}: {{ res.ip_owner }} - {{ res.risk_description }}</li>
                            {% endfor %}
                        </ul>
                    </td>
                {% else %}
                    <td>N/A</td>
                    <td>N/A</td>
                {% endif %}
                <td><a href="{{ url_for('view_check_logs', check_id=item.id) }}" target="_blank">View Logs</a></td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</body>
</html>