{% extends 'layout.html' %}

{% block content %}
<h1>Check Logs for Check ID: {{ check_id }}</h1>
<div id="checkLogsContainer">
    <div id="checkLogs"></div>
</div>

<script>
    const checkId = "{{ check_id }}";

    function fetchCheckLogs(checkId) {
        fetch(`/get_check_logs/${checkId}`)
            .then(response => response.json())
            .then(data => {
                const logsDiv = document.getElementById('checkLogs');
                logsDiv.innerHTML = '';
                if (data.logs.length === 0) {
                    // Display a message if no logs are found
                    logsDiv.innerHTML = '<p>No logs found for this check ID.</p>';
                } else {
                    data.logs.forEach(log => {
                        logsDiv.innerHTML += '<p>' + log.timestamp + ' - ' + log.level + ': ' + log.message + '</p>';
                    });
                }
            });
    }

    document.addEventListener('DOMContentLoaded', function() {
        fetchCheckLogs(checkId);
    });
</script>
{% endblock %} 