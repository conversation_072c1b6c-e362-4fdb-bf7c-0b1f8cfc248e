{% extends 'layout_ui.html' %}

{% block title %}TRO IP by Plaintiff{% endblock %}

{% block head %}
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='tro_ip/tro_ip.css') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
{% endblock %}

{% block content %}
    <div class="app-container">
        <div class="filters-container">
            <div class="filters-grid">
                <div class="filter-group">
                    <label for="picture_type">IP Type:</label>
                    <select id="picture_type">
                        <option value="">All</option>
                        <option value="trademarks">Trademark</option>
                        <option value="patents">Patent</option>
                        <option value="copyrights">Copyright</option>
                        <option value="no_ip">No IP</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="plaintiff_name">Plaintiff Name:</label>
                    <input type="text" id="plaintiff_name" placeholder="Enter plaintiff name">
                </div>
                <div class="filter-group">
                    <label for="plaintiff_id">Plaintiff ID:</label>
                    <input type="text" id="plaintiff_id" placeholder="Enter plaintiff ID">
                </div>
                <div class="filter-group">
                    <label for="ip_count_min">Min IP Count:</label>
                    <input type="number" id="ip_count_min" min="0" value="0">
                </div>
                <div class="filter-group">
                    <label for="ip_count_max">Max IP Count:</label>
                    <input type="number" id="ip_count_max" min="0">
                </div>
                <div class="filter-group">
                    <label for="sort_by">Sort By:</label>
                    <select id="sort_by">
                        <option value="ip_count">Number of IP</option>
                        <option value="plaintiff_name">Plaintiff Name</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="sort_order">Order:</label>
                    <select id="sort_order">
                        <option value="desc">Descending</option>
                        <option value="asc">Ascending</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="limit">Results:</label>
                    <select id="limit">
                        <option value="10">10</option>
                        <option value="20" selected>20</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                        <option value="200">200</option>
                    </select>
                </div>
            </div>

            <div class="actions-row">
                <div class="results-info">
                    <span id="total-results">0 plaintiffs</span>
                    (<span id="last-refresh">Last refresh: Never</span>)
                </div>
                <div class="action-buttons">
                    <button id="apply-filters" class="btn primary">Apply</button>
                    <button id="refresh-data" class="btn secondary">Refresh</button>
                </div>
            </div>
        </div>

        <div class="cases-container" id="cases-container"> {# Renamed from plaintiffs-ip-container #}
            <!-- Plaintiffs will be populated here dynamically -->
            <div class="loading">Loading plaintiffs IP data...</div>
        </div>

        <div class="pagination-controls">
            <button id="prev-page" class="btn secondary" disabled>Previous</button>
            <span id="page-info">Page 1 of 1</span>
            <button id="next-page" class="btn secondary" disabled>Next</button>
        </div>
    </div>
{% endblock %}

{% block scripts %}
    <script src="{{ url_for('static', filename='tro_ip/tro_ip.js') }}"></script>
    <script>
        // Add active class to menu item
        const menuItem = document.getElementById('menu-tro-ip');
        if (menuItem) {
            menuItem.classList.add('active');
        }
    </script>
{% endblock %}
