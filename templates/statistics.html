{% extends "layout_ui.html" %}

{% block title %}Database Statistics{% endblock %}

{% block head %}
    <link rel="stylesheet" href="{{ url_for('static', filename='statistics/statistics.css') }}">
    <script src="https://d3js.org/d3.v7.min.js"></script>
{% endblock %}

{% block content %}
<div class="statistics-container">
    <h1>Database Statistics</h1>

    {% if error %}
    <div class="error-message">
        <strong>Error:</strong> {{ error }}
    </div>
    {% endif %}

    <!-- Controls -->
    <div class="controls-section">
        <div class="button-group">
            <button id="refresh-btn" class="btn primary">Refresh Data</button>
            <button id="collect-btn" class="btn secondary">Collect New Statistics</button>
        </div>
        <div class="time-filter">
            <label for="days-filter">Show data for last:</label>
            <select id="days-filter">
                <option value="7">7 days</option>
                <option value="30" selected>30 days</option>
                <option value="90">90 days</option>
                <option value="365">1 year</option>
            </select>
        </div>
    </div>

    <!-- Loading indicator -->
    <div id="loading" class="loading" style="display: none;">
        <div class="spinner"></div>
        Loading statistics...
    </div>

    <!-- Statistics Grid -->
    <div id="statistics-grid" class="statistics-grid">
        <!-- Statistics cards will be populated here -->
    </div>

    <!-- Details Modal -->
    <div id="details-overlay" class="overlay">
        <div class="overlay-content">
            <div class="overlay-header">
                <h3 id="details-title" class="overlay-title">Metric Details</h3>
                <button class="close-overlay" onclick="closeDetailsModal()">&times;</button>
            </div>
            <div class="overlay-body">
                <div id="details-loading" class="loading" style="display: none;">
                    <div class="spinner"></div>
                    Loading details...
                </div>
                <div id="details-content">
                    <!-- Details content will be populated here -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
    <script src="{{ url_for('static', filename='statistics/statistics.js') }}"></script>
{% endblock %}