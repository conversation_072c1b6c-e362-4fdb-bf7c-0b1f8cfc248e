<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Reverse Check - API Studio</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='api_studio/api_studio.css') }}">
    <!-- Load modular JavaScript files -->
    <script src="{{ url_for('static', filename='api_studio/utils.js') }}"></script>
    <script src="{{ url_for('static', filename='api_studio/language.js') }}"></script>
    <script src="{{ url_for('static', filename='api_studio/reverse-check.js') }}"></script>
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}" type="image/x-icon">
</head>
<body>
    <header class="header">
        <div class="logo-title-container">
            <img src="static\images\maidalv_logo.png" alt="API Studio Logo" class="logo" style="height: 40px; margin-right: 15px;">
            <a href="https://www.maidalv.com" style="text-decoration: none; color: inherit;"><h1 class="studio-title">Maidalv API Studio - Reverse Check</h1></a>
        </div>
        <nav>
            <ul>
                <li><a href="/api_studio" data-i18n="check_page">Check</a></li>
                <li><a href="/api_studio_reverse_check" data-i18n="reverse_check_page">Reverse Check</a></li>
                <li>
                    <select id="languageSelect" style="padding: 5px; border-radius: 4px;">
                        <option value="en">English</option>
                        <option value="zh">中文</option>
                    </select>
                </li>
            </ul>
        </nav>
    </header>

    <div class="container main-content">
        <div class="left">
            <h2 data-i18n="reverse_check_results">Reverse Check Results</h2>
            
            <!-- API Key Input -->
            <div class="form-group">
                <label for="reverse_check_api_key" data-i18n="api_key">API Key:</label><span class="required-field">*</span>
                <input type="text" id="reverse_check_api_key" name="reverse_check_api_key" placeholder="Enter your API key">
            </div>

            <!-- Date Range Selection -->
            <div class="form-group">
                <label for="start_date" data-i18n="start_date">Start Date:</label><span class="required-field">*</span>
                <input type="date" id="start_date" name="start_date">
            </div>
            
            <div class="form-group">
                <label for="end_date" data-i18n="end_date">End Date:</label><span class="required-field">*</span>
                <input type="date" id="end_date" name="end_date">
            </div>

            <button type="button" id="fetchReverseCheckBtn" class="submit-btn" data-i18n="fetch_reverse_check">Fetch Reverse Check</button>

            <!-- Reverse Check List -->
            <div id="reverseCheckList" class="reverse-check-list" style="margin-top: 20px;">
                <h3 data-i18n="available_reverse_checks">Available Reverse Checks</h3>
                <div id="reverseCheckTree" class="reverse-check-tree">
                    <!-- Reverse checks will be populated here -->
                </div>
            </div>
        </div>

        <div class="right">
            <div class="output-header">
                <h2 data-i18n="reverse_check_details">Reverse Check Details</h2>
                <div class="controls">
                    <button id="toggleReportBtn" class="toggle-btn" data-i18n="hide_report">Hide Report</button>
                </div>
            </div>
            
            <div id="output" class="output">
                <p data-i18n="select_reverse_check">Select a reverse check from the left panel to view details</p>
            </div>
        </div>
    </div>

    <!-- Hidden input for language state -->
    <input type="hidden" id="language" value="en">

    <script>
        // Initialize the reverse check page
        document.addEventListener('DOMContentLoaded', function() {
            initializeReverseCheckPage();
        });
    </script>
</body>
</html>
