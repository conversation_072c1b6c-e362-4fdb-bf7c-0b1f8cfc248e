<!DOCTYPE html>
<html>
<head>
    <title>Product Matches</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .match-image { max-height: 200px; }
        .navigation { position: fixed; bottom: 20px; right: 20px; }
        table { width: 100% !important; }
        td { vertical-align: top; }
        /* New styles for wider layout */
        .container-fluid { max-width: 98%; }
        .product-sidebar { position: sticky; top: 20px; height: fit-content; }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <h2>Product #{{ product_id }}: {{ product['product_filename'] }}</h2>
        <div class="row">
            <div class="col-3 product-sidebar">
                <h4>Product Image</h4>
                <img src="{{ url_for('document_file', filename='AssessFiles/CopyrightTest/' + product['product_filename']) }}" 
                     class="img-fluid">
            </div>
            
            <div class="col-9">
                <form method="post">
                    <table class="table table-bordered">
                        <thead>
                            <tr>
                                <th style="width: 20%">Model</th>
                                <th style="width: 26.66%">Top 1 Match</th>
                                <th style="width: 26.66%">Top 2 Match</th>
                                <th style="width: 26.66%">Top 3 Match</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for model in product['models'] %}
                            {% set model_index = loop.index0 %}
                            <tr>
                                <td>{{ model['model_name'] }}</td>
                                {% for match in model['top_matches'] %}
                                <td>
                                    <img src="{{ url_for('data_file', filename='IP/copyrights/Production/' + match['filename']) }}" 
                                         class="match-image mb-2">
                                    <div>Score: {{ "%.2f"|format(match['score']) }}</div>
                                    <div>Plaintiff: {{ match['plaintiff_name'] or 'Unknown' }}</div>
                                    <input type="number" min="0" max="10" step="0.5" 
                                           name="qa_{{ model_index }}_{{ loop.index0 }}"
                                           value="{% if match['qualityAssessment'] is not none %}{{ match['qualityAssessment'] }}{% endif %}"
                                           class="form-control mt-2" placeholder="Score (0-10)">
                                </td>
                                {% endfor %}
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    
                    <div class="navigation">
                        <input type="submit" name="prev" value="Previous" class="btn btn-primary">
                        <input type="submit" name="next" value="Next" class="btn btn-primary">
                        <span class="ms-3">Product {{ product_id + 1 }} of {{ total_products }}</span>
                        <input type="number" name="id" value="{{ product_id }}" 
                               class="form-control d-inline-block ms-2" style="width: 100px"
                               min="0" max="{{ total_products - 1 }}">
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>
</html> 