{% extends 'layout_ui.html' %}

{% block title %}Review Case Plaintiff{% endblock %}

{% block head %}
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='plaintiff_review/review_plaintiff.css') }}">
{% endblock %}

{% block content %}
    <div class="app-container">
        <div class="filters-container">
            <div class="actions-row">
                <div class="action-buttons left-buttons">
                    <button id="add-9-and-len2" class="btn secondary">Add #9 and len=2</button>
                    <button id="delete-plaintiff-without-cases" class="btn secondary">Delete plaintiff without cases</button>
                </div>
                <div class="action-buttons right-buttons">
                    <span id="total-results">0 results</span>
                    <button id="refresh-data" class="btn secondary">Refresh</button>
                </div>
            </div>
        </div>

        <div class="table-container">
            <div class="loading">Loading plaintiff review data...</div>
            <table class="common-table" id="review-table" style="display: none;">
                <thead>
                    <tr>
                        <th>Case ID</th>
                        <th>Filed Date</th>
                        <th>Docket</th>
                        <th>Current Plaintiff</th>
                        <th>Plaintiff Names List</th>
                        <th>Proposed Name</th>
                        <th>Method Info</th>
                        <th>Updated</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody id="review-table-body">
                    <!-- Data will be populated here -->
                </tbody>
            </table>

            <div class="info-container" style="margin-top: 20px; text-align: center; color: #666;">
                <p>Use the individual Approve/Reject buttons for each review. Approve will show a case selection dialog.</p>
            </div>
        </div>
    </div>

    <!-- Overlay for delete confirmation -->
    <div id="delete-overlay" class="overlay">
        <div class="overlay-content">
            <div class="overlay-title">Plaintiffs to be deleted</div>
            <div class="overlay-list" id="plaintiffs-to-delete-list">
                <!-- List will be populated here -->
            </div>
            <div class="overlay-buttons">
                <button id="cancel-delete" class="btn secondary">Cancel</button>
                <button id="confirm-delete" class="btn primary">Confirm Delete</button>
            </div>
        </div>
    </div>

    </div>
{% endblock %}

{% block scripts %}
    <script src="{{ url_for('static', filename='plaintiff_review/review_plaintiff.js') }}"></script>
    <script>
        // Add active class to menu item
        const menuItem = document.getElementById('menu-review-plaintiff');
        if (menuItem) {
            menuItem.classList.add('active');
        }
    </script>
{% endblock %}